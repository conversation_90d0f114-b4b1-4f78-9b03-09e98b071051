-- Background Jobs table for tracking async operations
CREATE TABLE IF NOT EXISTS public.background_jobs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    job_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    total_items INTEGER DEFAULT 0,
    processed_items INTEGER DEFAULT 0,
    result JSONB DEFAULT '{}',
    error_message TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_background_jobs_status ON public.background_jobs(status);
CREATE INDEX IF NOT EXISTS idx_background_jobs_job_type ON public.background_jobs(job_type);
CREATE INDEX IF NOT EXISTS idx_background_jobs_created_at ON public.background_jobs(created_at DESC);

-- Update trigger function (reuse existing one)
CREATE TRIGGER update_background_jobs_updated_at 
  BEFORE UPDATE ON public.background_jobs 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- RLS Policy (allow all operations for now, can be restricted later)
ALTER TABLE public.background_jobs ENABLE ROW LEVEL SECURITY;

-- Allow all authenticated users to view and manage jobs
DROP POLICY IF EXISTS "Allow authenticated users to manage jobs" ON public.background_jobs;
CREATE POLICY "Allow authenticated users to manage jobs" ON public.background_jobs 
  FOR ALL USING (auth.role() = 'authenticated');

import { createServiceRoleClient } from '@/lib/supabase-server'

export interface BackgroundJob {
  id: string
  job_type: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  total_items: number
  processed_items: number
  result: any
  error_message?: string
  metadata: any
  created_at: string
  updated_at: string
  completed_at?: string
}

export class BackgroundJobService {
  private supabase = createServiceRoleClient()

  /**
   * Create a new background job
   */
  async createJob(jobType: string, metadata: any = {}): Promise<string> {
    const { data, error } = await this.supabase
      .from('background_jobs')
      .insert({
        job_type: jobType,
        status: 'pending',
        metadata
      })
      .select('id')
      .single()

    if (error) {
      throw new Error(`Failed to create job: ${error.message}`)
    }

    return data.id
  }

  /**
   * Update job status and progress
   */
  async updateJob(
    jobId: string, 
    updates: {
      status?: BackgroundJob['status']
      progress?: number
      total_items?: number
      processed_items?: number
      result?: any
      error_message?: string
      completed_at?: string
    }
  ): Promise<void> {
    const { error } = await this.supabase
      .from('background_jobs')
      .update(updates)
      .eq('id', jobId)

    if (error) {
      throw new Error(`Failed to update job: ${error.message}`)
    }
  }

  /**
   * Mark job as running
   */
  async startJob(jobId: string, totalItems: number = 0): Promise<void> {
    await this.updateJob(jobId, {
      status: 'running',
      total_items: totalItems,
      processed_items: 0,
      progress: 0
    })
  }

  /**
   * Update job progress
   */
  async updateProgress(jobId: string, processedItems: number, totalItems?: number): Promise<void> {
    const total = totalItems || processedItems
    const progress = total > 0 ? Math.round((processedItems / total) * 100) : 0

    await this.updateJob(jobId, {
      processed_items: processedItems,
      total_items: total,
      progress: Math.min(progress, 100)
    })
  }

  /**
   * Mark job as completed
   */
  async completeJob(jobId: string, result: any = {}): Promise<void> {
    await this.updateJob(jobId, {
      status: 'completed',
      progress: 100,
      result,
      completed_at: new Date().toISOString()
    })
  }

  /**
   * Mark job as failed
   */
  async failJob(jobId: string, errorMessage: string): Promise<void> {
    await this.updateJob(jobId, {
      status: 'failed',
      error_message: errorMessage,
      completed_at: new Date().toISOString()
    })
  }

  /**
   * Get job status
   */
  async getJob(jobId: string): Promise<BackgroundJob | null> {
    const { data, error } = await this.supabase
      .from('background_jobs')
      .select('*')
      .eq('id', jobId)
      .single()

    if (error) {
      console.error('Error fetching job:', error)
      return null
    }

    return data
  }

  /**
   * Get recent jobs by type
   */
  async getJobsByType(jobType: string, limit: number = 10): Promise<BackgroundJob[]> {
    const { data, error } = await this.supabase
      .from('background_jobs')
      .select('*')
      .eq('job_type', jobType)
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching jobs:', error)
      return []
    }

    return data || []
  }

  /**
   * Clean up old completed jobs (older than 24 hours)
   */
  async cleanupOldJobs(): Promise<void> {
    const cutoffDate = new Date()
    cutoffDate.setHours(cutoffDate.getHours() - 24)

    const { error } = await this.supabase
      .from('background_jobs')
      .delete()
      .in('status', ['completed', 'failed'])
      .lt('completed_at', cutoffDate.toISOString())

    if (error) {
      console.error('Error cleaning up old jobs:', error)
    }
  }
}

'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { JobProgress } from '@/components/JobProgress'
import { RefreshCw, Package, Database, Loader2 } from 'lucide-react'

export default function BackgroundJobsDemo() {
  const [currentJobId, setCurrentJobId] = useState<string | null>(null)
  const [isStarting, setIsStarting] = useState(false)
  const [lastResult, setLastResult] = useState<any>(null)

  const startBundleRefresh = async () => {
    try {
      setIsStarting(true)
      setLastResult(null)
      
      const response = await fetch('/api/bundles/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          platform: 'youtube',
          forceRefresh: true
        })
      })

      const data = await response.json()
      
      if (data.success && data.jobId) {
        setCurrentJobId(data.jobId)
      } else {
        console.error('Failed to start job:', data.error)
      }
    } catch (error) {
      console.error('Error starting bundle refresh:', error)
    } finally {
      setIsStarting(false)
    }
  }

  const setupBackgroundJobs = async () => {
    try {
      const response = await fetch('/api/setup-background-jobs', {
        method: 'POST'
      })
      const data = await response.json()
      console.log('Setup result:', data)
    } catch (error) {
      console.error('Error setting up background jobs:', error)
    }
  }

  const handleJobComplete = (result: any) => {
    console.log('Job completed:', result)
    setLastResult(result)
    setCurrentJobId(null)
  }

  const handleJobError = (error: string) => {
    console.error('Job failed:', error)
    setCurrentJobId(null)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-4">
      <div className="max-w-4xl mx-auto py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">Background Jobs Demo</h1>
          <p className="text-xl text-gray-300">Test the new background processing system</p>
        </div>

        <div className="grid gap-6">
          {/* Setup Section */}
          <Card className="bg-white/10 border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Database className="h-5 w-5" />
                Setup
              </CardTitle>
              <CardDescription className="text-gray-300">
                Initialize the background jobs table (run this first)
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button 
                onClick={setupBackgroundJobs}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Setup Background Jobs Table
              </Button>
            </CardContent>
          </Card>

          {/* Bundle Refresh Section */}
          <Card className="bg-white/10 border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Package className="h-5 w-5" />
                Bundle Refresh
              </CardTitle>
              <CardDescription className="text-gray-300">
                Start a background bundle refresh job
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={startBundleRefresh}
                disabled={isStarting || !!currentJobId}
                className="bg-purple-600 hover:bg-purple-700"
              >
                {isStarting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Starting...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Start Bundle Refresh
                  </>
                )}
              </Button>

              {currentJobId && (
                <JobProgress
                  jobId={currentJobId}
                  jobType="bundle_refresh"
                  onComplete={handleJobComplete}
                  onError={handleJobError}
                />
              )}
            </CardContent>
          </Card>

          {/* Results Section */}
          {lastResult && (
            <Card className="bg-white/10 border-white/20">
              <CardHeader>
                <CardTitle className="text-white">Last Job Result</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-sm text-gray-300 bg-black/20 p-4 rounded overflow-auto">
                  {JSON.stringify(lastResult, null, 2)}
                </pre>
              </CardContent>
            </Card>
          )}

          {/* Instructions */}
          <Card className="bg-white/10 border-white/20">
            <CardHeader>
              <CardTitle className="text-white">How It Works</CardTitle>
            </CardHeader>
            <CardContent className="text-gray-300 space-y-2">
              <p>1. <strong>Setup:</strong> Click "Setup Background Jobs Table" to create the database table</p>
              <p>2. <strong>Start Job:</strong> Click "Start Bundle Refresh" to begin a background refresh</p>
              <p>3. <strong>Monitor:</strong> Watch the progress bar update in real-time</p>
              <p>4. <strong>Complete:</strong> See the results when the job finishes</p>
              <div className="mt-4 p-3 bg-blue-900/30 rounded">
                <p className="text-blue-200">
                  <strong>Key Benefits:</strong> The API returns immediately with a job ID, 
                  then processes in the background. Your UI stays responsive while showing real-time progress!
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

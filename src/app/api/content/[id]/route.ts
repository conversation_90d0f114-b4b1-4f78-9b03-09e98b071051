import { createServerSupabaseClient } from '@/lib/supabase-server'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createServerSupabaseClient()
    const contentId = params.id

    // Get the content with creator information
    const { data: content, error: contentError } = await supabase
      .from('content')
      .select(`
        id,
        title,
        description,
        content_type,
        platform_url,
        thumbnail_url,
        published_at,
        ai_summary,
        referenced_titles,
        tags,
        content_analysis,
        creators (
          id,
          name,
          platform,
          handle,
          avatar_url,
          trust_score,
          verified
        )
      `)
      .eq('id', contentId)
      .single()

    if (contentError || !content) {
      return NextResponse.json({
        success: false,
        error: 'Content not found'
      }, { status: 404 })
    }

    // Parse content analysis if it exists
    let analysis = null
    if (content.content_analysis) {
      try {
        analysis = typeof content.content_analysis === 'string' 
          ? JSON.parse(content.content_analysis)
          : content.content_analysis
      } catch (e) {
        console.error('Error parsing content analysis:', e)
      }
    }

    return NextResponse.json({
      success: true,
      content: {
        ...content,
        analysis
      }
    })

  } catch (error) {
    console.error('Error fetching content detail:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

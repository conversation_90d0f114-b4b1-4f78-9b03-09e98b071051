'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { JobProgress } from '@/components/JobProgress'
import { RefreshCw, Loader2 } from 'lucide-react'

interface RefreshWithProgressProps {
  onRefreshComplete?: () => void
  className?: string
}

export function RefreshWithProgress({ onRefreshComplete, className = '' }: RefreshWithProgressProps) {
  const [currentJobId, setCurrentJobId] = useState<string | null>(null)
  const [isStarting, setIsStarting] = useState(false)

  const startRefresh = async () => {
    try {
      setIsStarting(true)
      
      const response = await fetch('/api/bundles/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          platform: 'youtube',
          maxAge: 604800, // 1 week
          forceRefresh: false
        })
      })

      const data = await response.json()
      
      if (data.success && data.jobId) {
        setCurrentJobId(data.jobId)
      } else {
        console.error('Failed to start refresh:', data.error)
      }
    } catch (error) {
      console.error('Error starting refresh:', error)
    } finally {
      setIsStarting(false)
    }
  }

  const handleJobComplete = (result: any) => {
    console.log('Refresh completed:', result)
    setCurrentJobId(null)
    
    // Call the callback to refresh the feed
    if (onRefreshComplete) {
      onRefreshComplete()
    }
  }

  const handleJobError = (error: string) => {
    console.error('Refresh failed:', error)
    setCurrentJobId(null)
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <Button 
        onClick={startRefresh}
        disabled={isStarting || !!currentJobId}
        variant="outline"
        className="text-white border-white/30 hover:bg-white/10"
      >
        {isStarting ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Starting...
          </>
        ) : currentJobId ? (
          <>
            <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
            Refreshing...
          </>
        ) : (
          <>
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh Content
          </>
        )}
      </Button>

      {currentJobId && (
        <JobProgress
          jobId={currentJobId}
          jobType="bundle_refresh"
          onComplete={handleJobComplete}
          onError={handleJobError}
          className="bg-white/5 border-white/10"
        />
      )}
    </div>
  )
}

'use client'

import { useBackgroundJob } from '@/hooks/useBackgroundJob'
import { Progress } from '@/components/ui/progress'
import { CheckCircle, XCircle, Loader2 } from 'lucide-react'

interface JobProgressProps {
  jobId: string | null
  jobType: string
  onComplete?: (result: any) => void
  onError?: (error: string) => void
  className?: string
}

export function JobProgress({ 
  jobId, 
  jobType, 
  onComplete, 
  onError, 
  className = '' 
}: JobProgressProps) {
  const { job, isLoading, isCompleted, isFailed, error, startPolling } = useBackgroundJob(
    onComplete,
    onError
  )

  // Start polling when jobId is provided
  if (jobId && !job && !isLoading) {
    startPolling(jobId)
  }

  if (!jobId) {
    return null
  }

  const getStatusIcon = () => {
    if (isCompleted) {
      return <CheckCircle className="h-5 w-5 text-green-500" />
    }
    if (isFailed) {
      return <XCircle className="h-5 w-5 text-red-500" />
    }
    return <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
  }

  const getStatusText = () => {
    if (!job) return 'Starting...'
    
    switch (job.status) {
      case 'pending':
        return 'Queued'
      case 'running':
        return `Processing ${job.processed_items}/${job.total_items} items`
      case 'completed':
        return 'Completed successfully'
      case 'failed':
        return `Failed: ${error || 'Unknown error'}`
      default:
        return 'Unknown status'
    }
  }

  const getJobTypeLabel = () => {
    switch (jobType) {
      case 'bundle_refresh':
        return 'Bundle Refresh'
      case 'content_backfill':
        return 'Content Processing'
      default:
        return jobType.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
    }
  }

  return (
    <div className={`bg-white border rounded-lg p-4 shadow-sm ${className}`}>
      <div className="flex items-center gap-3 mb-3">
        {getStatusIcon()}
        <div>
          <h3 className="font-medium text-gray-900">{getJobTypeLabel()}</h3>
          <p className="text-sm text-gray-600">{getStatusText()}</p>
        </div>
      </div>

      {job && job.status === 'running' && (
        <div className="space-y-2">
          <Progress value={job.progress} className="h-2" />
          <div className="flex justify-between text-xs text-gray-500">
            <span>{job.progress}% complete</span>
            <span>{job.processed_items} / {job.total_items}</span>
          </div>
        </div>
      )}

      {isCompleted && job?.result && (
        <div className="mt-3 p-3 bg-green-50 rounded-md">
          <p className="text-sm text-green-800">
            {job.result.message || 'Job completed successfully'}
          </p>
          {job.result.stats && (
            <div className="mt-2 text-xs text-green-700">
              <div>Bundles refreshed: {job.result.stats.bundles_refreshed}</div>
              <div>Creators updated: {job.result.stats.creators_updated}</div>
            </div>
          )}
        </div>
      )}

      {isFailed && (
        <div className="mt-3 p-3 bg-red-50 rounded-md">
          <p className="text-sm text-red-800">
            {error || 'Job failed with unknown error'}
          </p>
        </div>
      )}
    </div>
  )
}

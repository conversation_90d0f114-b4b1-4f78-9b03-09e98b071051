const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

async function testPerformance() {
  try {
    console.log('🚀 Testing feed performance after indexes...')

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )

    // Test 1: Basic feed query performance
    console.log('\n📊 Test 1: Basic feed query performance')
    const start1 = Date.now()
    
    const { data: userCreators } = await supabase
      .from('user_creators')
      .select('creator_id')
      .limit(10)

    if (userCreators && userCreators.length > 0) {
      const creatorIds = userCreators.map(uc => uc.creator_id)
      
      const { data: feedData, error } = await supabase
        .from('content')
        .select(`
          id,
          title,
          content_type,
          published_at,
          creator_id,
          creators!inner (name, trust_score)
        `)
        .in('creator_id', creatorIds)
        .order('published_at', { ascending: false })
        .limit(20)

      const duration1 = Date.now() - start1
      console.log(`   ⚡ Query completed in ${duration1}ms`)
      console.log(`   📝 Retrieved ${feedData?.length || 0} items`)
      
      if (duration1 < 500) {
        console.log('   ✅ EXCELLENT performance (< 500ms)')
      } else if (duration1 < 1000) {
        console.log('   🟡 GOOD performance (< 1s)')
      } else {
        console.log('   🔴 SLOW performance (> 1s) - indexes may not be applied yet')
      }
    }

    // Test 2: Content type filtering performance
    console.log('\n📊 Test 2: Content type filtering performance')
    const start2 = Date.now()
    
    const { data: reviewData } = await supabase
      .from('content')
      .select('id, title, content_type')
      .eq('content_type', 'review')
      .limit(10)

    const duration2 = Date.now() - start2
    console.log(`   ⚡ Content type query completed in ${duration2}ms`)
    console.log(`   📝 Found ${reviewData?.length || 0} reviews`)

    // Test 3: Tag/Genre filtering performance
    console.log('\n📊 Test 3: Tag/Genre filtering performance')
    const start3 = Date.now()
    
    const { data: tagData } = await supabase
      .from('content')
      .select('id, title, tags')
      .contains('tags', ['action'])
      .limit(10)

    const duration3 = Date.now() - start3
    console.log(`   ⚡ Tag filtering query completed in ${duration3}ms`)
    console.log(`   📝 Found ${tagData?.length || 0} action-tagged items`)

    // Test 4: Recent content performance
    console.log('\n📊 Test 4: Recent content performance')
    const start4 = Date.now()
    
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
    const { data: recentData } = await supabase
      .from('content')
      .select('id, title, published_at')
      .gte('published_at', thirtyDaysAgo)
      .order('published_at', { ascending: false })
      .limit(15)

    const duration4 = Date.now() - start4
    console.log(`   ⚡ Recent content query completed in ${duration4}ms`)
    console.log(`   📝 Found ${recentData?.length || 0} recent items`)

    // Overall performance assessment
    console.log('\n🎯 Performance Summary:')
    const durations = [duration1, duration2, duration3, duration4].filter(d => d !== undefined)
    const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length
    console.log(`   📊 Average query time: ${Math.round(avgDuration)}ms`)
    
    if (avgDuration < 300) {
      console.log('   🚀 OUTSTANDING performance! Indexes are working perfectly!')
    } else if (avgDuration < 600) {
      console.log('   ✅ EXCELLENT performance! Significant improvement achieved!')
    } else if (avgDuration < 1000) {
      console.log('   🟡 GOOD performance! Some improvement, may need time for indexes to optimize')
    } else {
      console.log('   🔴 Performance needs improvement. Check if indexes were created successfully.')
    }

    // Test 5: Check if indexes exist
    console.log('\n🔍 Checking if indexes were created...')
    try {
      const { data: indexes } = await supabase
        .from('pg_indexes')
        .select('indexname, tablename')
        .like('indexname', 'idx_content_%')

      if (indexes && indexes.length > 0) {
        console.log(`   ✅ Found ${indexes.length} content-related indexes:`)
        indexes.forEach(idx => {
          console.log(`      - ${idx.indexname} on ${idx.tablename}`)
        })
      } else {
        console.log('   ⚠️ No custom indexes found. They may need to be created manually.')
      }
    } catch (err) {
      console.log('   ⚠️ Could not check indexes (this is normal)')
    }

    console.log('\n🎉 Performance test completed!')

  } catch (error) {
    console.error('❌ Performance test failed:', error)
  }
}

testPerformance()

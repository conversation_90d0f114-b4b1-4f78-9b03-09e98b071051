// Test Reddit aggregation only (simplest working test)
async function testRedditOnly() {
  console.log('🧪 Testing Reddit-only aggregation...')

  try {
    // Test the Reddit aggregation endpoint
    console.log('\n📱 Testing Reddit aggregation...')
    const response = await fetch('http://localhost:3000/api/content/aggregate-alternative?sources=reddit', {
      method: 'POST'
    })
    
    if (!response.ok) {
      const errorText = await response.text()
      console.log(`❌ Reddit aggregation failed: ${response.status}`)
      console.log(`Error: ${errorText}`)
      return
    }

    const data = await response.json()
    
    console.log('✅ Reddit aggregation successful!')
    console.log(`📊 Content added: ${data.contentAdded}`)
    console.log(`⏱️ Duration: ${data.duration}ms`)
    console.log(`📈 Success rate: ${data.summary?.success_rate}`)
    
    if (data.sourceResults?.reddit) {
      const reddit = data.sourceResults.reddit
      console.log(`\n📱 Reddit Results:`)
      console.log(`   Content added: ${reddit.contentAdded}`)
      console.log(`   Errors: ${reddit.errors?.length || 0}`)
      
      if (reddit.subredditResults) {
        console.log(`   Subreddits processed:`)
        Object.entries(reddit.subredditResults).forEach(([subreddit, result]) => {
          console.log(`     r/${subreddit}: ${result.contentAdded} posts`)
        })
      }
    }
    
    if (data.errors && data.errors.length > 0) {
      console.log(`\n⚠️ Errors encountered: ${data.errors.length}`)
      data.errors.slice(0, 3).forEach((error, index) => {
        console.log(`   ${index + 1}. ${error}`)
      })
    }

    // Test the status endpoint
    console.log('\n📊 Testing status endpoint...')
    const statusResponse = await fetch('http://localhost:3000/api/content/aggregate-alternative')
    
    if (statusResponse.ok) {
      const statusData = await statusResponse.json()
      console.log('✅ Status endpoint working')
      console.log(`   Last run: ${statusData.lastRun || 'Never'}`)
      console.log(`   Content in last 24h: ${JSON.stringify(statusData.contentLast24Hours)}`)
      console.log(`   Total content: ${JSON.stringify(statusData.totalContent)}`)
    } else {
      console.log(`❌ Status endpoint failed: ${statusResponse.status}`)
    }

    console.log('\n🎉 Reddit test completed successfully!')
    console.log('\n📈 What this means:')
    console.log('   ✅ Reddit API is working and accessible')
    console.log('   ✅ Content filtering and classification works')
    console.log('   ✅ Database storage is working')
    console.log('   ✅ Your feed now has high-quality Reddit discussions!')
    console.log('\n🚀 Next steps:')
    console.log('   1. Check your feed to see the new Reddit content')
    console.log('   2. Test YouTube RSS aggregation')
    console.log('   3. Set up automated aggregation')

  } catch (error) {
    console.error('❌ Reddit test failed:', error)
    console.log('\n🔧 Troubleshooting:')
    console.log('   1. Make sure your Next.js app is running (npm run dev)')
    console.log('   2. Check that the database migration was applied')
    console.log('   3. Verify Supabase connection is working')
  }
}

testRedditOnly()

// Test Podcast RSS feed parsing
const { XMLParser } = require('fast-xml-parser')

async function testPodcastRSS() {
  console.log('🧪 Testing Podcast RSS feed parsing...')

  const xmlParser = new XMLParser({
    ignoreAttributes: false,
    attributeNamePrefix: '@_'
  })

  try {
    // Test 1: Fetch The Big Picture RSS feed
    console.log('\n🎙️ Test 1: Fetching The Big Picture RSS feed...')
    const rssUrl = 'https://feeds.simplecast.com/dHoohVNH'
    
    const response = await fetch(rssUrl, {
      headers: {
        'User-Agent': 'SceneSniffer/1.0 (Podcast Aggregator)'
      }
    })
    
    if (!response.ok) {
      throw new Error(`RSS fetch failed: ${response.status}`)
    }

    const xmlText = await response.text()
    console.log(`✅ Successfully fetched RSS feed (${xmlText.length} characters)`)

    // Test 2: Parse XML
    console.log('\n🔍 Test 2: Parsing XML data...')
    const parsedData = xmlParser.parse(xmlText)
    
    if (!parsedData.rss || !parsedData.rss.channel || !parsedData.rss.channel.item) {
      throw new Error('Invalid podcast RSS structure')
    }

    const episodes = Array.isArray(parsedData.rss.channel.item) 
      ? parsedData.rss.channel.item 
      : [parsedData.rss.channel.item]

    console.log(`✅ Successfully parsed ${episodes.length} episode entries`)
    console.log(`   Podcast: ${parsedData.rss.channel.title}`)
    console.log(`   Description: ${parsedData.rss.channel.description?.substring(0, 100)}...`)

    // Test 3: Extract episode data
    console.log('\n📄 Test 3: Extracting episode data...')
    if (episodes.length > 0) {
      const sampleEpisode = episodes[0]
      console.log('Sample episode data:')
      console.log(`   Title: ${sampleEpisode.title}`)
      console.log(`   Published: ${sampleEpisode.pubDate}`)
      console.log(`   Duration: ${sampleEpisode['itunes:duration'] || 'N/A'}`)
      console.log(`   GUID: ${sampleEpisode.guid}`)
      console.log(`   Link: ${sampleEpisode.link}`)
      console.log(`   Description: ${sampleEpisode.description?.substring(0, 150)}...`)
      
      if (sampleEpisode.enclosure) {
        console.log(`   Audio URL: ${sampleEpisode.enclosure['@_url']}`)
      }
    }

    // Test 4: Movie/TV content filtering
    console.log('\n🎬 Test 4: Filtering for movie/TV content...')
    const movieTVEpisodes = episodes.filter(episode => {
      const text = (episode.title + ' ' + (episode.description || '')).toLowerCase()
      const movieTVKeywords = [
        'movie', 'film', 'cinema', 'tv', 'series', 'show', 'episode',
        'netflix', 'disney', 'hbo', 'marvel', 'dc', 'trailer', 'review',
        'rewatch', 'draft', 'binge', 'streaming', 'premiere', 'finale'
      ]
      return movieTVKeywords.some(keyword => text.includes(keyword))
    })

    console.log(`✅ Found ${movieTVEpisodes.length} movie/TV episodes out of ${episodes.length} total`)

    // Test 5: Content classification
    console.log('\n🏷️ Test 5: Content classification...')
    const classifyContent = (title, description) => {
      const titleLower = title.toLowerCase()
      const descLower = (description || '').toLowerCase()
      
      if (titleLower.includes('review') || descLower.includes('review')) return 'review'
      if (titleLower.includes('draft') || descLower.includes('draft')) return 'recommendation'
      if (titleLower.includes('rewatch') || descLower.includes('rewatch')) return 'review'
      if (titleLower.includes('breakdown') || descLower.includes('analysis')) return 'breakdown'
      if (titleLower.includes('theory') || descLower.includes('explained')) return 'theory'
      
      return 'review' // Default for podcasts
    }

    movieTVEpisodes.slice(0, 5).forEach((episode, index) => {
      const contentType = classifyContent(episode.title, episode.description)
      console.log(`   ${index + 1}. "${episode.title.substring(0, 50)}..." → ${contentType}`)
    })

    // Test 6: Duration parsing
    console.log('\n⏱️ Test 6: Duration parsing...')
    const parseDuration = (duration) => {
      if (!duration) return null
      
      try {
        const parts = duration.split(':').map(Number)
        
        if (parts.length === 3) {
          return parts[0] * 3600 + parts[1] * 60 + parts[2]
        } else if (parts.length === 2) {
          return parts[0] * 60 + parts[1]
        } else if (parts.length === 1) {
          return parts[0]
        }
      } catch {
        return null
      }
      
      return null
    }

    episodes.slice(0, 3).forEach((episode, index) => {
      const duration = parseDuration(episode['itunes:duration'])
      const durationStr = duration 
        ? `${Math.floor(duration / 60)}m ${duration % 60}s` 
        : 'Unknown'
      console.log(`   ${index + 1}. "${episode.title.substring(0, 40)}..." → ${durationStr}`)
    })

    // Test 7: Test multiple podcast feeds
    console.log('\n📊 Test 7: Testing multiple podcast feeds...')
    const podcasts = [
      { name: 'The Big Picture', url: 'https://feeds.simplecast.com/dHoohVNH' },
      { name: 'The Rewatchables', url: 'https://rss.art19.com/the-rewatchables' },
      { name: 'The Watch', url: 'https://feeds.megaphone.fm/the-watch' }
    ]
    
    for (const podcast of podcasts) {
      try {
        const podcastResponse = await fetch(podcast.url, {
          headers: {
            'User-Agent': 'SceneSniffer/1.0 (Podcast Aggregator)'
          }
        })
        
        if (podcastResponse.ok) {
          const podcastXml = await podcastResponse.text()
          const podcastData = xmlParser.parse(podcastXml)
          const podcastEpisodes = Array.isArray(podcastData.rss.channel.item) 
            ? podcastData.rss.channel.item 
            : [podcastData.rss.channel.item]
          
          console.log(`   ✅ ${podcast.name}: ${podcastEpisodes.length} episodes`)
        } else {
          console.log(`   ❌ ${podcast.name}: Failed (${podcastResponse.status})`)
        }
        
        // Rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000))
      } catch (error) {
        console.log(`   ❌ ${podcast.name}: Error - ${error.message}`)
      }
    }

    console.log('\n🎉 Podcast RSS test completed successfully!')
    console.log('\n📊 Summary:')
    console.log(`   ✅ Podcast RSS feeds are accessible`)
    console.log(`   ✅ XML parsing works correctly`)
    console.log(`   ✅ Episode data extraction works`)
    console.log(`   ✅ Movie/TV content filtering works`)
    console.log(`   ✅ Content classification works`)
    console.log(`   ✅ Duration parsing works`)
    console.log(`   ✅ Multiple podcast feeds accessible`)
    console.log(`   🚀 Ready for full podcast aggregation!`)

  } catch (error) {
    console.error('❌ Podcast RSS test failed:', error)
  }
}

testPodcastRSS()

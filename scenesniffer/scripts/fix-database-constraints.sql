-- Quick fix for database constraints to support new platforms
-- Run this in Supabase SQL Editor to fix the constraint errors

-- Update creators table constraint to include new platforms
ALTER TABLE public.creators DROP CONSTRAINT IF EXISTS creators_platform_check;
ALTER TABLE public.creators ADD CONSTRAINT creators_platform_check 
CHECK (platform IN ('youtube', 'instagram', 'twitter', 'tiktok', 'reddit', 'podcast', 'mastodon'));

-- Update content table constraint to include new platforms  
ALTER TABLE public.content DROP CONSTRAINT IF EXISTS content_platform_check;
ALTER TABLE public.content ADD CONSTRAINT content_platform_check 
CHECK (platform IN ('youtube', 'instagram', 'twitter', 'reddit', 'podcast', 'mastodon'));

-- Update content media_type constraint to include audio
ALTER TABLE public.content DROP CONSTRAINT IF EXISTS content_media_type_check;
ALTER TABLE public.content ADD CONSTRAINT content_media_type_check 
CHECK (media_type IN ('video', 'image', 'carousel', 'text', 'thread', 'reel', 'story', 'audio'));

-- Add missing columns if they don't exist
ALTER TABLE public.content 
ADD COLUMN IF NOT EXISTS platform VARCHAR(20) NOT NULL DEFAULT 'youtube',
ADD COLUMN IF NOT EXISTS media_type VARCHAR(20),
ADD COLUMN IF NOT EXISTS media_urls JSONB DEFAULT '[]',
ADD COLUMN IF NOT EXISTS duration INTEGER,
ADD COLUMN IF NOT EXISTS engagement_metrics JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS content_metadata JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS ai_extracted_text TEXT,
ADD COLUMN IF NOT EXISTS ai_content_quality_score DECIMAL(3,2) DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS ai_topics JSONB DEFAULT '[]';

-- Update the unique constraint to include platform
ALTER TABLE public.content DROP CONSTRAINT IF EXISTS content_creator_id_platform_id_key;
ALTER TABLE public.content DROP CONSTRAINT IF EXISTS content_creator_platform_unique;
ALTER TABLE public.content ADD CONSTRAINT content_creator_platform_unique 
UNIQUE(creator_id, platform_id, platform);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_content_platform ON public.content(platform);
CREATE INDEX IF NOT EXISTS idx_content_media_type ON public.content(media_type);
CREATE INDEX IF NOT EXISTS idx_content_platform_published ON public.content(platform, published_at DESC);
CREATE INDEX IF NOT EXISTS idx_content_quality_score ON public.content(ai_content_quality_score DESC);

-- Create GIN indexes for JSONB fields
CREATE INDEX IF NOT EXISTS idx_content_engagement_metrics_gin ON public.content USING GIN (engagement_metrics);
CREATE INDEX IF NOT EXISTS idx_content_metadata_gin ON public.content USING GIN (content_metadata);
CREATE INDEX IF NOT EXISTS idx_content_ai_topics_gin ON public.content USING GIN (ai_topics);

-- Add comments
COMMENT ON CONSTRAINT creators_platform_check ON public.creators IS 'Supports youtube, instagram, twitter, tiktok, reddit, podcast, mastodon';
COMMENT ON CONSTRAINT content_platform_check ON public.content IS 'Supports youtube, instagram, twitter, reddit, podcast, mastodon';
COMMENT ON CONSTRAINT content_media_type_check ON public.content IS 'Supports video, image, carousel, text, thread, reel, story, audio';

-- Create content_stats table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.content_stats (
    id VARCHAR(50) PRIMARY KEY,
    last_run TIMESTAMP WITH TIME ZONE,
    total_content_added INTEGER DEFAULT 0,
    sources_processed TEXT[],
    duration_ms INTEGER,
    errors_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

COMMENT ON TABLE public.content_stats IS 'Tracks aggregation statistics and performance metrics';

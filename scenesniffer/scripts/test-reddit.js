// Test Reddit API integration
async function testRedditAPI() {
  console.log('🧪 Testing Reddit API integration...')

  try {
    // Test 1: Fetch r/movies posts
    console.log('\n📱 Test 1: Fetching r/movies posts...')
    const response = await fetch('https://www.reddit.com/r/movies/hot.json?limit=5', {
      headers: {
        'User-Agent': 'SceneSniffer/1.0 (Content Aggregator for Movie/TV Recommendations)'
      }
    })

    if (!response.ok) {
      throw new Error(`Reddit API error: ${response.status}`)
    }

    const data = await response.json()
    const posts = data.data.children.map(child => child.data)

    console.log(`✅ Successfully fetched ${posts.length} posts from r/movies`)
    
    // Show sample post
    if (posts.length > 0) {
      const samplePost = posts[0]
      console.log('\n📄 Sample post:')
      console.log(`   Title: ${samplePost.title}`)
      console.log(`   Author: u/${samplePost.author}`)
      console.log(`   Upvotes: ${samplePost.ups}`)
      console.log(`   Comments: ${samplePost.num_comments}`)
      console.log(`   URL: https://reddit.com${samplePost.permalink}`)
    }

    // Test 2: Check movie/TV content filtering
    console.log('\n🎬 Test 2: Filtering for movie/TV content...')
    const movieTVPosts = posts.filter(post => {
      const text = (post.title + ' ' + (post.selftext || '')).toLowerCase()
      const movieTVKeywords = [
        'movie', 'film', 'cinema', 'tv', 'series', 'show', 'episode',
        'netflix', 'disney', 'hbo', 'marvel', 'dc', 'actor', 'director'
      ]
      return movieTVKeywords.some(keyword => text.includes(keyword))
    })

    console.log(`✅ Found ${movieTVPosts.length} movie/TV related posts out of ${posts.length} total`)

    // Test 3: Test multiple subreddits
    console.log('\n📊 Test 3: Testing multiple subreddits...')
    const subreddits = ['movies', 'television', 'MovieDetails', 'netflix']
    
    for (const subreddit of subreddits) {
      try {
        const subResponse = await fetch(`https://www.reddit.com/r/${subreddit}/hot.json?limit=3`, {
          headers: {
            'User-Agent': 'SceneSniffer/1.0 (Content Aggregator)'
          }
        })
        
        if (subResponse.ok) {
          const subData = await subResponse.json()
          const subPosts = subData.data.children.length
          console.log(`   ✅ r/${subreddit}: ${subPosts} posts`)
        } else {
          console.log(`   ❌ r/${subreddit}: Failed (${subResponse.status})`)
        }
        
        // Rate limiting
        await new Promise(resolve => setTimeout(resolve, 500))
      } catch (error) {
        console.log(`   ❌ r/${subreddit}: Error - ${error.message}`)
      }
    }

    // Test 4: Content classification
    console.log('\n🏷️ Test 4: Content classification...')
    const classifyContent = (title, flair) => {
      const titleLower = title.toLowerCase()
      const flairLower = (flair || '').toLowerCase()
      
      if (flairLower.includes('discussion') || titleLower.includes('discussion')) return 'theory'
      if (flairLower.includes('review') || titleLower.includes('review')) return 'review'
      if (flairLower.includes('news') || titleLower.includes('news')) return 'news'
      if (titleLower.includes('recommend') || titleLower.includes('suggest')) return 'recommendation'
      if (titleLower.includes('theory') || titleLower.includes('explained')) return 'theory'
      
      return 'review'
    }

    movieTVPosts.forEach((post, index) => {
      const contentType = classifyContent(post.title, post.link_flair_text)
      console.log(`   ${index + 1}. "${post.title.substring(0, 50)}..." → ${contentType}`)
    })

    console.log('\n🎉 Reddit API test completed successfully!')
    console.log('\n📊 Summary:')
    console.log(`   ✅ Reddit API is accessible`)
    console.log(`   ✅ Content filtering works`)
    console.log(`   ✅ Multiple subreddits accessible`)
    console.log(`   ✅ Content classification works`)
    console.log(`   🚀 Ready for full aggregation!`)

  } catch (error) {
    console.error('❌ Reddit API test failed:', error)
  }
}

testRedditAPI()

// Test YouTube RSS feed parsing
const { XMLParser } = require('fast-xml-parser')

async function testYouTubeRSS() {
  console.log('🧪 Testing YouTube RSS feed parsing...')

  const xmlParser = new XMLParser({
    ignoreAttributes: false,
    attributeNamePrefix: '@_'
  })

  try {
    // Test 1: Fetch Emergency Awesome RSS feed
    console.log('\n📺 Test 1: Fetching Emergency Awesome RSS feed...')
    const channelId = 'UCq6VFHwMzcMXbuKyG7SQYIg' // Emergency Awesome
    const rssUrl = `https://www.youtube.com/feeds/videos.xml?channel_id=${channelId}`
    
    const response = await fetch(rssUrl)
    
    if (!response.ok) {
      throw new Error(`RSS fetch failed: ${response.status}`)
    }

    const xmlText = await response.text()
    console.log(`✅ Successfully fetched RSS feed (${xmlText.length} characters)`)

    // Test 2: Parse XML
    console.log('\n🔍 Test 2: Parsing XML data...')
    const parsedData = xmlParser.parse(xmlText)
    
    if (!parsedData.feed || !parsedData.feed.entry) {
      throw new Error('Invalid RSS structure')
    }

    const entries = Array.isArray(parsedData.feed.entry) 
      ? parsedData.feed.entry 
      : [parsedData.feed.entry]

    console.log(`✅ Successfully parsed ${entries.length} video entries`)

    // Test 3: Extract video data
    console.log('\n📄 Test 3: Extracting video data...')
    if (entries.length > 0) {
      const sampleVideo = entries[0]
      console.log('Sample video data:')
      console.log(`   Video ID: ${sampleVideo['yt:videoId']}`)
      console.log(`   Title: ${sampleVideo.title}`)
      console.log(`   Published: ${sampleVideo.published}`)
      console.log(`   URL: https://www.youtube.com/watch?v=${sampleVideo['yt:videoId']}`)
      
      if (sampleVideo['media:group']) {
        console.log(`   Description: ${sampleVideo['media:group']['media:description']?.substring(0, 100)}...`)
        console.log(`   Thumbnail: ${sampleVideo['media:group']['media:thumbnail']?.['@_url']}`)
      }
    }

    // Test 4: Content classification
    console.log('\n🏷️ Test 4: Content classification...')
    const classifyContent = (title, description) => {
      const text = (title + ' ' + (description || '')).toLowerCase()
      
      if (text.includes('review') || text.includes('reaction')) return 'review'
      if (text.includes('theory') || text.includes('explained') || text.includes('ending')) return 'theory'
      if (text.includes('breakdown') || text.includes('analysis')) return 'breakdown'
      if (text.includes('trailer') || text.includes('news') || text.includes('announced')) return 'news'
      if (text.includes('recommend') || text.includes('best') || text.includes('top')) return 'recommendation'
      
      return 'news' // Default for Emergency Awesome
    }

    entries.slice(0, 5).forEach((video, index) => {
      const description = video['media:group']?.['media:description'] || ''
      const contentType = classifyContent(video.title, description)
      console.log(`   ${index + 1}. "${video.title.substring(0, 50)}..." → ${contentType}`)
    })

    // Test 5: Test multiple channels
    console.log('\n📊 Test 5: Testing multiple YouTube channels...')
    const channels = [
      { id: 'UCq6VFHwMzcMXbuKyG7SQYIg', name: 'Emergency Awesome' },
      { id: 'UCKy1dAqELo0zrOtPkf0eTMw', name: 'IGN Movie Trailers' },
      { id: 'UCiDJtJKMICpb9B1qf7qjEOA', name: 'Screen Junkies' }
    ]
    
    for (const channel of channels) {
      try {
        const channelRssUrl = `https://www.youtube.com/feeds/videos.xml?channel_id=${channel.id}`
        const channelResponse = await fetch(channelRssUrl)
        
        if (channelResponse.ok) {
          const channelXml = await channelResponse.text()
          const channelData = xmlParser.parse(channelXml)
          const channelEntries = Array.isArray(channelData.feed.entry) 
            ? channelData.feed.entry 
            : [channelData.feed.entry]
          
          console.log(`   ✅ ${channel.name}: ${channelEntries.length} videos`)
        } else {
          console.log(`   ❌ ${channel.name}: Failed (${channelResponse.status})`)
        }
        
        // Rate limiting
        await new Promise(resolve => setTimeout(resolve, 500))
      } catch (error) {
        console.log(`   ❌ ${channel.name}: Error - ${error.message}`)
      }
    }

    // Test 6: Movie/TV content filtering
    console.log('\n🎬 Test 6: Filtering for movie/TV content...')
    const movieTVVideos = entries.filter(video => {
      const text = (video.title + ' ' + (video['media:group']?.['media:description'] || '')).toLowerCase()
      const movieTVKeywords = [
        'movie', 'film', 'cinema', 'tv', 'series', 'show', 'episode',
        'netflix', 'disney', 'hbo', 'marvel', 'dc', 'trailer', 'review'
      ]
      return movieTVKeywords.some(keyword => text.includes(keyword))
    })

    console.log(`✅ Found ${movieTVVideos.length} movie/TV videos out of ${entries.length} total`)

    console.log('\n🎉 YouTube RSS test completed successfully!')
    console.log('\n📊 Summary:')
    console.log(`   ✅ YouTube RSS feeds are accessible`)
    console.log(`   ✅ XML parsing works correctly`)
    console.log(`   ✅ Video data extraction works`)
    console.log(`   ✅ Content classification works`)
    console.log(`   ✅ Multiple channels accessible`)
    console.log(`   ✅ Movie/TV content filtering works`)
    console.log(`   🚀 Ready for full YouTube RSS aggregation!`)

  } catch (error) {
    console.error('❌ YouTube RSS test failed:', error)
  }
}

testYouTubeRSS()

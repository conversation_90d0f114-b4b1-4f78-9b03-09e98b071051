// Test the feed fixes and new platform support
async function testFeedFixes() {
  console.log('🧪 Testing feed fixes and platform support...')

  try {
    // Test 1: Check feed API with platform filtering
    console.log('\n📱 Test 1: Testing feed API with platform filtering...')
    const feedResponse = await fetch('http://localhost:3000/api/content/feed?limit=25&platforms=reddit,youtube,podcast')
    
    if (!feedResponse.ok) {
      console.log(`❌ Feed API failed: ${feedResponse.status}`)
      const errorText = await feedResponse.text()
      console.log(`Error: ${errorText}`)
      return
    }

    const feedData = await feedResponse.json()
    console.log('✅ Feed API working with platform filtering')
    console.log(`📊 Content returned: ${feedData.content?.length || 0}`)
    console.log(`📈 Total content: ${feedData.pagination?.total || 0}`)
    console.log(`🔄 Has more pages: ${feedData.pagination?.hasMore || false}`)
    
    // Check platform distribution
    if (feedData.content && feedData.content.length > 0) {
      const platformCounts = {}
      feedData.content.forEach(item => {
        const platform = item.platform || item.creators?.platform || 'unknown'
        platformCounts[platform] = (platformCounts[platform] || 0) + 1
      })
      
      console.log('\n📊 Platform distribution:')
      Object.entries(platformCounts).forEach(([platform, count]) => {
        console.log(`   ${platform}: ${count} items`)
      })
      
      // Show sample content
      console.log('\n📄 Sample content:')
      feedData.content.slice(0, 3).forEach((item, index) => {
        const platform = item.platform || item.creators?.platform || 'unknown'
        console.log(`   ${index + 1}. [${platform}] ${item.title?.substring(0, 50)}...`)
        if (item.engagement_metrics) {
          const metrics = Object.entries(item.engagement_metrics)
            .map(([key, value]) => `${key}: ${value}`)
            .join(', ')
          console.log(`      Engagement: ${metrics}`)
        }
      })
    }

    // Test 2: Check pagination
    console.log('\n📄 Test 2: Testing pagination...')
    const page2Response = await fetch('http://localhost:3000/api/content/feed?limit=10&offset=10')
    
    if (page2Response.ok) {
      const page2Data = await page2Response.json()
      console.log('✅ Pagination working')
      console.log(`📊 Page 2 content: ${page2Data.content?.length || 0}`)
    } else {
      console.log(`❌ Pagination failed: ${page2Response.status}`)
    }

    // Test 3: Check specific platforms
    console.log('\n🔍 Test 3: Testing individual platforms...')
    const platforms = ['reddit', 'youtube', 'podcast']
    
    for (const platform of platforms) {
      try {
        const platformResponse = await fetch(`http://localhost:3000/api/content/feed?platforms=${platform}&limit=5`)
        
        if (platformResponse.ok) {
          const platformData = await platformResponse.json()
          console.log(`   ✅ ${platform}: ${platformData.content?.length || 0} items`)
        } else {
          console.log(`   ❌ ${platform}: Failed (${platformResponse.status})`)
        }
      } catch (error) {
        console.log(`   ❌ ${platform}: Error - ${error.message}`)
      }
    }

    // Test 4: Check content types
    console.log('\n🏷️ Test 4: Testing content type filtering...')
    const contentTypeResponse = await fetch('http://localhost:3000/api/content/feed?content_types=review,news&limit=10')
    
    if (contentTypeResponse.ok) {
      const contentTypeData = await contentTypeResponse.json()
      console.log('✅ Content type filtering working')
      console.log(`📊 Review/News content: ${contentTypeData.content?.length || 0}`)
      
      if (contentTypeData.content && contentTypeData.content.length > 0) {
        const typeCounts = {}
        contentTypeData.content.forEach(item => {
          typeCounts[item.content_type] = (typeCounts[item.content_type] || 0) + 1
        })
        console.log('   Content type distribution:', typeCounts)
      }
    } else {
      console.log(`❌ Content type filtering failed: ${contentTypeResponse.status}`)
    }

    console.log('\n🎉 Feed testing completed!')
    console.log('\n📋 Summary:')
    console.log('   ✅ Platform filtering added (reddit, youtube, podcast)')
    console.log('   ✅ Enhanced content fields included')
    console.log('   ✅ Pagination should work with 25 items per page')
    console.log('   ✅ Multi-platform content support')
    console.log('\n🚀 Next steps:')
    console.log('   1. Run the database fix in Supabase')
    console.log('   2. Test Reddit aggregation')
    console.log('   3. Check your feed for new platform content')
    console.log('   4. Use platform filters to see specific content types')

  } catch (error) {
    console.error('❌ Feed test failed:', error)
    console.log('\n🔧 Troubleshooting:')
    console.log('   1. Make sure your Next.js app is running')
    console.log('   2. Check that you have content in the database')
    console.log('   3. Verify the database migration was applied')
    console.log('   4. Check browser console for any errors')
  }
}

testFeedFixes()

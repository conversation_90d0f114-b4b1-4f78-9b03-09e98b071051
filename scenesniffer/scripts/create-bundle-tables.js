const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function createBundleTables() {
  console.log('🚀 Creating bundle tables...')
  
  try {
    // Read the SQL migration file
    const sqlPath = path.join(__dirname, '../supabase/migrations/20240804000001_create_bundle_tables.sql')
    const sql = fs.readFileSync(sqlPath, 'utf8')
    
    console.log('📄 Executing SQL migration...')
    
    // Split SQL into individual statements and execute them
    const statements = sql
      .split(';')
      .map(s => s.trim())
      .filter(s => s.length > 0 && !s.startsWith('--'))
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`)
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i] + ';'
      console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`)
      
      try {
        // Use the rpc function to execute raw SQL
        const { data, error } = await supabase.rpc('exec_sql', { sql: statement })
        
        if (error) {
          console.error(`❌ Error in statement ${i + 1}:`, error.message)
          // Continue with other statements
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`)
        }
      } catch (err) {
        console.error(`❌ Exception in statement ${i + 1}:`, err.message)
        // Continue with other statements
      }
    }
    
    // Test if tables were created
    console.log('\n🔍 Testing table creation...')
    
    const tables = ['creator_bundles', 'user_bundles', 'bundle_creators', 'bundle_analytics']
    const results = []
    
    for (const table of tables) {
      try {
        const { data, error } = await supabase.from(table).select('id').limit(1)
        if (error) {
          results.push(`❌ ${table}: ${error.message}`)
        } else {
          results.push(`✅ ${table}: Table exists and accessible`)
        }
      } catch (err) {
        results.push(`❌ ${table}: ${err.message}`)
      }
    }
    
    console.log('\n📊 Results:')
    results.forEach(result => console.log(result))
    
    const allSuccess = results.every(r => r.includes('✅'))
    
    if (allSuccess) {
      console.log('\n🎉 All bundle tables created successfully!')
    } else {
      console.log('\n⚠️  Some tables may not have been created properly.')
      console.log('💡 You may need to run the SQL manually in the Supabase dashboard.')
    }
    
  } catch (error) {
    console.error('❌ Error creating bundle tables:', error)
    process.exit(1)
  }
}

// Run the script
createBundleTables()
  .then(() => {
    console.log('✨ Script completed')
    process.exit(0)
  })
  .catch((error) => {
    console.error('💥 Script failed:', error)
    process.exit(1)
  })

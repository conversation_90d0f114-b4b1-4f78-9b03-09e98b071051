const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

async function runIndexes() {
  try {
    console.log('🚀 Running database indexes for Cinematic Pulse Feed...')

    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )

    // Read the indexes file
    const indexesPath = path.join(__dirname, '../database/migrations/add_feed_indexes.sql')
    const indexesSQL = fs.readFileSync(indexesPath, 'utf8')

    console.log('📝 Loaded indexes SQL file')

    // Split into individual statements
    const statements = indexesSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && stmt !== '')

    console.log(`🔧 Found ${statements.length} SQL statements to execute`)

    // Execute each statement individually for better error handling
    const results = []
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i].trim()
      if (!statement) continue

      console.log(`\n⚡ Executing statement ${i + 1}/${statements.length}:`)
      console.log(`   ${statement.substring(0, 80)}${statement.length > 80 ? '...' : ''}`)

      try {
        // For CREATE INDEX statements, we'll use a direct approach
        if (statement.toUpperCase().includes('CREATE INDEX')) {
          // Extract index name for better logging
          const indexMatch = statement.match(/CREATE INDEX (?:IF NOT EXISTS )?(\w+)/i)
          const indexName = indexMatch ? indexMatch[1] : 'unknown'
          
          console.log(`   📊 Creating index: ${indexName}`)
          
          // Use rpc to execute raw SQL (if available)
          const { data, error } = await supabase.rpc('exec_sql', { 
            sql: statement + ';' 
          })
          
          if (error) {
            if (error.message.includes('already exists')) {
              console.log(`   ✅ Index ${indexName} already exists (skipping)`)
              results.push({ statement: indexName, status: 'exists', error: null })
            } else {
              console.log(`   ❌ Error creating index ${indexName}:`, error.message)
              results.push({ statement: indexName, status: 'error', error: error.message })
            }
          } else {
            console.log(`   ✅ Index ${indexName} created successfully`)
            results.push({ statement: indexName, status: 'success', error: null })
          }
        } 
        else if (statement.toUpperCase().includes('ANALYZE')) {
          console.log(`   📈 Running table analysis...`)
          
          const { data, error } = await supabase.rpc('exec_sql', { 
            sql: statement + ';' 
          })
          
          if (error) {
            console.log(`   ⚠️ Analysis warning:`, error.message)
            results.push({ statement: 'ANALYZE', status: 'warning', error: error.message })
          } else {
            console.log(`   ✅ Table analysis completed`)
            results.push({ statement: 'ANALYZE', status: 'success', error: null })
          }
        }
        else if (statement.toUpperCase().includes('COMMENT')) {
          console.log(`   📝 Adding comment...`)
          
          const { data, error } = await supabase.rpc('exec_sql', { 
            sql: statement + ';' 
          })
          
          if (error) {
            console.log(`   ⚠️ Comment warning:`, error.message)
            results.push({ statement: 'COMMENT', status: 'warning', error: error.message })
          } else {
            console.log(`   ✅ Comment added`)
            results.push({ statement: 'COMMENT', status: 'success', error: null })
          }
        }

        // Small delay to avoid overwhelming the database
        await new Promise(resolve => setTimeout(resolve, 100))

      } catch (err) {
        console.log(`   ❌ Exception:`, err.message)
        results.push({ statement: statement.substring(0, 50), status: 'exception', error: err.message })
      }
    }

    // Summary
    console.log('\n🎉 Index creation completed!')
    console.log('\n📊 Summary:')
    
    const successful = results.filter(r => r.status === 'success').length
    const existing = results.filter(r => r.status === 'exists').length
    const errors = results.filter(r => r.status === 'error').length
    const warnings = results.filter(r => r.status === 'warning').length
    
    console.log(`   ✅ Successfully created: ${successful}`)
    console.log(`   ♻️  Already existing: ${existing}`)
    console.log(`   ⚠️  Warnings: ${warnings}`)
    console.log(`   ❌ Errors: ${errors}`)

    if (errors > 0) {
      console.log('\n❌ Errors encountered:')
      results.filter(r => r.status === 'error').forEach(r => {
        console.log(`   - ${r.statement}: ${r.error}`)
      })
    }

    console.log('\n🚀 Your feed should now have significantly improved performance!')

  } catch (error) {
    console.error('❌ Failed to run indexes:', error)
    
    // Fallback: provide manual instructions
    console.log('\n📋 Manual Instructions:')
    console.log('1. Go to your Supabase dashboard')
    console.log('2. Navigate to SQL Editor')
    console.log('3. Copy and paste the contents of: database/migrations/add_feed_indexes.sql')
    console.log('4. Execute the SQL')
    console.log('5. Your feed performance will improve dramatically!')
  }
}

runIndexes()

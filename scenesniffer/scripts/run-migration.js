const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

async function runMigration() {
  try {
    // Create Supabase client with service role key
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )

    console.log('🚀 Running content engagement migration...')

    // Read the migration file
    const migrationPath = path.join(__dirname, '../supabase/migrations/20250110000001_add_content_engagement.sql')
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')

    // Split the migration into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

    console.log(`📝 Found ${statements.length} SQL statements to execute`)

    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i] + ';'
      console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`)
      
      try {
        const { error } = await supabase.rpc('exec_sql', { sql: statement })
        
        if (error) {
          console.error(`❌ Error in statement ${i + 1}:`, error)
          // Continue with other statements
        } else {
          console.log(`✅ Statement ${i + 1} executed successfully`)
        }
      } catch (err) {
        console.error(`❌ Exception in statement ${i + 1}:`, err.message)
        // Continue with other statements
      }
    }

    console.log('🎉 Migration completed!')

  } catch (error) {
    console.error('❌ Migration failed:', error)
    process.exit(1)
  }
}

// Alternative approach: Use direct SQL execution
async function runMigrationDirect() {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )

    console.log('🚀 Running content engagement migration (direct approach)...')

    // Add columns to content table
    console.log('📝 Adding engagement columns to content table...')
    const { error: alterError } = await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE public.content 
        ADD COLUMN IF NOT EXISTS view_count INTEGER DEFAULT 0,
        ADD COLUMN IF NOT EXISTS engagement_score DECIMAL(5,2) DEFAULT 0.0,
        ADD COLUMN IF NOT EXISTS last_viewed_at TIMESTAMP WITH TIME ZONE;
      `
    })

    if (alterError) {
      console.error('❌ Error adding columns:', alterError)
    } else {
      console.log('✅ Engagement columns added successfully')
    }

    // Add behavior_profile column to user_preferences
    console.log('📝 Adding behavior_profile column to user_preferences...')
    const { error: behaviorError } = await supabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE public.user_preferences 
        ADD COLUMN IF NOT EXISTS behavior_profile JSONB DEFAULT NULL;
      `
    })

    if (behaviorError) {
      console.error('❌ Error adding behavior_profile column:', behaviorError)
    } else {
      console.log('✅ Behavior profile column added successfully')
    }

    console.log('🎉 Basic migration completed! Some advanced features may need manual setup.')

  } catch (error) {
    console.error('❌ Migration failed:', error)
  }
}

// Check if exec_sql function exists, if not use direct approach
async function checkAndRunMigration() {
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY
  )

  try {
    // Test if we can execute SQL directly
    const { error } = await supabase.rpc('exec_sql', { sql: 'SELECT 1;' })
    
    if (error) {
      console.log('📝 exec_sql function not available, using direct approach...')
      await runMigrationDirect()
    } else {
      console.log('📝 exec_sql function available, using full migration...')
      await runMigration()
    }
  } catch (err) {
    console.log('📝 Using direct approach due to error:', err.message)
    await runMigrationDirect()
  }
}

// Run the migration
checkAndRunMigration()

// Test the full alternative aggregation system
async function testFullAggregation() {
  console.log('🚀 Testing full alternative content aggregation...')

  try {
    // Test 1: Check aggregation status
    console.log('\n📊 Test 1: Checking aggregation status...')
    const statusResponse = await fetch('http://localhost:3000/api/content/aggregate-alternative')
    
    if (statusResponse.ok) {
      const statusData = await statusResponse.json()
      console.log('✅ Aggregation status endpoint working')
      console.log(`   Available sources: ${statusData.availableSources?.join(', ')}`)
      console.log(`   Last run: ${statusData.lastRun || 'Never'}`)
      console.log(`   Total content: ${JSON.stringify(statusData.totalContent)}`)
    } else {
      console.log(`❌ Status check failed: ${statusResponse.status}`)
    }

    // Test 2: Test Reddit-only aggregation
    console.log('\n📱 Test 2: Testing Reddit-only aggregation...')
    const redditResponse = await fetch('http://localhost:3000/api/content/aggregate-alternative?sources=reddit', {
      method: 'POST'
    })
    
    if (redditResponse.ok) {
      const redditData = await redditResponse.json()
      console.log('✅ Reddit aggregation working')
      console.log(`   Content added: ${redditData.contentAdded}`)
      console.log(`   Duration: ${redditData.duration}ms`)
      console.log(`   Success rate: ${redditData.summary?.success_rate}`)
      
      if (redditData.sourceResults?.reddit) {
        const reddit = redditData.sourceResults.reddit
        console.log(`   Subreddits processed: ${Object.keys(reddit.subredditResults || {}).length}`)
        console.log(`   Errors: ${reddit.errors?.length || 0}`)
      }
    } else {
      console.log(`❌ Reddit aggregation failed: ${redditResponse.status}`)
      const errorText = await redditResponse.text()
      console.log(`   Error: ${errorText}`)
    }

    // Test 3: Test YouTube RSS-only aggregation
    console.log('\n📺 Test 3: Testing YouTube RSS-only aggregation...')
    const youtubeResponse = await fetch('http://localhost:3000/api/content/aggregate-alternative?sources=youtube_rss', {
      method: 'POST'
    })
    
    if (youtubeResponse.ok) {
      const youtubeData = await youtubeResponse.json()
      console.log('✅ YouTube RSS aggregation working')
      console.log(`   Content added: ${youtubeData.contentAdded}`)
      console.log(`   Duration: ${youtubeData.duration}ms`)
      console.log(`   Success rate: ${youtubeData.summary?.success_rate}`)
      
      if (youtubeData.sourceResults?.youtube_rss) {
        const youtube = youtubeData.sourceResults.youtube_rss
        console.log(`   Channels processed: ${Object.keys(youtube.channelResults || {}).length}`)
        console.log(`   Errors: ${youtube.errors?.length || 0}`)
      }
    } else {
      console.log(`❌ YouTube RSS aggregation failed: ${youtubeResponse.status}`)
      const errorText = await youtubeResponse.text()
      console.log(`   Error: ${errorText}`)
    }

    // Test 4: Test Podcast-only aggregation
    console.log('\n🎙️ Test 4: Testing Podcast-only aggregation...')
    const podcastResponse = await fetch('http://localhost:3000/api/content/aggregate-alternative?sources=podcast', {
      method: 'POST'
    })
    
    if (podcastResponse.ok) {
      const podcastData = await podcastResponse.json()
      console.log('✅ Podcast aggregation working')
      console.log(`   Content added: ${podcastData.contentAdded}`)
      console.log(`   Duration: ${podcastData.duration}ms`)
      console.log(`   Success rate: ${podcastData.summary?.success_rate}`)
      
      if (podcastData.sourceResults?.podcast) {
        const podcast = podcastData.sourceResults.podcast
        console.log(`   Podcasts processed: ${Object.keys(podcast.podcastResults || {}).length}`)
        console.log(`   Errors: ${podcast.errors?.length || 0}`)
      }
    } else {
      console.log(`❌ Podcast aggregation failed: ${podcastResponse.status}`)
      const errorText = await podcastResponse.text()
      console.log(`   Error: ${errorText}`)
    }

    // Test 5: Test full aggregation (all sources)
    console.log('\n🌟 Test 5: Testing full aggregation (all sources)...')
    const fullResponse = await fetch('http://localhost:3000/api/content/aggregate-alternative', {
      method: 'POST'
    })
    
    if (fullResponse.ok) {
      const fullData = await fullResponse.json()
      console.log('✅ Full aggregation working')
      console.log(`   Total content added: ${fullData.contentAdded}`)
      console.log(`   Duration: ${fullData.duration}ms`)
      console.log(`   Success rate: ${fullData.summary?.success_rate}`)
      console.log(`   Sources: ${fullData.summary?.sources?.join(', ')}`)
      
      console.log('\n📊 Breakdown by source:')
      console.log(`   Reddit: ${fullData.summary?.reddit || 0} items`)
      console.log(`   YouTube RSS: ${fullData.summary?.youtube_rss || 0} items`)
      console.log(`   Podcast: ${fullData.summary?.podcast || 0} items`)
      
      if (fullData.errors && fullData.errors.length > 0) {
        console.log(`\n⚠️ Errors encountered: ${fullData.errors.length}`)
        fullData.errors.slice(0, 3).forEach((error, index) => {
          console.log(`   ${index + 1}. ${error}`)
        })
      }
    } else {
      console.log(`❌ Full aggregation failed: ${fullResponse.status}`)
      const errorText = await fullResponse.text()
      console.log(`   Error: ${errorText}`)
    }

    console.log('\n🎉 Alternative aggregation test completed!')
    console.log('\n📈 Expected Results:')
    console.log('   📱 Reddit: 200-500 high-quality discussions per run')
    console.log('   📺 YouTube RSS: 50-100 videos per run (no API limits)')
    console.log('   🎙️ Podcast: 20-50 professional episodes per run')
    console.log('   🚀 Total: 270-650 pieces of content per run')
    console.log('   💰 Cost: $0 (completely free!)')
    console.log('   🔄 Can run every 15-30 minutes without restrictions')

  } catch (error) {
    console.error('❌ Full aggregation test failed:', error)
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testFullAggregation()
}

module.exports = { testFullAggregation }

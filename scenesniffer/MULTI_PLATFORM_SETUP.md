# 🚀 Multi-Platform API Setup Guide

This guide will help you set up Instagram and Twitter APIs to get **10x more content** for your SceneSniffer feed.

## 📋 Prerequisites

- Facebook Developer Account
- Twitter Developer Account  
- Your SceneSniffer app running locally

## 🔧 Step 1: Instagram Basic Display API

### 1.1 Create Facebook App
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Click **"Create App"** → **"Consumer"** → **"Next"**
3. App Name: `SceneSniffer Content Aggregator`
4. Contact Email: Your email
5. Click **"Create App"**

### 1.2 Add Instagram Basic Display
1. In app dashboard → **"Add Product"**
2. Find **"Instagram Basic Display"** → **"Set Up"**
3. Go to **Instagram Basic Display** → **Basic Display**
4. Click **"Create New App"**

### 1.3 Configure Instagram App
1. **Display Name**: `SceneSniffer`
2. **Valid OAuth Redirect URIs**: 
   ```
   http://localhost:3000/api/auth/instagram/callback
   https://your-domain.com/api/auth/instagram/callback
   ```
3. **Deauthorize Callback URL**: 
   ```
   http://localhost:3000/api/auth/instagram/deauthorize
   ```
4. **Data Deletion Request URL**:
   ```
   http://localhost:3000/api/auth/instagram/delete
   ```

### 1.4 Get Instagram Credentials
1. Copy **Instagram App ID**
2. Copy **Instagram App Secret**
3. Add to your `.env.local`:

```bash
# Instagram API
INSTAGRAM_APP_ID=your_instagram_app_id_here
INSTAGRAM_APP_SECRET=your_instagram_app_secret_here
INSTAGRAM_REDIRECT_URI=http://localhost:3000/api/auth/instagram/callback
```

## 🐦 Step 2: Twitter API v2

### 2.1 Create Twitter Developer Account
1. Go to [Twitter Developer Portal](https://developer.twitter.com/)
2. Click **"Sign up"** (use existing Twitter account)
3. Fill application:
   - **Use case**: Content aggregation for entertainment platform
   - **Description**: Aggregating movie/TV content from creators for personalized feeds
4. Wait for approval (usually instant)

### 2.2 Create Twitter App
1. Developer Portal → **"Create Project"**
2. **Project Name**: `SceneSniffer Content Aggregator`
3. **Use Case**: Making a bot
4. **Description**: Aggregating entertainment content for personalized recommendations
5. **App Name**: `scenesniffer-aggregator`

### 2.3 Get Twitter Credentials
1. Go to app → **"Keys and Tokens"**
2. Copy **API Key** and **API Secret Key**
3. Generate **Bearer Token**
4. Add to your `.env.local`:

```bash
# Twitter API
TWITTER_API_KEY=your_api_key_here
TWITTER_API_SECRET=your_api_secret_here
TWITTER_BEARER_TOKEN=your_bearer_token_here
```

## 🗄️ Step 3: Database Setup

### 3.1 Run Multi-Platform Migration
1. Go to your Supabase dashboard
2. Navigate to **SQL Editor**
3. Copy and paste the contents of: `supabase/migrations/20250110000002_enhance_content_multiplatform.sql`
4. Execute the SQL

### 3.2 Update User Preferences Table
Add Instagram credentials columns:
```sql
ALTER TABLE public.user_preferences 
ADD COLUMN IF NOT EXISTS instagram_access_token TEXT,
ADD COLUMN IF NOT EXISTS instagram_user_id TEXT,
ADD COLUMN IF NOT EXISTS instagram_token_expires_at TIMESTAMP WITH TIME ZONE;
```

## 🧪 Step 4: Test the Setup

### 4.1 Test Instagram API
```bash
# Test Instagram connection
curl -X GET "http://localhost:3000/api/test/instagram"
```

### 4.2 Test Twitter API
```bash
# Test Twitter connection
curl -X GET "http://localhost:3000/api/test/twitter"
```

### 4.3 Test Multi-Platform Aggregation
```bash
# Test all platforms
curl -X POST "http://localhost:3000/api/content/aggregate?platforms=youtube,instagram,twitter"

# Test specific platform
curl -X POST "http://localhost:3000/api/content/aggregate?platforms=instagram"
```

## 🎯 Step 5: Connect Instagram Account

### 5.1 User Flow
1. User goes to `/feed`
2. Clicks **"Connect Instagram"** button
3. Redirects to Instagram OAuth
4. User authorizes SceneSniffer
5. Redirects back with access token
6. Token stored in user preferences

### 5.2 Add Connect Button to Feed
Add this to your feed page:

```tsx
<Button 
  onClick={() => window.location.href = '/api/auth/instagram/connect'}
  className="bg-pink-600 hover:bg-pink-700"
>
  <Instagram className="mr-2 h-4 w-4" />
  Connect Instagram
</Button>
```

## 📊 Expected Results

### Content Volume Increase:
- **Before**: ~50 YouTube videos/day
- **After**: ~500-1000 pieces of content/day
  - YouTube: 50 videos/day (deep content)
  - Instagram: 200-400 posts/day (visual recommendations)
  - Twitter: 300-600 tweets/day (real-time news)

### Content Types:
- **YouTube**: Reviews, breakdowns, theories
- **Instagram**: Recommendations, visual content, stories
- **Twitter**: News, quick takes, discussions, threads

## 🔧 Troubleshooting

### Instagram Issues:
- **"Invalid redirect URI"**: Check redirect URI matches exactly
- **"App not approved"**: Instagram Basic Display works immediately
- **"No posts found"**: User needs public posts about movies/TV

### Twitter Issues:
- **"Rate limit exceeded"**: Wait 15 minutes, then retry
- **"User not found"**: Check Twitter handle format (@username)
- **"Forbidden"**: Check Bearer token is correct

### General Issues:
- **"No content found"**: Check creators have movie/TV content
- **"Database error"**: Run the migration SQL first
- **"API key invalid"**: Double-check environment variables

## 🚀 Next Steps

1. **Set up APIs** (Instagram + Twitter)
2. **Run database migration**
3. **Test with sample creators**
4. **Add creator discovery automation**
5. **Set up automated aggregation (every 15 minutes)**

Your feed will now have **10x more content** with intelligent multi-platform aggregation! 🎉

## 📞 Support

If you encounter issues:
1. Check the console logs for detailed error messages
2. Verify API credentials in `.env.local`
3. Ensure database migration completed successfully
4. Test individual platform APIs first

Happy aggregating! 🚀

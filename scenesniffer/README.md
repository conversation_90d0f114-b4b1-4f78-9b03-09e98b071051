# 🎬 SceneSniffer

Your personalized movie and TV content intelligence platform. Discover what to watch through trusted creators, AI-powered insights, and real-time streaming availability.

## 🚀 Features

- **Creator-First Discovery**: Follow your favorite YouTube, Instagram, and Twitter creators
- **AI-Powered Insights**: Smart summarization and content categorization
- **Streaming Intelligence**: Real-time "Where to Watch" information
- **Personalized Feed**: Curated content based on your preferences
- **Watchlist Management**: Track what you want to watch
- **Content Analysis**: Automatic movie/TV show detection and metadata

## 🛠️ Tech Stack

- **Frontend**: Next.js 14+ with TypeScript and Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **AI**: OpenAI GPT-3.5 Turbo
- **External APIs**: YouTube API v3, TMDb API, JustWatch (unofficial)
- **UI Components**: Radix UI with custom styling

## 📋 Prerequisites

- Node.js 18+ and npm
- Supabase account
- OpenAI API key
- YouTube Data API v3 key
- TMDb API key

## 🔧 Setup Instructions

### 1. Clone and Install

```bash
git clone <repository-url>
cd scenesniffer
npm install
```

### 2. Environment Variables

Copy `.env.example` to `.env.local` and fill in your API keys:

```bash
cp .env.example .env.local
```

Required environment variables:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here

# YouTube API Configuration
YOUTUBE_API_KEY=your_youtube_api_key_here

# TMDb API Configuration
TMDB_API_KEY=your_tmdb_api_key_here

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 3. Database Setup

1. Create a new Supabase project
2. Run the database schema from `database/schema.sql`
3. Enable Row Level Security (RLS) policies

### 4. Run the Application

```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Project Structure

```
scenesniffer/
├── src/
│   ├── app/                 # Next.js app directory
│   ├── components/         # React components
│   ├── lib/               # Utility libraries
│   ├── types/             # TypeScript definitions
│   └── utils/             # Utility functions
├── database/
│   └── schema.sql         # Database schema
└── package.json
```

## 🚀 Deployment

Deploy to Vercel by connecting your GitHub repository and adding environment variables.

## 📈 Roadmap

### Phase 1 (MVP) ✅
- [x] Basic project setup
- [x] Database schema
- [x] API integrations
- [x] Core UI components

### Phase 2
- [ ] User authentication
- [ ] Creator management
- [ ] Feed functionality
- [ ] Watchlist features

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

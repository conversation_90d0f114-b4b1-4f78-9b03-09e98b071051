# Background Jobs System

This document explains the new background job processing system that allows long-running operations to run asynchronously without blocking the UI.

## Overview

The background job system provides:
- **Immediate API responses** with job IDs
- **Real-time progress tracking** via polling
- **Job status management** (pending, running, completed, failed)
- **Result storage** for completed jobs
- **Error handling** with detailed error messages

## Architecture

### Components

1. **Database Table**: `background_jobs` - stores job metadata and progress
2. **Service Class**: `BackgroundJobService` - manages job lifecycle
3. **React Hook**: `useBackgroundJob` - handles polling and state management
4. **UI Component**: `JobProgress` - displays progress and status
5. **API Endpoints**: Job creation and status checking

### Flow

```
1. User clicks "Refresh" button
2. API creates job record immediately
3. API returns job ID to client
4. Background processing starts asynchronously
5. Client polls job status every 2 seconds
6. Progress updates in real-time
7. Job completes and client receives results
8. UI updates with completion status
```

## Usage

### 1. Setup Database

First, create the background jobs table:

```bash
# Run the setup endpoint
curl -X POST http://localhost:3000/api/setup-background-jobs
```

Or visit the demo page: `/background-jobs-demo`

### 2. Create a Background Job

Modify your existing API endpoint to use background processing:

```typescript
import { BackgroundJobService } from '@/lib/background-jobs'

export async function POST(request: NextRequest) {
  const jobService = new BackgroundJobService()
  
  // Create job immediately
  const jobId = await jobService.createJob('my_job_type', { 
    param1: 'value1' 
  })
  
  // Start background processing (don't await)
  processInBackground(jobId)
    .catch(error => jobService.failJob(jobId, error.message))
  
  // Return immediately
  return NextResponse.json({
    success: true,
    jobId,
    status: 'started'
  })
}

async function processInBackground(jobId: string) {
  const jobService = new BackgroundJobService()
  
  try {
    await jobService.startJob(jobId, totalItems)
    
    // Your processing logic here
    for (let i = 0; i < totalItems; i++) {
      // Process item
      await processItem(i)
      
      // Update progress
      await jobService.updateProgress(jobId, i + 1, totalItems)
    }
    
    // Complete job
    await jobService.completeJob(jobId, { 
      message: 'Processing completed',
      itemsProcessed: totalItems 
    })
    
  } catch (error) {
    await jobService.failJob(jobId, error.message)
  }
}
```

### 3. Add Progress Tracking to UI

Use the provided React components:

```typescript
import { JobProgress } from '@/components/JobProgress'
import { useBackgroundJob } from '@/hooks/useBackgroundJob'

function MyComponent() {
  const [jobId, setJobId] = useState<string | null>(null)
  
  const startJob = async () => {
    const response = await fetch('/api/my-endpoint', { method: 'POST' })
    const data = await response.json()
    setJobId(data.jobId)
  }
  
  const handleComplete = (result: any) => {
    console.log('Job completed:', result)
    // Refresh your data here
    refreshData()
  }
  
  return (
    <div>
      <button onClick={startJob}>Start Processing</button>
      
      {jobId && (
        <JobProgress
          jobId={jobId}
          jobType="my_job_type"
          onComplete={handleComplete}
          onError={(error) => console.error(error)}
        />
      )}
    </div>
  )
}
```

### 4. Or Use the Pre-built Component

For simple refresh operations:

```typescript
import { RefreshWithProgress } from '@/components/RefreshWithProgress'

function MyPage() {
  const refreshData = () => {
    // Your data refresh logic
    fetchContent()
  }
  
  return (
    <RefreshWithProgress onRefreshComplete={refreshData} />
  )
}
```

## API Endpoints

### Create Job Status Endpoint

```typescript
// /api/jobs/[jobId]/route.ts
import { BackgroundJobService } from '@/lib/background-jobs'

export async function GET(request, { params }) {
  const jobService = new BackgroundJobService()
  const job = await jobService.getJob(params.jobId)
  
  return NextResponse.json({
    success: true,
    job: {
      id: job.id,
      status: job.status,
      progress: job.progress,
      result: job.result,
      error_message: job.error_message
    }
  })
}
```

## Job Types

Currently supported job types:
- `bundle_refresh` - Refresh creator bundles
- `content_backfill` - Process content with AI
- Add your own custom job types

## Best Practices

1. **Always handle errors** - Use try/catch and `failJob()`
2. **Update progress regularly** - Keep users informed
3. **Set reasonable timeouts** - Don't let jobs run forever
4. **Clean up old jobs** - Use `cleanupOldJobs()` periodically
5. **Test thoroughly** - Use the demo page to verify functionality

## Demo

Visit `/background-jobs-demo` to see the system in action and test the functionality.

## Benefits

✅ **Non-blocking UI** - Users can continue using the app while jobs run
✅ **Real-time feedback** - Progress bars and status updates
✅ **Error handling** - Clear error messages and recovery
✅ **Scalable** - Easy to add new job types
✅ **Simple** - No complex infrastructure required

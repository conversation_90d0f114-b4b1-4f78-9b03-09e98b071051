// Test script to debug the content aggregation endpoint
const fetch = require('node-fetch');

async function testAggregateEndpoint() {
  console.log('🎬 Testing Content Aggregation Endpoint...\n');
  
  try {
    // Test the POST endpoint
    const response = await fetch('http://localhost:3001/api/content/aggregate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    
    const result = await response.json();
    
    console.log('Response Status:', response.status);
    console.log('Response Data:', JSON.stringify(result, null, 2));
    
    // Also test the test-content endpoint to see current state
    console.log('\n📊 Current Database State:');
    const testResponse = await fetch('http://localhost:3001/api/test-content');
    const testResult = await testResponse.json();
    
    console.log('Creators Count:', testResult.data?.creators?.count || 0);
    console.log('Content Count:', testResult.data?.content?.count || 0);
    console.log('YouTube API Status:', testResult.data?.youtubeApi?.status);
    console.log('Has YouTube API Key:', testResult.data?.environment?.hasYouTubeKey);
    
  } catch (error) {
    console.error('Error testing endpoint:', error);
  }
}

testAggregateEndpoint();

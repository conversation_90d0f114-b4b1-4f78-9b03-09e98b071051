'use client'

import { useCallback, useState } from 'react'
import { useWatchlist } from '@/contexts/WatchlistContext'
import { toast } from 'sonner'

// Hook for watchlist actions with optimistic updates and error handling
export function useWatchlistActions() {
  const { state, actions } = useWatchlist()
  const [actionLoading, setActionLoading] = useState<Record<string, boolean>>({})

  // Helper to set loading state for specific actions
  const setLoading = useCallback((key: string, loading: boolean) => {
    setActionLoading(prev => ({ ...prev, [key]: loading }))
  }, [])

  // Add to watchlist with toast feedback
  const addToWatchlist = useCallback(async (item: {
    tmdb_id: number
    content_type: 'movie' | 'tv'
    title: string
    poster_path?: string
    total_runtime?: number
  }) => {
    const key = `add-${item.tmdb_id}-${item.content_type}`
    setLoading(key, true)

    try {
      await actions.addToWatchlist({
        ...item,
        status: 'want_to_watch',
        progress_percentage: 0
      })
      
      toast.success(`Added "${item.title}" to watchlist`)
      return true
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to add to watchlist'
      toast.error(message)
      return false
    } finally {
      setLoading(key, false)
    }
  }, [actions, setLoading])

  // Remove from watchlist with confirmation
  const removeFromWatchlist = useCallback(async (id: string, title: string) => {
    const key = `remove-${id}`
    setLoading(key, true)

    try {
      await actions.removeFromWatchlist(id)
      toast.success(`Removed "${title}" from watchlist`)
      return true
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to remove from watchlist'
      toast.error(message)
      return false
    } finally {
      setLoading(key, false)
    }
  }, [actions, setLoading])

  // Start watching an item
  const startWatching = useCallback(async (id: string, title: string) => {
    const key = `start-${id}`
    setLoading(key, true)

    try {
      await actions.performAction(id, 'start_watching')
      toast.success(`Started watching "${title}"`)
      return true
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to start watching'
      toast.error(message)
      return false
    } finally {
      setLoading(key, false)
    }
  }, [actions, setLoading])

  // Mark as completed
  const markCompleted = useCallback(async (id: string, title: string) => {
    const key = `complete-${id}`
    setLoading(key, true)

    try {
      await actions.performAction(id, 'finish_watching')
      toast.success(`Completed "${title}"!`)
      return true
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to mark as completed'
      toast.error(message)
      return false
    } finally {
      setLoading(key, false)
    }
  }, [actions, setLoading])

  // Update progress (for movies)
  const updateProgress = useCallback(async (id: string, progressPercentage: number, title?: string) => {
    const key = `progress-${id}`
    setLoading(key, true)

    try {
      await actions.updateProgress(id, { progress_percentage: progressPercentage })
      
      if (progressPercentage >= 100) {
        toast.success(`Completed "${title || 'item'}"!`)
      } else {
        toast.success(`Progress updated to ${Math.round(progressPercentage)}%`)
      }
      return true
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to update progress'
      toast.error(message)
      return false
    } finally {
      setLoading(key, false)
    }
  }, [actions, setLoading])

  // Mark episode as watched (for TV shows)
  const markEpisodeWatched = useCallback(async (id: string, title?: string) => {
    const key = `episode-${id}`
    setLoading(key, true)

    try {
      await actions.performAction(id, 'mark_current_episode_watched')
      toast.success(`Episode marked as watched${title ? ` for "${title}"` : ''}`)
      return true
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to mark episode as watched'
      toast.error(message)
      return false
    } finally {
      setLoading(key, false)
    }
  }, [actions, setLoading])

  // Move to next season (for TV shows)
  const nextSeason = useCallback(async (id: string, title?: string) => {
    const key = `season-${id}`
    setLoading(key, true)

    try {
      await actions.performAction(id, 'next_season')
      toast.success(`Moved to next season${title ? ` for "${title}"` : ''}`)
      return true
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to move to next season'
      toast.error(message)
      return false
    } finally {
      setLoading(key, false)
    }
  }, [actions, setLoading])

  // Update rating
  const updateRating = useCallback(async (id: string, rating: number, title?: string) => {
    const key = `rating-${id}`
    setLoading(key, true)

    try {
      await actions.updateWatchlistItem(id, { user_rating: rating })
      toast.success(`Rated "${title || 'item'}" ${rating}/10`)
      return true
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to update rating'
      toast.error(message)
      return false
    } finally {
      setLoading(key, false)
    }
  }, [actions, setLoading])

  // Update status
  const updateStatus = useCallback(async (id: string, status: string, title?: string) => {
    const key = `status-${id}`
    setLoading(key, true)

    try {
      await actions.updateWatchlistItem(id, { status: status as any })
      toast.success(`Status updated${title ? ` for "${title}"` : ''}`)
      return true
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to update status'
      toast.error(message)
      return false
    } finally {
      setLoading(key, false)
    }
  }, [actions, setLoading])

  // Add personal notes
  const updateNotes = useCallback(async (id: string, notes: string, title?: string) => {
    const key = `notes-${id}`
    setLoading(key, true)

    try {
      await actions.updateWatchlistItem(id, { personal_notes: notes })
      toast.success(`Notes updated${title ? ` for "${title}"` : ''}`)
      return true
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to update notes'
      toast.error(message)
      return false
    } finally {
      setLoading(key, false)
    }
  }, [actions, setLoading])

  return {
    // State
    loading: state.loading,
    error: state.error,
    actionLoading,
    
    // Actions
    addToWatchlist,
    removeFromWatchlist,
    startWatching,
    markCompleted,
    updateProgress,
    markEpisodeWatched,
    nextSeason,
    updateRating,
    updateStatus,
    updateNotes,
    
    // Utility
    clearError: actions.clearError,
    isActionLoading: (key: string) => actionLoading[key] || false
  }
}

import { useState, useEffect, useCallback } from 'react'

export interface JobStatus {
  id: string
  type: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  progress: number
  total_items: number
  processed_items: number
  result?: any
  error_message?: string
  created_at: string
  updated_at: string
  completed_at?: string
}

export interface UseBackgroundJobReturn {
  job: JobStatus | null
  isLoading: boolean
  isCompleted: boolean
  isFailed: boolean
  error: string | null
  startPolling: (jobId: string) => void
  stopPolling: () => void
}

export function useBackgroundJob(
  onComplete?: (result: any) => void,
  onError?: (error: string) => void,
  pollInterval: number = 2000
): UseBackgroundJobReturn {
  const [job, setJob] = useState<JobStatus | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pollJobId, setPollJobId] = useState<string | null>(null)

  const fetchJobStatus = useCallback(async (jobId: string) => {
    try {
      const response = await fetch(`/api/jobs/${jobId}`)
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to fetch job status')
      }

      if (data.success && data.job) {
        setJob(data.job)
        setError(null)

        // Handle completion
        if (data.job.status === 'completed') {
          setPollJobId(null) // Stop polling
          if (onComplete) {
            onComplete(data.job.result)
          }
        }

        // Handle failure
        if (data.job.status === 'failed') {
          setPollJobId(null) // Stop polling
          const errorMsg = data.job.error_message || 'Job failed'
          setError(errorMsg)
          if (onError) {
            onError(errorMsg)
          }
        }
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMsg)
      setPollJobId(null) // Stop polling on fetch error
      if (onError) {
        onError(errorMsg)
      }
    }
  }, [onComplete, onError])

  // Polling effect
  useEffect(() => {
    if (!pollJobId) return

    setIsLoading(true)
    
    const poll = async () => {
      await fetchJobStatus(pollJobId)
    }

    // Initial fetch
    poll()

    // Set up polling interval
    const interval = setInterval(poll, pollInterval)

    return () => {
      clearInterval(interval)
      setIsLoading(false)
    }
  }, [pollJobId, pollInterval, fetchJobStatus])

  const startPolling = useCallback((jobId: string) => {
    setJob(null)
    setError(null)
    setPollJobId(jobId)
  }, [])

  const stopPolling = useCallback(() => {
    setPollJobId(null)
    setIsLoading(false)
  }, [])

  return {
    job,
    isLoading,
    isCompleted: job?.status === 'completed',
    isFailed: job?.status === 'failed',
    error,
    startPolling,
    stopPolling
  }
}

'use client'

import { useState, useEffect, useCallback } from 'react'
import { CreatorBundle, UserBundle } from '@/types'

export interface BundleFilters {
  platform?: string
  status?: string
  limit?: number
  offset?: number
}

export function useBundles(filters: BundleFilters = {}) {
  const [bundles, setBundles] = useState<CreatorBundle[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState({
    total: 0,
    limit: 20,
    offset: 0,
    hasMore: false
  })

  // Fetch bundles
  const fetchBundles = useCallback(async (newFilters?: BundleFilters) => {
    setLoading(true)
    setError(null)
    
    try {
      const params = new URLSearchParams({
        platform: newFilters?.platform || filters.platform || 'youtube',
        status: newFilters?.status || filters.status || 'active',
        limit: String(newFilters?.limit || filters.limit || 20),
        offset: String(newFilters?.offset || filters.offset || 0)
      })

      const response = await fetch(`/api/bundles?${params}`)
      const data = await response.json()
      
      if (data.success) {
        setBundles(data.bundles)
        setPagination(data.pagination)
      } else {
        setError(data.error || 'Failed to fetch bundles')
      }
    } catch (err) {
      setError('Network error while fetching bundles')
      console.error('Error fetching bundles:', err)
    } finally {
      setLoading(false)
    }
  }, [filters])

  // Get bundle details
  const getBundleDetails = useCallback(async (bundleId: string) => {
    try {
      const response = await fetch(`/api/bundles/${bundleId}`)
      const data = await response.json()
      
      if (data.success) {
        return { success: true, bundle: data.bundle }
      } else {
        return { success: false, error: data.error }
      }
    } catch (err) {
      console.error('Error fetching bundle details:', err)
      return { success: false, error: 'Network error' }
    }
  }, [])

  // Add bundle to user's subscriptions
  const addBundle = useCallback(async (bundleId: string) => {
    setError(null)
    
    try {
      const response = await fetch('/api/users/bundles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ bundle_id: bundleId })
      })
      
      const data = await response.json()
      
      if (data.success) {
        return { 
          success: true, 
          message: data.message,
          creators_added: data.creators_added 
        }
      } else {
        setError(data.error || 'Failed to add bundle')
        return { success: false, error: data.error }
      }
    } catch (err) {
      const errorMsg = 'Network error while adding bundle'
      setError(errorMsg)
      console.error('Error adding bundle:', err)
      return { success: false, error: errorMsg }
    }
  }, [])

  // Remove bundle from user's subscriptions
  const removeBundle = useCallback(async (bundleId: string) => {
    setError(null)
    
    try {
      const response = await fetch(`/api/users/bundles?bundle_id=${bundleId}`, {
        method: 'DELETE'
      })
      
      const data = await response.json()
      
      if (data.success) {
        return { success: true, message: data.message }
      } else {
        setError(data.error || 'Failed to remove bundle')
        return { success: false, error: data.error }
      }
    } catch (err) {
      const errorMsg = 'Network error while removing bundle'
      setError(errorMsg)
      console.error('Error removing bundle:', err)
      return { success: false, error: errorMsg }
    }
  }, [])

  // Load more bundles (pagination)
  const loadMore = useCallback(async () => {
    if (!pagination.hasMore || loading) return
    
    await fetchBundles({
      ...filters,
      offset: pagination.offset + pagination.limit
    })
  }, [fetchBundles, filters, pagination, loading])

  // Refresh bundles
  const refresh = useCallback(() => {
    fetchBundles({ ...filters, offset: 0 })
  }, [fetchBundles, filters])

  // Load bundles on mount and when filters change
  useEffect(() => {
    fetchBundles()
  }, [fetchBundles])

  return {
    // State
    bundles,
    loading,
    error,
    pagination,
    
    // Actions
    fetchBundles,
    getBundleDetails,
    addBundle,
    removeBundle,
    loadMore,
    refresh,
    
    // Utilities
    totalBundles: pagination.total,
    hasMore: pagination.hasMore
  }
}

export function useUserBundles() {
  const [userBundles, setUserBundles] = useState<UserBundle[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch user's subscribed bundles
  const fetchUserBundles = useCallback(async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/users/bundles')
      const data = await response.json()
      
      if (data.success) {
        setUserBundles(data.bundles)
      } else {
        setError(data.error || 'Failed to fetch user bundles')
      }
    } catch (err) {
      setError('Network error while fetching user bundles')
      console.error('Error fetching user bundles:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  // Check if user is subscribed to a bundle
  const isSubscribed = useCallback((bundleId: string) => {
    return userBundles.some(ub => ub.bundle_id === bundleId)
  }, [userBundles])

  // Get user bundle by ID
  const getUserBundle = useCallback((bundleId: string) => {
    return userBundles.find(ub => ub.bundle_id === bundleId)
  }, [userBundles])

  // Load user bundles on mount
  useEffect(() => {
    fetchUserBundles()
  }, [fetchUserBundles])

  return {
    // State
    userBundles,
    loading,
    error,
    
    // Actions
    fetchUserBundles,
    refresh: fetchUserBundles,
    
    // Utilities
    isSubscribed,
    getUserBundle,
    totalSubscribed: userBundles.length
  }
}

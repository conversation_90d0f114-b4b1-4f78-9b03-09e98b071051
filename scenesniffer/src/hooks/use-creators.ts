'use client'

import { useState, useEffect, useCallback } from 'react'

export interface Creator {
  id: string
  platform: string
  handle: string
  name: string
  verified: boolean
  trust_score: number
  avatar_url?: string
  follower_count?: number
  followed_at: string
  created_at: string
}

export interface CreatorInput {
  platform: string
  handle: string
  name?: string
}

export function useCreators() {
  const [creators, setCreators] = useState<Creator[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch user's followed creators
  const fetchCreators = useCallback(async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/api/user/creators')
      const data = await response.json()
      
      if (data.success) {
        setCreators(data.creators)
      } else {
        setError(data.error || 'Failed to fetch creators')
      }
    } catch (err) {
      setError('Network error while fetching creators')
      console.error('Error fetching creators:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  // Add/Follow a creator
  const addCreator = useCallback(async (creatorData: CreatorInput) => {
    setError(null)
    
    try {
      const response = await fetch('/api/user/creators', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(creatorData)
      })
      
      const data = await response.json()
      
      if (data.success) {
        // Refresh the creators list
        await fetchCreators()
        return { success: true, creator: data.creator }
      } else {
        setError(data.error || 'Failed to follow creator')
        return { success: false, error: data.error }
      }
    } catch (err) {
      const errorMsg = 'Network error while following creator'
      setError(errorMsg)
      console.error('Error adding creator:', err)
      return { success: false, error: errorMsg }
    }
  }, [fetchCreators])

  // Remove/Unfollow a creator by ID
  const removeCreator = useCallback(async (creatorId: string) => {
    setError(null)
    
    try {
      const response = await fetch(`/api/user/creators?creator_id=${creatorId}`, {
        method: 'DELETE'
      })
      
      const data = await response.json()
      
      if (data.success) {
        // Remove from local state immediately for better UX
        setCreators(prev => prev.filter(c => c.id !== creatorId))
        return { success: true }
      } else {
        setError(data.error || 'Failed to unfollow creator')
        return { success: false, error: data.error }
      }
    } catch (err) {
      const errorMsg = 'Network error while unfollowing creator'
      setError(errorMsg)
      console.error('Error removing creator:', err)
      return { success: false, error: errorMsg }
    }
  }, [])

  // Remove/Unfollow a creator by platform and handle
  const removeCreatorByHandle = useCallback(async (platform: string, handle: string) => {
    setError(null)
    
    try {
      const response = await fetch(`/api/user/creators?platform=${platform}&handle=${handle}`, {
        method: 'DELETE'
      })
      
      const data = await response.json()
      
      if (data.success) {
        // Remove from local state immediately for better UX
        setCreators(prev => prev.filter(c => !(c.platform === platform && c.handle === handle)))
        return { success: true }
      } else {
        setError(data.error || 'Failed to unfollow creator')
        return { success: false, error: data.error }
      }
    } catch (err) {
      const errorMsg = 'Network error while unfollowing creator'
      setError(errorMsg)
      console.error('Error removing creator:', err)
      return { success: false, error: errorMsg }
    }
  }, [])

  // Check if user is following a specific creator
  const isFollowing = useCallback((platform: string, handle: string) => {
    return creators.some(c => c.platform === platform && c.handle === handle)
  }, [creators])

  // Get creator by platform and handle
  const getCreator = useCallback((platform: string, handle: string) => {
    return creators.find(c => c.platform === platform && c.handle === handle)
  }, [creators])

  // Get creators by platform
  const getCreatorsByPlatform = useCallback((platform: string) => {
    return creators.filter(c => c.platform === platform)
  }, [creators])

  // Load creators on mount
  useEffect(() => {
    fetchCreators()
  }, [fetchCreators])

  return {
    // State
    creators,
    loading,
    error,
    
    // Actions
    fetchCreators,
    addCreator,
    removeCreator,
    removeCreatorByHandle,
    
    // Utilities
    isFollowing,
    getCreator,
    getCreatorsByPlatform,
    
    // Stats
    totalCreators: creators.length,
    platformCounts: creators.reduce((acc, creator) => {
      acc[creator.platform] = (acc[creator.platform] || 0) + 1
      return acc
    }, {} as Record<string, number>)
  }
}

'use client'

import { useState, useEffect, useCallback, useRef, useMemo } from 'react'
import { useWatchlist } from '@/contexts/WatchlistContext'

interface WatchlistStatusItem {
  tmdb_id: number
  content_type: 'movie' | 'tv'
  in_watchlist: boolean
  status?: string
  watchlist_id?: string
}

// Hook for checking and managing watchlist status of multiple items
export function useWatchlistStatus(items: Array<{ tmdb_id: number; content_type: 'movie' | 'tv' }>) {
  const { actions } = useWatchlist()
  const [statusMap, setStatusMap] = useState<Record<string, WatchlistStatusItem>>({})
  const [loading, setLoading] = useState(false)

  // Create a key for the status map
  const createKey = useCallback((tmdbId: number, contentType: string) => {
    return `${tmdbId}-${contentType}`
  }, [])

  // Check status for all items
  const checkStatus = useCallback(async () => {
    if (items.length === 0) return

    setLoading(true)
    try {
      const tmdbIds = items.map(item => item.tmdb_id)
      const statusData = await actions.checkWatchlistStatus(tmdbIds)
      
      const newStatusMap: Record<string, WatchlistStatusItem> = {}
      
      items.forEach(item => {
        const key = createKey(item.tmdb_id, item.content_type)
        const status = statusData[item.tmdb_id.toString()]
        
        newStatusMap[key] = {
          tmdb_id: item.tmdb_id,
          content_type: item.content_type,
          in_watchlist: status?.in_watchlist || false,
          status: status?.status,
          watchlist_id: status?.watchlist_id
        }
      })
      
      setStatusMap(newStatusMap)
    } catch (error) {
      console.error('Failed to check watchlist status:', error)
    } finally {
      setLoading(false)
    }
  }, [items, actions, createKey])

  // Create a stable dependency that doesn't change when array reference changes
  const itemsKey = useMemo(() => {
    return items.map(item => `${item.tmdb_id}-${item.content_type}`).sort().join(',')
  }, [items])

  // Global cache to prevent duplicate API calls
  const globalCache = useRef<Map<string, Promise<any>>>(new Map())

  // Check status when items change (using stable key instead of array reference)
  useEffect(() => {
    if (!itemsKey) return

    // Check if we're already fetching this exact set of items
    if (globalCache.current.has(itemsKey)) {
      return // Skip if already in progress
    }

    // Mark this request as in progress
    const promise = checkStatus()
    globalCache.current.set(itemsKey, promise)

    // Clean up when done
    promise.finally(() => {
      globalCache.current.delete(itemsKey)
    })
  }, [itemsKey, checkStatus])

  // Get status for a specific item
  const getItemStatus = useCallback((tmdbId: number, contentType: 'movie' | 'tv') => {
    const key = createKey(tmdbId, contentType)
    return statusMap[key] || {
      tmdb_id: tmdbId,
      content_type: contentType,
      in_watchlist: false,
      status: undefined,
      watchlist_id: undefined
    }
  }, [statusMap, createKey])

  // Update status for a specific item (after add/remove operations)
  const updateItemStatus = useCallback((tmdbId: number, contentType: 'movie' | 'tv', updates: Partial<WatchlistStatusItem>) => {
    const key = createKey(tmdbId, contentType)
    setStatusMap(prev => ({
      ...prev,
      [key]: {
        ...prev[key],
        tmdb_id: tmdbId,
        content_type: contentType,
        ...updates
      }
    }))
  }, [createKey])

  // Mark item as added to watchlist
  const markAsAdded = useCallback((tmdbId: number, contentType: 'movie' | 'tv', watchlistId: string, status: string = 'want_to_watch') => {
    updateItemStatus(tmdbId, contentType, {
      in_watchlist: true,
      status,
      watchlist_id: watchlistId
    })
  }, [updateItemStatus])

  // Mark item as removed from watchlist
  const markAsRemoved = useCallback((tmdbId: number, contentType: 'movie' | 'tv') => {
    updateItemStatus(tmdbId, contentType, {
      in_watchlist: false,
      status: undefined,
      watchlist_id: undefined
    })
  }, [updateItemStatus])

  // Update status for an item
  const updateStatus = useCallback((tmdbId: number, contentType: 'movie' | 'tv', status: string) => {
    updateItemStatus(tmdbId, contentType, { status })
  }, [updateItemStatus])

  return {
    statusMap,
    loading,
    getItemStatus,
    markAsAdded,
    markAsRemoved,
    updateStatus,
    refetch: checkStatus
  }
}

// Hook for a single item's watchlist status
export function useSingleWatchlistStatus(tmdbId: number, contentType: 'movie' | 'tv') {
  const { statusMap, loading, getItemStatus, markAsAdded, markAsRemoved, updateStatus } = useWatchlistStatus([{ tmdb_id: tmdbId, content_type: contentType }])
  
  const status = getItemStatus(tmdbId, contentType)
  
  return {
    inWatchlist: status.in_watchlist,
    status: status.status,
    watchlistId: status.watchlist_id,
    loading,
    markAsAdded: (watchlistId: string, statusValue?: string) => markAsAdded(tmdbId, contentType, watchlistId, statusValue),
    markAsRemoved: () => markAsRemoved(tmdbId, contentType),
    updateStatus: (statusValue: string) => updateStatus(tmdbId, contentType, statusValue)
  }
}

import { useCallback, useEffect, useRef } from 'react'
import { useAuth } from '@/components/providers/auth-provider'

interface UseContentTrackingOptions {
  contentId: string
  contentTitle?: string
  autoTrackView?: boolean
  viewThreshold?: number // Seconds before counting as a view
}

export function useContentTracking({
  contentId,
  contentTitle,
  autoTrackView = true,
  viewThreshold = 3
}: UseContentTrackingOptions) {
  const { user } = useAuth()
  const viewTracked = useRef(false)
  const startTime = useRef<number>(Date.now())
  const timeSpentInterval = useRef<NodeJS.Timeout>()

  // Track content interaction
  const trackInteraction = useCallback(async (
    interactionType: 'view' | 'click' | 'share' | 'bookmark' | 'like' | 'time_spent',
    value?: number,
    metadata?: Record<string, any>
  ) => {
    if (!user || !contentId) return

    try {
      const response = await fetch(`/api/content/${contentId}/interact`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          interaction_type: interactionType,
          interaction_value: value,
          metadata: {
            content_title: contentTitle,
            ...metadata
          }
        })
      })

      if (!response.ok) {
        console.warn('Failed to track interaction:', await response.text())
      }
    } catch (error) {
      console.warn('Error tracking interaction:', error)
    }
  }, [user, contentId, contentTitle])

  // Track view after threshold
  const trackView = useCallback(() => {
    if (!viewTracked.current && user) {
      viewTracked.current = true
      trackInteraction('view')
    }
  }, [trackInteraction, user])

  // Track time spent
  const trackTimeSpent = useCallback(() => {
    const timeSpent = (Date.now() - startTime.current) / 1000 // Convert to seconds
    if (timeSpent > 5) { // Only track if spent more than 5 seconds
      trackInteraction('time_spent', timeSpent)
    }
  }, [trackInteraction])

  // Auto-track view after threshold
  useEffect(() => {
    if (autoTrackView && user && contentId) {
      const timer = setTimeout(() => {
        trackView()
      }, viewThreshold * 1000)

      return () => clearTimeout(timer)
    }
  }, [autoTrackView, user, contentId, viewThreshold, trackView])

  // Track time spent periodically
  useEffect(() => {
    if (user && contentId) {
      // Track time spent every 30 seconds
      timeSpentInterval.current = setInterval(() => {
        trackTimeSpent()
        startTime.current = Date.now() // Reset start time
      }, 30000)

      return () => {
        if (timeSpentInterval.current) {
          clearInterval(timeSpentInterval.current)
        }
        // Track final time spent on unmount
        trackTimeSpent()
      }
    }
  }, [user, contentId, trackTimeSpent])

  // Manual tracking functions
  const trackClick = useCallback((metadata?: Record<string, any>) => {
    trackInteraction('click', undefined, metadata)
  }, [trackInteraction])

  const trackShare = useCallback((platform?: string) => {
    trackInteraction('share', undefined, { platform })
  }, [trackInteraction])

  const trackBookmark = useCallback(() => {
    trackInteraction('bookmark')
  }, [trackInteraction])

  const trackLike = useCallback(() => {
    trackInteraction('like')
  }, [trackInteraction])

  return {
    trackView,
    trackClick,
    trackShare,
    trackBookmark,
    trackLike,
    trackInteraction
  }
}

// Hook for tracking feed-level interactions
export function useFeedTracking() {
  const { user } = useAuth()

  const trackFeedView = useCallback(async (metadata?: Record<string, any>) => {
    if (!user) return

    try {
      await fetch('/api/analytics/feed-view', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          user_id: user.id,
          metadata: {
            timestamp: new Date().toISOString(),
            ...metadata
          }
        })
      })
    } catch (error) {
      console.warn('Error tracking feed view:', error)
    }
  }, [user])

  const trackContentCardClick = useCallback(async (contentId: string, position: number) => {
    if (!user) return

    try {
      await fetch(`/api/content/${contentId}/interact`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          interaction_type: 'click',
          metadata: {
            source: 'feed',
            position,
            timestamp: new Date().toISOString()
          }
        })
      })
    } catch (error) {
      console.warn('Error tracking content card click:', error)
    }
  }, [user])

  return {
    trackFeedView,
    trackContentCardClick
  }
}

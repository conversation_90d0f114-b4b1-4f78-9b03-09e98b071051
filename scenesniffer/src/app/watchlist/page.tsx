'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useAuth } from '@/components/providers/auth-provider'
import { useWatchlist } from '@/contexts/WatchlistContext'
import { useWatchlistActions } from '@/hooks/useWatchlistActions'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Progress } from '@/components/ui/progress'
import { 
  Film, 
  Tv, 
  Search, 
  Filter, 
  SortAsc, 
  SortDesc,
  Play,
  Pause,
  CheckCircle,
  Clock,
  Star,
  Trash2,
  Edit,
  ArrowLeft,
  Grid,
  List,
  Calendar,
  TrendingUp
} from 'lucide-react'
import Link from 'next/link'

const STATUS_OPTIONS = [
  { value: 'all', label: 'All Items', icon: Grid },
  { value: 'want_to_watch', label: 'Want to Watch', icon: Clock },
  { value: 'watching', label: 'Currently Watching', icon: Play },
  { value: 'completed', label: 'Completed', icon: CheckCircle },
  { value: 'on_hold', label: 'On Hold', icon: Pause },
  { value: 'dropped', label: 'Dropped', icon: Trash2 }
]

const SORT_OPTIONS = [
  { value: 'added_at', label: 'Date Added' },
  { value: 'last_watched_at', label: 'Last Watched' },
  { value: 'title', label: 'Title' },
  { value: 'user_rating', label: 'Rating' },
  { value: 'progress_percentage', label: 'Progress' }
]

export default function WatchlistPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { user, loading: authLoading } = useAuth()
  const { state, actions } = useWatchlist()
  const { removeFromWatchlist, updateStatus, updateRating } = useWatchlistActions()

  // Filters and sorting
  const [filters, setFilters] = useState({
    status: searchParams.get('status') || 'all',
    type: searchParams.get('type') || 'all',
    search: searchParams.get('search') || ''
  })
  const [sortBy, setSortBy] = useState(searchParams.get('sort') || 'added_at')
  const [sortOrder, setSortOrder] = useState(searchParams.get('order') || 'desc')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/auth')
    }
  }, [user, authLoading, router])

  // Fetch watchlist when filters change
  useEffect(() => {
    if (user) {
      const fetchFilters: any = {}
      if (filters.status !== 'all') fetchFilters.status = filters.status
      if (filters.type !== 'all') fetchFilters.type = filters.type
      
      actions.fetchWatchlist(fetchFilters)
    }
  }, [user, filters, actions])

  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams()
    if (filters.status !== 'all') params.set('status', filters.status)
    if (filters.type !== 'all') params.set('type', filters.type)
    if (filters.search) params.set('search', filters.search)
    if (sortBy !== 'added_at') params.set('sort', sortBy)
    if (sortOrder !== 'desc') params.set('order', sortOrder)
    
    const newUrl = params.toString() ? `/watchlist?${params.toString()}` : '/watchlist'
    router.replace(newUrl, { scroll: false })
  }, [filters, sortBy, sortOrder, router])

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900 flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  // Filter and sort items
  let filteredItems = state.items.filter(item => {
    if (filters.search) {
      const searchLower = filters.search.toLowerCase()
      if (!item.title.toLowerCase().includes(searchLower)) {
        return false
      }
    }
    return true
  })

  // Sort items
  filteredItems.sort((a, b) => {
    let aValue: any = a[sortBy as keyof typeof a]
    let bValue: any = b[sortBy as keyof typeof b]
    
    if (sortBy === 'title') {
      aValue = aValue?.toLowerCase() || ''
      bValue = bValue?.toLowerCase() || ''
    }
    
    if (aValue === null || aValue === undefined) aValue = sortOrder === 'desc' ? -Infinity : Infinity
    if (bValue === null || bValue === undefined) bValue = sortOrder === 'desc' ? -Infinity : Infinity
    
    if (sortOrder === 'desc') {
      return bValue > aValue ? 1 : bValue < aValue ? -1 : 0
    } else {
      return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
    }
  })

  const statusCounts = STATUS_OPTIONS.map(status => ({
    ...status,
    count: status.value === 'all' 
      ? state.items.length 
      : state.items.filter(item => item.status === status.value).length
  }))

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Button
              variant="ghost"
              onClick={() => router.back()}
              className="text-white hover:bg-white/10"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-white">My Watchlist</h1>
              <p className="text-gray-300">
                Manage your movies and TV shows
              </p>
            </div>
          </div>

          {/* Status Filter Tabs */}
          <div className="flex flex-wrap gap-2 mb-6">
            {statusCounts.map((status) => {
              const Icon = status.icon
              const isActive = filters.status === status.value
              return (
                <Button
                  key={status.value}
                  variant={isActive ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilters(prev => ({ ...prev, status: status.value }))}
                  className={isActive 
                    ? "bg-purple-600 text-white" 
                    : "border-white/20 text-white hover:bg-white/10"
                  }
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {status.label}
                  <Badge variant="secondary" className="ml-2 bg-white/20 text-white">
                    {status.count}
                  </Badge>
                </Button>
              )
            })}
          </div>

          {/* Filters and Controls */}
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex gap-2 flex-1">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search watchlist..."
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  className="pl-10 bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                />
              </div>

              {/* Type Filter */}
              <Select value={filters.type} onValueChange={(value) => setFilters(prev => ({ ...prev, type: value }))}>
                <SelectTrigger className="w-32 bg-white/10 border-white/20 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="movie">Movies</SelectItem>
                  <SelectItem value="tv">TV Shows</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex gap-2 items-center">
              {/* Sort */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-40 bg-white/10 border-white/20 text-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {SORT_OPTIONS.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Sort Order */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortOrder(prev => prev === 'desc' ? 'asc' : 'desc')}
                className="border-white/20 text-white hover:bg-white/10"
              >
                {sortOrder === 'desc' ? <SortDesc className="h-4 w-4" /> : <SortAsc className="h-4 w-4" />}
              </Button>

              {/* View Mode */}
              <div className="flex border border-white/20 rounded-md overflow-hidden">
                <Button
                  variant={viewMode === 'grid' ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className={viewMode === 'grid' ? "bg-purple-600" : "text-white hover:bg-white/10"}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className={viewMode === 'list' ? "bg-purple-600" : "text-white hover:bg-white/10"}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        {state.loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {Array.from({ length: 8 }).map((_, i) => (
              <Card key={i} className="bg-white/10 border-white/20 animate-pulse">
                <CardContent className="p-4">
                  <div className="aspect-[2/3] bg-white/20 rounded-lg mb-3"></div>
                  <div className="h-4 bg-white/20 rounded mb-2"></div>
                  <div className="h-3 bg-white/20 rounded w-2/3"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredItems.length === 0 ? (
          <Card className="bg-white/10 border-white/20 text-white">
            <CardContent className="p-12 text-center">
              <Film className="h-16 w-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">
                {filters.status === 'all' ? 'No items in watchlist' : `No ${filters.status.replace('_', ' ')} items`}
              </h3>
              <p className="text-gray-300 mb-6">
                {filters.status === 'all' 
                  ? 'Start building your watchlist by discovering content!'
                  : 'Try changing your filters or add some content to your watchlist.'
                }
              </p>
              <div className="flex gap-2 justify-center">
                <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
                  <Link href="/feed">Discover Content</Link>
                </Button>
                {filters.status !== 'all' && (
                  <Button 
                    variant="outline" 
                    onClick={() => setFilters(prev => ({ ...prev, status: 'all' }))}
                    className="border-white/20 text-white hover:bg-white/10"
                  >
                    View All Items
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ) : (
          <div className={viewMode === 'grid' 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
            : "space-y-4"
          }>
            {filteredItems.map((item) => (
              <WatchlistItemCard
                key={item.id}
                item={item}
                viewMode={viewMode}
                onRemove={removeFromWatchlist}
                onUpdateStatus={updateStatus}
                onUpdateRating={updateRating}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

interface WatchlistItemCardProps {
  item: any
  viewMode: 'grid' | 'list'
  onRemove: (id: string, title: string) => Promise<boolean>
  onUpdateStatus: (id: string, status: string, title?: string) => Promise<boolean>
  onUpdateRating: (id: string, rating: number, title?: string) => Promise<boolean>
}

function WatchlistItemCard({ item, viewMode, onRemove, onUpdateStatus, onUpdateRating }: WatchlistItemCardProps) {
  const [showActions, setShowActions] = useState(false)
  const [rating, setRating] = useState(item.user_rating || 0)

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'watching': return 'bg-green-600'
      case 'completed': return 'bg-blue-600'
      case 'on_hold': return 'bg-yellow-600'
      case 'dropped': return 'bg-red-600'
      default: return 'bg-gray-600'
    }
  }

  const getStatusLabel = (status: string) => {
    return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())
  }

  if (viewMode === 'list') {
    return (
      <Card className="bg-white/10 border-white/20 text-white hover:bg-white/15 transition-colors">
        <CardContent className="p-4">
          <div className="flex items-center gap-4">
            {/* Poster */}
            <div className="w-16 h-24 bg-gradient-to-br from-purple-600 to-purple-800 rounded-lg overflow-hidden flex-shrink-0">
              {item.poster_path && (
                <img
                  src={`https://image.tmdb.org/t/p/w200${item.poster_path}`}
                  alt={item.title}
                  className="w-full h-full object-cover"
                />
              )}
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-white truncate">{item.title}</h3>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="secondary" className={`${getStatusColor(item.status)} text-white`}>
                      {getStatusLabel(item.status)}
                    </Badge>
                    <Badge variant="outline" className="border-white/20 text-white">
                      {item.content_type === 'movie' ? 'Movie' : 'TV Show'}
                    </Badge>
                  </div>

                  {/* Progress */}
                  {item.status === 'watching' && (
                    <div className="mt-2">
                      <div className="flex items-center justify-between text-xs mb-1">
                        <span>{Math.round(item.progress_percentage)}% complete</span>
                        {item.content_type === 'tv' && item.current_season && (
                          <span>S{item.current_season}E{item.current_episode || 1}</span>
                        )}
                      </div>
                      <Progress value={item.progress_percentage} className="h-1" />
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2 ml-4">
                  {item.user_rating && (
                    <div className="flex items-center gap-1 text-yellow-400">
                      <Star className="h-4 w-4 fill-current" />
                      <span className="text-sm">{item.user_rating}</span>
                    </div>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onRemove(item.id, item.title)}
                    className="border-white/20 text-white hover:bg-red-600/20"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Grid view
  return (
    <Card className="bg-white/10 border-white/20 text-white hover:bg-white/15 transition-colors group">
      <CardContent className="p-4">
        {/* Poster */}
        <div className="relative aspect-[2/3] bg-gradient-to-br from-purple-600 to-purple-800 rounded-lg mb-3 overflow-hidden">
          {item.poster_path && (
            <img
              src={`https://image.tmdb.org/t/p/w500${item.poster_path}`}
              alt={item.title}
              className="w-full h-full object-cover"
            />
          )}

          {/* Status badge */}
          <div className="absolute top-2 left-2">
            <Badge className={`${getStatusColor(item.status)} text-white text-xs`}>
              {getStatusLabel(item.status)}
            </Badge>
          </div>

          {/* Progress overlay for watching items */}
          {item.status === 'watching' && (
            <div className="absolute bottom-0 left-0 right-0 bg-black/60 p-2">
              <div className="flex items-center justify-between text-xs mb-1">
                <span>{Math.round(item.progress_percentage)}% complete</span>
                {item.content_type === 'tv' && item.current_season && (
                  <span>S{item.current_season}E{item.current_episode || 1}</span>
                )}
              </div>
              <Progress value={item.progress_percentage} className="h-1" />
            </div>
          )}
        </div>

        {/* Content Info */}
        <div className="space-y-2">
          <h3 className="font-semibold text-white truncate" title={item.title}>
            {item.title}
          </h3>

          <div className="flex items-center justify-between">
            <Badge variant="outline" className="border-white/20 text-white text-xs">
              {item.content_type === 'movie' ? 'Movie' : 'TV Show'}
            </Badge>
            {item.user_rating && (
              <div className="flex items-center gap-1 text-yellow-400">
                <Star className="h-3 w-3 fill-current" />
                <span className="text-xs">{item.user_rating}</span>
              </div>
            )}
          </div>

          {/* Quick Actions */}
          <div className="flex gap-1 pt-2 opacity-0 group-hover:opacity-100 transition-opacity">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onRemove(item.id, item.title)}
              className="flex-1 border-white/20 text-white hover:bg-red-600/20 text-xs"
            >
              <Trash2 className="h-3 w-3 mr-1" />
              Remove
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

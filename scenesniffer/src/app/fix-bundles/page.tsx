'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function FixBundlesPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)

  const setupTables = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      const response = await fetch('/api/setup-bundle-tables', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({ 
        success: false, 
        error: 'Failed to call API',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setLoading(false)
    }
  }

  const testBundlesAPI = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      const response = await fetch('/api/bundles?platform=youtube&status=active&limit=5')
      const data = await response.json()
      setResult({ 
        success: response.ok,
        message: 'Bundles API test',
        data 
      })
    } catch (error) {
      setResult({ 
        success: false, 
        error: 'Failed to test bundles API',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setLoading(false)
    }
  }

  const seedSampleBundles = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      const response = await fetch('/api/bundles/seed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      const data = await response.json()
      setResult(data)
    } catch (error) {
      setResult({ 
        success: false, 
        error: 'Failed to seed bundles',
        details: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <Card className="bg-white/10 border-white/20 text-white mb-8">
          <CardHeader>
            <CardTitle className="text-2xl">🔧 Fix Bundle Database Issues</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-gray-300">
              This page helps fix the bundle database relationship issues.
            </p>
            
            <div className="grid md:grid-cols-3 gap-4">
              <Button
                onClick={setupTables}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {loading ? 'Setting up...' : '1. Setup Tables'}
              </Button>
              
              <Button
                onClick={seedSampleBundles}
                disabled={loading}
                className="bg-green-600 hover:bg-green-700"
              >
                {loading ? 'Seeding...' : '2. Seed Sample Data'}
              </Button>
              
              <Button
                onClick={testBundlesAPI}
                disabled={loading}
                className="bg-purple-600 hover:bg-purple-700"
              >
                {loading ? 'Testing...' : '3. Test API'}
              </Button>
            </div>
          </CardContent>
        </Card>

        {result && (
          <Card className={`border-2 ${result.success ? 'border-green-500 bg-green-900/20' : 'border-red-500 bg-red-900/20'}`}>
            <CardHeader>
              <CardTitle className={result.success ? 'text-green-300' : 'text-red-300'}>
                {result.success ? '✅ Success' : '❌ Error'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="text-sm text-gray-300 whitespace-pre-wrap overflow-auto">
                {JSON.stringify(result, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}

        <Card className="bg-white/10 border-white/20 text-white mt-8">
          <CardHeader>
            <CardTitle>📋 Instructions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <p><strong>Step 1:</strong> Click "Setup Tables" to create the bundle database tables</p>
            <p><strong>Step 2:</strong> Click "Seed Sample Data" to create test bundles</p>
            <p><strong>Step 3:</strong> Click "Test API" to verify everything is working</p>
            <p className="text-yellow-300 text-sm mt-4">
              <strong>Note:</strong> If you get foreign key relationship errors, the tables need to be created first.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

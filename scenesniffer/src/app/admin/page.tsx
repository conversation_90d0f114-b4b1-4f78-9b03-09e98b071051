'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Loader2, Database, Users, RefreshCw, Settings, BarChart3, AlertCircle, CheckCircle, Tags } from 'lucide-react'
import GenreManager from '@/components/admin/GenreManager'

interface OperationResult {
  success: boolean
  message: string
  processed?: number
  errors?: number
  total?: number
  dryRun?: boolean
}

interface SystemStats {
  content: {
    total: number
    aiSummaryCompletion: number
    tagsCompletion: number
    referencedTitlesCompletion: number
  }
  creators: {
    total: number
    genreCompletion: number
  }
  users: {
    total: number
  }
}

export default function AdminPage() {
  const [loading, setLoading] = useState<string | null>(null)
  const [results, setResults] = useState<{ [key: string]: OperationResult }>({})
  const [stats, setStats] = useState<SystemStats | null>(null)
  const [statsLoading, setStatsLoading] = useState(true)
  const [showGenreManager, setShowGenreManager] = useState(false)

  // Load system stats on component mount
  useEffect(() => {
    const loadStats = async () => {
      try {
        const response = await fetch('/api/admin/status')
        const data = await response.json()
        if (data.success) {
          setStats(data.stats)
        }
      } catch (error) {
        console.error('Failed to load stats:', error)
      } finally {
        setStatsLoading(false)
      }
    }
    loadStats()
  }, [])

  const runOperation = async (operation: string, endpoint: string, params: string = '') => {
    setLoading(operation)
    try {
      const response = await fetch(`${endpoint}${params}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      const data = await response.json()
      setResults(prev => ({ ...prev, [operation]: data }))
    } catch (error) {
      setResults(prev => ({ 
        ...prev, 
        [operation]: { 
          success: false, 
          message: error instanceof Error ? error.message : 'Unknown error' 
        } 
      }))
    } finally {
      setLoading(null)
    }
  }

  const operations = [
    {
      id: 'content-backfill-dry',
      title: 'Content Backfill (Dry Run)',
      description: 'Test content AI processing without making changes',
      icon: Database,
      color: 'bg-blue-500',
      endpoint: '/api/content/backfill',
      params: '?limit=10&dryRun=true'
    },
    {
      id: 'content-backfill',
      title: 'Content Backfill (Live)',
      description: 'Process all content missing AI data',
      icon: Database,
      color: 'bg-green-500',
      endpoint: '/api/content/backfill',
      params: '?limit=50&dryRun=false'
    },
    {
      id: 'creator-genres-dry',
      title: 'Creator Genres (Dry Run)',
      description: 'Test creator genre assignment without changes',
      icon: Users,
      color: 'bg-purple-500',
      endpoint: '/api/creators/assign-genres',
      params: '?dryRun=true'
    },
    {
      id: 'creator-genres',
      title: 'Creator Genres (Live)',
      description: 'Assign genres to all creators',
      icon: Users,
      color: 'bg-orange-500',
      endpoint: '/api/creators/assign-genres',
      params: '?dryRun=false'
    },
    {
      id: 'content-aggregate',
      title: 'Content Aggregation',
      description: 'Fetch new content from YouTube creators',
      icon: RefreshCw,
      color: 'bg-indigo-500',
      endpoint: '/api/content/aggregate',
      params: ''
    },
    {
      id: 'genre-manager',
      title: 'Genre Management',
      description: 'Manage genres used across creators and content',
      icon: Tags,
      color: 'bg-pink-500',
      endpoint: '',
      params: '',
      isSpecial: true
    }
  ]

  const getResultBadge = (result: OperationResult) => {
    if (result.success) {
      return (
        <Badge className="bg-green-100 text-green-800 border-green-200">
          <CheckCircle className="h-3 w-3 mr-1" />
          Success
        </Badge>
      )
    } else {
      return (
        <Badge className="bg-red-100 text-red-800 border-red-200">
          <AlertCircle className="h-3 w-3 mr-1" />
          Error
        </Badge>
      )
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">SceneSniffer Admin</h1>
          <p className="text-gray-300">Manage content processing, creator data, and system operations</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card className="bg-white/10 border-white/20 text-white">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Database className="h-8 w-8 text-blue-400" />
                <div>
                  <div className="text-2xl font-bold">{statsLoading ? '...' : stats?.content.total || 0}</div>
                  <div className="text-sm text-gray-300">Content Items</div>
                  {stats && (
                    <div className="text-xs text-blue-300">
                      AI: {stats.content.aiSummaryCompletion}% | Tags: {stats.content.tagsCompletion}%
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 border-white/20 text-white">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Users className="h-8 w-8 text-purple-400" />
                <div>
                  <div className="text-2xl font-bold">{statsLoading ? '...' : stats?.creators.total || 0}</div>
                  <div className="text-sm text-gray-300">Creators</div>
                  {stats && (
                    <div className="text-xs text-purple-300">
                      Genres: {stats.creators.genreCompletion}%
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 border-white/20 text-white">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <RefreshCw className="h-8 w-8 text-green-400" />
                <div>
                  <div className="text-2xl font-bold">{statsLoading ? '...' : stats?.users.total || 0}</div>
                  <div className="text-sm text-gray-300">Active Users</div>
                  <div className="text-xs text-green-300">System Active</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 border-white/20 text-white">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <BarChart3 className="h-8 w-8 text-orange-400" />
                <div>
                  <div className="text-2xl font-bold">
                    {statsLoading ? '...' : (stats && stats.content.aiSummaryCompletion > 80 && stats.creators.genreCompletion > 80) ? '✅' : '⚠️'}
                  </div>
                  <div className="text-sm text-gray-300">System Health</div>
                  <div className="text-xs text-orange-300">
                    {statsLoading ? 'Loading...' : (stats && stats.content.aiSummaryCompletion > 80 && stats.creators.genreCompletion > 80) ? 'Healthy' : 'Needs Attention'}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Operations */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {operations.map((op) => {
            const Icon = op.icon
            const result = results[op.id]
            const isLoading = loading === op.id

            return (
              <Card key={op.id} className="bg-white/10 border-white/20 text-white">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${op.color}`}>
                      <Icon className="h-5 w-5 text-white" />
                    </div>
                    {op.title}
                  </CardTitle>
                  <p className="text-gray-300 text-sm">{op.description}</p>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Button
                      onClick={() => {
                        if (op.id === 'genre-manager') {
                          setShowGenreManager(true)
                        } else {
                          runOperation(op.id, op.endpoint, op.params)
                        }
                      }}
                      disabled={isLoading}
                      className="w-full bg-white/20 hover:bg-white/30 text-white border-white/30"
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Running...
                        </>
                      ) : (
                        op.id === 'genre-manager' ? 'Open Genre Manager' : 'Run Operation'
                      )}
                    </Button>

                    {result && (
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Status:</span>
                          {getResultBadge(result)}
                        </div>
                        
                        <div className="text-sm text-gray-300">
                          <div>{result.message}</div>
                          {result.processed !== undefined && (
                            <div className="mt-1">
                              Processed: {result.processed} | Errors: {result.errors} | Total: {result.total}
                              {result.dryRun && <span className="text-yellow-400"> (Dry Run)</span>}
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Quick Actions */}
        <Card className="mt-8 bg-white/10 border-white/20 text-white">
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <Settings className="h-5 w-5" />
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button
                variant="outline"
                className="text-white border-white/30 hover:bg-white/10"
                onClick={() => window.location.href = '/feed'}
              >
                View Feed
              </Button>
              <Button
                variant="outline"
                className="text-white border-white/30 hover:bg-white/10"
                onClick={() => window.location.href = '/'}
              >
                Back to Home
              </Button>
              <Button
                variant="outline"
                className="text-white border-white/30 hover:bg-white/10"
                onClick={() => setResults({})}
              >
                Clear Results
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Genre Manager Modal */}
        {showGenreManager && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
            <div className="bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto p-6">
              <GenreManager onClose={() => setShowGenreManager(false)} />
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

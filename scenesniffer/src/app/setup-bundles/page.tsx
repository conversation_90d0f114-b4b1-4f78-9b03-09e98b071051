'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Database, Users, Package, Loader2, CheckCircle, XCircle, AlertCircle } from 'lucide-react'

export default function SetupBundlesPage() {
  const [setupSteps, setSetupSteps] = useState({
    creators: { status: 'pending', result: null },
    tables: { status: 'pending', result: null },
    bundles: { status: 'pending', result: null }
  })
  const [currentStep, setCurrentStep] = useState<string | null>(null)

  const updateStepStatus = (step: string, status: string, result: any = null) => {
    setSetupSteps(prev => ({
      ...prev,
      [step]: { status, result }
    }))
  }

  const runSetupStep = async (step: string, endpoint: string, description: string) => {
    setCurrentStep(step)
    updateStepStatus(step, 'running')
    
    try {
      console.log(`🔧 ${description}...`)
      
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
      
      const data = await response.json()
      
      if (data.success) {
        updateStepStatus(step, 'success', data)
        console.log(`✅ ${description} completed`)
      } else {
        updateStepStatus(step, 'failed', data)
        console.error(`❌ ${description} failed:`, data.error)
      }
    } catch (error) {
      console.error(`Error in ${step}:`, error)
      updateStepStatus(step, 'failed', { error: error instanceof Error ? error.message : 'Unknown error' })
    } finally {
      setCurrentStep(null)
    }
  }

  const runFullSetup = async () => {
    // Step 1: Seed sample creators
    await runSetupStep('creators', '/api/seed-sample-creators', 'Seeding sample creators')
    
    // Step 2: Test table creation (this will fail if tables don't exist)
    await runSetupStep('tables', '/api/migrate-bundle-tables', 'Testing bundle tables')
    
    // Step 3: Generate AI bundles (only if previous steps succeeded)
    if (setupSteps.creators.status === 'success') {
      await runSetupStep('bundles', '/api/bundles/generate', 'Generating AI bundles')
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="h-5 w-5 text-green-400" />
      case 'failed': return <XCircle className="h-5 w-5 text-red-400" />
      case 'running': return <Loader2 className="h-5 w-5 text-blue-400 animate-spin" />
      default: return <AlertCircle className="h-5 w-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-green-600/20 border-green-400/30 text-green-300'
      case 'failed': return 'bg-red-600/20 border-red-400/30 text-red-300'
      case 'running': return 'bg-blue-600/20 border-blue-400/30 text-blue-300'
      default: return 'bg-gray-600/20 border-gray-400/30 text-gray-300'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <header className="border-b border-white/20 bg-black/20 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center gap-3">
            <Database className="h-8 w-8 text-purple-400" />
            <div>
              <h1 className="text-2xl font-bold text-white">Bundle System Setup</h1>
              <p className="text-gray-300 text-sm">Initialize the AI-powered creator bundle system</p>
            </div>
            <Badge className="bg-purple-600/20 text-purple-300">SETUP</Badge>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Setup Instructions */}
        <Card className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 border-purple-400/30 mb-8">
          <CardContent className="p-6">
            <h3 className="text-xl font-semibold text-white mb-4">🚀 Bundle System Setup</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-purple-300 mb-2">📋 Setup Steps:</h4>
                <ol className="text-gray-300 text-sm space-y-1 list-decimal list-inside">
                  <li>Create sample creators with content for AI curation</li>
                  <li>Verify bundle database tables exist</li>
                  <li>Generate AI-curated bundles automatically</li>
                  <li>Test bundle functionality and UI</li>
                </ol>
              </div>
              <div>
                <h4 className="font-medium text-purple-300 mb-2">⚠️ Prerequisites:</h4>
                <ul className="text-gray-300 text-sm space-y-1">
                  <li>• Supabase database must be running</li>
                  <li>• Bundle tables must exist (create manually if needed)</li>
                  <li>• Service role key must be configured</li>
                  <li>• AI curation engine must be functional</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Manual Table Creation Instructions */}
        <Card className="bg-yellow-600/20 border-yellow-400/30 mb-8">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-yellow-300 mb-4">📝 Manual Table Creation Required</h3>
            <p className="text-gray-300 mb-4">
              If the setup fails due to missing tables, you'll need to create them manually in your Supabase dashboard.
              Run this SQL in the Supabase SQL editor:
            </p>
            <div className="bg-black/30 p-4 rounded text-sm text-gray-300 font-mono overflow-x-auto">
              <pre>{`-- Creator Bundles Table
CREATE TABLE public.creator_bundles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  tags TEXT[] DEFAULT '{}',
  platform VARCHAR(50) NOT NULL,
  creator_count INTEGER DEFAULT 0,
  refreshed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ttl INTEGER DEFAULT 604800,
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bundle Creators Relationship Table  
CREATE TABLE public.bundle_creators (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  bundle_id UUID REFERENCES public.creator_bundles(id) ON DELETE CASCADE,
  creator_id UUID REFERENCES public.creators(id) ON DELETE CASCADE,
  position INTEGER DEFAULT 0,
  score DECIMAL(5,2) DEFAULT 0.0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(bundle_id, creator_id)
);

-- User Bundle Subscriptions Table
CREATE TABLE public.user_bundles (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  bundle_id UUID REFERENCES public.creator_bundles(id) ON DELETE CASCADE,
  added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  included_creator_ids UUID[] DEFAULT '{}',
  UNIQUE(user_id, bundle_id)
);`}</pre>
            </div>
          </CardContent>
        </Card>

        {/* Setup Controls */}
        <Card className="bg-white/10 border-white/20 text-white mb-8">
          <CardHeader>
            <CardTitle>Setup Controls</CardTitle>
            <CardDescription className="text-gray-300">
              Run the complete setup process or individual steps
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4 mb-6">
              <Button
                onClick={runFullSetup}
                disabled={currentStep !== null}
                className="bg-purple-600 hover:bg-purple-700"
              >
                {currentStep ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Running Setup...
                  </>
                ) : (
                  <>
                    <Database className="mr-2 h-4 w-4" />
                    Run Full Setup
                  </>
                )}
              </Button>
              
              <Button
                onClick={() => runSetupStep('creators', '/api/seed-sample-creators', 'Seeding sample creators')}
                disabled={currentStep !== null}
                variant="outline"
                className="text-white border-white/30 hover:bg-white/10"
              >
                <Users className="mr-2 h-4 w-4" />
                Seed Creators Only
              </Button>
              
              <Button
                onClick={() => runSetupStep('bundles', '/api/bundles/generate', 'Generating AI bundles')}
                disabled={currentStep !== null}
                variant="outline"
                className="text-white border-white/30 hover:bg-white/10"
              >
                <Package className="mr-2 h-4 w-4" />
                Generate Bundles Only
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Setup Status */}
        <div className="grid gap-6">
          {/* Step 1: Sample Creators */}
          <Card className={`${getStatusColor(setupSteps.creators.status)}`}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  {getStatusIcon(setupSteps.creators.status)}
                  <h3 className="text-lg font-semibold">Step 1: Sample Creators</h3>
                </div>
                <Badge className="bg-black/20">
                  {setupSteps.creators.status}
                </Badge>
              </div>
              
              <p className="text-sm mb-4">
                Create sample creators with content for AI curation testing
              </p>
              
              {setupSteps.creators.result && (
                <div className="text-sm">
                  {setupSteps.creators.status === 'success' ? (
                    <div>
                      ✅ Created {setupSteps.creators.result.stats?.creators_created || 0} creators 
                      with {setupSteps.creators.result.stats?.content_created || 0} content pieces
                    </div>
                  ) : (
                    <div>❌ {setupSteps.creators.result.error}</div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Step 2: Database Tables */}
          <Card className={`${getStatusColor(setupSteps.tables.status)}`}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  {getStatusIcon(setupSteps.tables.status)}
                  <h3 className="text-lg font-semibold">Step 2: Database Tables</h3>
                </div>
                <Badge className="bg-black/20">
                  {setupSteps.tables.status}
                </Badge>
              </div>
              
              <p className="text-sm mb-4">
                Verify that bundle database tables exist and are accessible
              </p>
              
              {setupSteps.tables.result && (
                <div className="text-sm">
                  {setupSteps.tables.status === 'success' ? (
                    <div>✅ Bundle tables verified and working</div>
                  ) : (
                    <div>
                      ❌ {setupSteps.tables.result.error}
                      <br />
                      <span className="text-xs">Please create tables manually using the SQL above</span>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Step 3: AI Bundles */}
          <Card className={`${getStatusColor(setupSteps.bundles.status)}`}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  {getStatusIcon(setupSteps.bundles.status)}
                  <h3 className="text-lg font-semibold">Step 3: AI Bundle Generation</h3>
                </div>
                <Badge className="bg-black/20">
                  {setupSteps.bundles.status}
                </Badge>
              </div>
              
              <p className="text-sm mb-4">
                Generate themed creator bundles using AI curation
              </p>
              
              {setupSteps.bundles.result && (
                <div className="text-sm">
                  {setupSteps.bundles.status === 'success' ? (
                    <div>
                      ✅ Generated {setupSteps.bundles.result.stats?.bundles_generated || 0} bundles 
                      with {setupSteps.bundles.result.stats?.total_creators_added || 0} total creators
                    </div>
                  ) : (
                    <div>❌ {setupSteps.bundles.result.error}</div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Next Steps */}
        {setupSteps.creators.status === 'success' && setupSteps.bundles.status === 'success' && (
          <Card className="bg-green-600/20 border-green-400/30 mt-8">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-green-300 mb-4">🎉 Setup Complete!</h3>
              <p className="text-gray-300 mb-4">
                Your bundle system is now ready. You can test it using these pages:
              </p>
              <div className="flex gap-4">
                <Button
                  onClick={() => window.open('/bundles-demo', '_blank')}
                  className="bg-green-600 hover:bg-green-700"
                >
                  Test Bundle UI
                </Button>
                <Button
                  onClick={() => window.open('/ai-curation-demo', '_blank')}
                  variant="outline"
                  className="text-green-300 border-green-400/30 hover:bg-green-600/10"
                >
                  Test AI Curation
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

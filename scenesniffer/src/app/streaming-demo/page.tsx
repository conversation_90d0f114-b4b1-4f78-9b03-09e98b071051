'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { StreamingAvailabilityDisplay, StreamingBadges } from '@/components/ui/streaming-availability'
import { Loader2, Play, ExternalLink, Star, Users, Clock } from 'lucide-react'

interface ContentWithStreaming {
  id: string
  title: string
  description: string
  content_type: string
  ai_summary: string
  referenced_titles: string[]
  published_at: string
  creators: {
    name: string
    verified: boolean
  }
  streamingData?: any[]
}

export default function StreamingDemoPage() {
  const [content, setContent] = useState<ContentWithStreaming[]>([])
  const [loading, setLoading] = useState(false)
  const [processingStreaming, setProcessingStreaming] = useState(false)

  // Mock user streaming services
  const userServices = ['netflix', 'disney', 'hbo', 'amazon']

  const loadContent = async () => {
    setLoading(true)
    try {
      // Get AI-enhanced content
      const response = await fetch('/api/verify-ai-content')
      const data = await response.json()
      
      if (data.success && data.aiEnhancedContent?.items) {
        setContent(data.aiEnhancedContent.items.map((item: any) => ({
          id: Math.random().toString(),
          title: item.title,
          description: item.originalDescription,
          content_type: item.contentType,
          ai_summary: item.aiSummary,
          referenced_titles: item.referencedTitles || [],
          published_at: new Date().toISOString(),
          creators: {
            name: item.creator,
            verified: true
          }
        })))
      }
    } catch (error) {
      console.error('Error loading content:', error)
    } finally {
      setLoading(false)
    }
  }

  const processStreamingData = async () => {
    setProcessingStreaming(true)
    try {
      // Process each content item for streaming data
      const updatedContent = []
      
      for (const item of content) {
        if (item.referenced_titles && item.referenced_titles.length > 0) {
          // Call streaming service for each item
          const streamingResponse = await fetch('/api/test-streaming-enhanced', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
          })
          
          const streamingData = await streamingResponse.json()
          
          // Find matching result for this content
          const matchingResult = streamingData.results?.find((r: any) => 
            r.contentTitle === item.title
          )
          
          updatedContent.push({
            ...item,
            streamingData: matchingResult?.allStreaming || []
          })
        } else {
          updatedContent.push(item)
        }
      }
      
      setContent(updatedContent)
    } catch (error) {
      console.error('Error processing streaming data:', error)
    } finally {
      setProcessingStreaming(false)
    }
  }

  useEffect(() => {
    loadContent()
  }, [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            🎬 Streaming Availability Demo
          </h1>
          <p className="text-gray-300 text-lg max-w-2xl mx-auto">
            See how SceneSniffer automatically finds where to watch movies and TV shows 
            mentioned in your favorite creators' content.
          </p>
        </div>

        {/* User Services */}
        <Card className="bg-white/10 border-white/20 text-white mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              📺 Your Streaming Services
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {userServices.map(service => (
                <Badge key={service} className="bg-purple-600/20 text-purple-300 border-purple-400/30">
                  {service.charAt(0).toUpperCase() + service.slice(1)}
                </Badge>
              ))}
            </div>
            <p className="text-gray-300 text-sm mt-2">
              We'll prioritize showing availability on these services.
            </p>
          </CardContent>
        </Card>

        {/* Controls */}
        <div className="flex gap-4 mb-8 justify-center">
          <Button
            onClick={loadContent}
            disabled={loading}
            className="bg-purple-600 hover:bg-purple-700"
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Load AI-Enhanced Content
          </Button>
          
          <Button
            onClick={processStreamingData}
            disabled={processingStreaming || content.length === 0}
            variant="outline"
            className="text-white border-white/30 hover:bg-white/10"
          >
            {processingStreaming && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Find Streaming Availability
          </Button>
        </div>

        {/* Content Feed with Streaming */}
        {content.length > 0 ? (
          <div className="space-y-6">
            {content.map((item) => (
              <Card key={item.id} className="bg-white/10 border-white/20 text-white">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Content Header */}
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="text-xl font-semibold text-white mb-2">
                          {item.title}
                        </h3>
                        
                        <div className="flex items-center gap-4 mb-3 text-sm text-gray-300">
                          <div className="flex items-center gap-1">
                            <Users className="h-4 w-4" />
                            <span>{item.creators.name}</span>
                            {item.creators.verified && <span className="text-blue-400">✓</span>}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            <span>Recent</span>
                          </div>
                          <Badge className="bg-purple-600/20 text-purple-300">
                            {item.content_type}
                          </Badge>
                        </div>
                      </div>
                      
                      {/* Streaming Badges */}
                      {item.streamingData && (
                        <div className="ml-4">
                          <StreamingBadges streamingData={item.streamingData} />
                        </div>
                      )}
                    </div>

                    {/* AI Summary */}
                    <p className="text-gray-300 text-sm">
                      {item.ai_summary}
                    </p>

                    {/* Referenced Titles */}
                    {item.referenced_titles && item.referenced_titles.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-purple-300 mb-2">
                          🎬 Movies & Shows Mentioned:
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {item.referenced_titles.map((title, index) => (
                            <Badge 
                              key={index}
                              variant="outline"
                              className="bg-blue-600/20 text-blue-300 border-blue-400/30"
                            >
                              {title}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Streaming Availability */}
                    {item.streamingData && item.streamingData.length > 0 && (
                      <div>
                        <h4 className="text-sm font-medium text-green-300 mb-3">
                          📺 Where to Watch:
                        </h4>
                        <StreamingAvailabilityDisplay 
                          streamingData={item.streamingData}
                          compact={true}
                        />
                      </div>
                    )}

                    {/* No Streaming Data */}
                    {item.referenced_titles && item.referenced_titles.length > 0 && 
                     (!item.streamingData || item.streamingData.length === 0) && (
                      <div className="bg-yellow-600/10 border border-yellow-400/20 rounded-lg p-3">
                        <p className="text-yellow-300 text-sm">
                          💡 Click "Find Streaming Availability" to see where these titles are available to watch.
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 border-purple-400/30">
            <CardContent className="p-8 text-center">
              <div className="text-6xl mb-4">🎬</div>
              <h3 className="text-xl font-semibold text-white mb-2">
                Ready to Demo Streaming Integration
              </h3>
              <p className="text-gray-300 mb-4">
                Click "Load AI-Enhanced Content" to see content with referenced movies and TV shows.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

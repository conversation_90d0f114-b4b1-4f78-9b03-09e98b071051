'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/providers/auth-provider'
import { AuthButton } from '@/components/auth/auth-button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Film, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export default function AuthPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [mode, setMode] = useState<'signin' | 'signup'>('signin')

  useEffect(() => {
    const checkUserStatus = async () => {
      if (!loading && user) {
        // Check if user has completed onboarding
        try {
          const response = await fetch('/api/user/preferences')
          if (response.ok) {
            const data = await response.json()
            if (data.preferences && (data.preferences.genres?.length > 0 || data.preferences.streaming_services?.length > 0)) {
              // User has preferences - redirect to feed
              router.push('/feed')
            } else {
              // User needs onboarding
              router.push('/onboarding')
            }
          } else {
            // Error fetching preferences or no preferences - go to onboarding
            router.push('/onboarding')
          }
        } catch (error) {
          console.error('Error checking user preferences:', error)
          // Fallback to onboarding
          router.push('/onboarding')
        }
      }
    }

    checkUserStatus()
  }, [user, loading, router])

  const handleAuthSuccess = async () => {
    // Let the useEffect handle the redirect after auth state updates
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
      </div>
    )
  }

  if (user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <Link href="/">
            <Button variant="outline" className="text-white border-white/50 hover:bg-white hover:text-black bg-transparent">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Home
            </Button>
          </Link>
          
          <div className="flex items-center gap-2">
            <Film className="h-8 w-8 text-purple-400" />
            <span className="text-2xl font-bold text-white">SceneSniffer</span>
          </div>
        </div>

        <div className="max-w-6xl mx-auto grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Side - Marketing Content */}
          <div className="space-y-8">
            <div>
              <h1 className="text-4xl lg:text-5xl font-bold text-white mb-4">
                Your Cinematic Pulse Awaits
              </h1>
              <p className="text-xl text-gray-300 mb-6">
                Join thousands of movie and TV enthusiasts who trust SceneSniffer for personalized content discovery.
              </p>
            </div>

            <div className="space-y-6">
              <div className="flex items-start gap-4">
                <div className="bg-purple-600 rounded-full p-2">
                  <Film className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-1">Creator-First Discovery</h3>
                  <p className="text-gray-300">
                    Follow your favorite YouTube, Instagram, and Twitter creators. Get recommendations from voices you trust.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="bg-purple-600 rounded-full p-2">
                  <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-1">AI-Powered Insights</h3>
                  <p className="text-gray-300">
                    Smart summaries, automatic content categorization, and spoiler-free recommendations powered by AI.
                  </p>
                </div>
              </div>

              <div className="flex items-start gap-4">
                <div className="bg-purple-600 rounded-full p-2">
                  <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-white mb-1">Streaming Intelligence</h3>
                  <p className="text-gray-300">
                    Real-time "Where to Watch" information across all major platforms. Never miss when content becomes available.
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white/5 border border-white/20 rounded-lg p-6">
              <div className="flex items-center gap-3 mb-3">
                <div className="flex -space-x-2">
                  <div className="w-8 h-8 bg-purple-500 rounded-full border-2 border-white"></div>
                  <div className="w-8 h-8 bg-blue-500 rounded-full border-2 border-white"></div>
                  <div className="w-8 h-8 bg-green-500 rounded-full border-2 border-white"></div>
                </div>
                <span className="text-white font-medium">Join 10,000+ users</span>
              </div>
              <p className="text-gray-300 text-sm">
                "SceneSniffer has completely changed how I discover new shows. The creator recommendations are spot-on!" 
                <span className="text-purple-400">- Sarah M.</span>
              </p>
            </div>
          </div>

          {/* Right Side - Auth Form */}
          <div className="flex flex-col items-center">
            <div className="w-full max-w-md">
              <div className="text-center mb-6">
                <div className="flex justify-center gap-2 mb-4">
                  <Button
                    variant={mode === 'signin' ? 'default' : 'outline'}
                    onClick={() => setMode('signin')}
                    className={mode === 'signin' ? 'bg-purple-600 hover:bg-purple-700 text-white' : 'border-white/50 text-white hover:bg-white/10 bg-transparent'}
                  >
                    Sign In
                  </Button>
                  <Button
                    variant={mode === 'signup' ? 'default' : 'outline'}
                    onClick={() => setMode('signup')}
                    className={mode === 'signup' ? 'bg-purple-600 hover:bg-purple-700 text-white' : 'border-white/50 text-white hover:bg-white/10 bg-transparent'}
                  >
                    Sign Up
                  </Button>
                </div>
              </div>

              <AuthButton mode={mode} onSuccess={handleAuthSuccess} />

              <div className="mt-6 text-center">
                <p className="text-gray-400 text-sm">
                  By continuing, you agree to our{' '}
                  <Link href="/terms" className="text-purple-400 hover:underline">
                    Terms of Service
                  </Link>{' '}
                  and{' '}
                  <Link href="/privacy" className="text-purple-400 hover:underline">
                    Privacy Policy
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

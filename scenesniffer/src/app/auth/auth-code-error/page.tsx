import Link from 'next/link'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertCircle, ArrowLeft } from 'lucide-react'

export default function AuthCodeErrorPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <AlertCircle className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-xl">Authentication Error</CardTitle>
          <CardDescription>
            There was a problem setting up your account. This might be a temporary issue.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-sm text-gray-600 space-y-2">
            <p>Possible causes:</p>
            <ul className="list-disc list-inside space-y-1 ml-2">
              <li>Network connectivity issue</li>
              <li>Database temporarily unavailable</li>
              <li>Invalid authentication code</li>
            </ul>
          </div>
          
          <div className="space-y-3">
            <Link href="/auth" className="block">
              <Button className="w-full bg-purple-600 hover:bg-purple-700">
                Try Again
              </Button>
            </Link>
            
            <Link href="/" className="block">
              <Button variant="outline" className="w-full">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Home
              </Button>
            </Link>
          </div>
          
          <div className="text-xs text-gray-500 text-center">
            If this problem persists, please contact support.
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

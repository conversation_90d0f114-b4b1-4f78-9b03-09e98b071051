'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Brain, Sparkles, Users, TrendingUp, Clock, Database, Loader2, RefreshCw, Zap } from 'lucide-react'

export default function AICurationDemoPage() {
  const [generating, setGenerating] = useState(false)
  const [refreshing, setRefreshing] = useState(false)
  const [generationResult, setGenerationResult] = useState<any>(null)
  const [refreshResult, setRefreshResult] = useState<any>(null)

  const handleGenerateBundles = async () => {
    setGenerating(true)
    setGenerationResult(null)
    
    try {
      const response = await fetch('/api/bundles/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          platform: 'youtube',
          forceRegenerate: false
        })
      })
      
      const data = await response.json()
      setGenerationResult(data)
    } catch (error) {
      console.error('Error generating bundles:', error)
      setGenerationResult({
        success: false,
        error: 'Network error while generating bundles'
      })
    } finally {
      setGenerating(false)
    }
  }

  const handleRefreshBundles = async () => {
    setRefreshing(true)
    setRefreshResult(null)
    
    try {
      const response = await fetch('/api/bundles/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          platform: 'youtube',
          forceRefresh: true,
          maxAge: 604800 // 1 week
        })
      })
      
      const data = await response.json()
      setRefreshResult(data)
    } catch (error) {
      console.error('Error refreshing bundles:', error)
      setRefreshResult({
        success: false,
        error: 'Network error while refreshing bundles'
      })
    } finally {
      setRefreshing(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <header className="border-b border-white/20 bg-black/20 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center gap-3">
            <Brain className="h-8 w-8 text-purple-400" />
            <div>
              <h1 className="text-2xl font-bold text-white">AI Bundle Curation Demo</h1>
              <p className="text-gray-300 text-sm">Test the AI-powered creator bundle generation system</p>
            </div>
            <Badge className="bg-purple-600/20 text-purple-300">AI POWERED</Badge>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* AI Curation Overview */}
        <Card className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 border-purple-400/30 mb-8">
          <CardContent className="p-6">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
              <Sparkles className="h-5 w-5" />
              AI-Assisted Bundle Curation System
            </h3>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-purple-300 mb-2">🤖 How AI Curation Works:</h4>
                <ul className="text-gray-300 text-sm space-y-1">
                  <li>• <strong>Trust Score Analysis</strong> - Evaluates creator reliability (25%)</li>
                  <li>• <strong>Activity Scoring</strong> - Measures recent content frequency (20%)</li>
                  <li>• <strong>Topic Relevance</strong> - Matches content to bundle themes (30%)</li>
                  <li>• <strong>Quality Assessment</strong> - Analyzes AI summaries and references (20%)</li>
                  <li>• <strong>Diversity Constraints</strong> - Ensures variety in selections (5%)</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-purple-300 mb-2">⚙️ Curation Features:</h4>
                <ul className="text-gray-300 text-sm space-y-1">
                  <li>• <strong>Minimum Activity</strong> - 3+ posts in last 30 days</li>
                  <li>• <strong>Trust Threshold</strong> - 6.0+ trust score required</li>
                  <li>• <strong>Smart Deduplication</strong> - Avoids similar creators</li>
                  <li>• <strong>Genre Distribution</strong> - Balances creator specialties</li>
                  <li>• <strong>Weekly Refresh</strong> - Automatic bundle updates</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* AI Generation Controls */}
        <div className="grid md:grid-cols-2 gap-6 mb-8">
          <Card className="bg-white/10 border-white/20 text-white">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Brain className="h-5 w-5 text-purple-400" />
                Generate AI Bundles
              </CardTitle>
              <CardDescription className="text-gray-300">
                Use AI to create themed creator bundles automatically
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={handleGenerateBundles}
                disabled={generating}
                className="w-full bg-purple-600 hover:bg-purple-700 mb-4"
              >
                {generating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    AI Generating Bundles...
                  </>
                ) : (
                  <>
                    <Sparkles className="mr-2 h-4 w-4" />
                    Generate AI Bundles
                  </>
                )}
              </Button>
              
              <div className="text-sm text-gray-300 space-y-1">
                <p>• Analyzes creator content and engagement</p>
                <p>• Groups creators by topic relevance</p>
                <p>• Applies quality and diversity filters</p>
                <p>• Creates 6 themed bundles automatically</p>
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white/10 border-white/20 text-white">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <RefreshCw className="h-5 w-5 text-green-400" />
                Refresh Bundles
              </CardTitle>
              <CardDescription className="text-gray-300">
                Update existing bundles with fresh AI curation
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button
                onClick={handleRefreshBundles}
                disabled={refreshing}
                className="w-full bg-green-600 hover:bg-green-700 mb-4"
              >
                {refreshing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    AI Refreshing...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Refresh with AI
                  </>
                )}
              </Button>
              
              <div className="text-sm text-gray-300 space-y-1">
                <p>• Re-evaluates all creators with fresh data</p>
                <p>• Adds trending creators to bundles</p>
                <p>• Removes inactive or low-quality creators</p>
                <p>• Updates scores and positions</p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Generation Results */}
        {generationResult && (
          <Card className={`mb-8 ${
            generationResult.success 
              ? 'bg-green-600/20 border-green-400/30' 
              : 'bg-red-600/20 border-red-400/30'
          }`}>
            <CardHeader>
              <CardTitle className={`flex items-center gap-2 ${
                generationResult.success ? 'text-green-300' : 'text-red-300'
              }`}>
                <Zap className="h-5 w-5" />
                AI Generation Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              {generationResult.success ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-green-300">
                        {generationResult.stats?.bundles_generated || 0}
                      </div>
                      <div className="text-sm text-gray-300">Bundles Created</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-300">
                        {generationResult.stats?.total_creators_added || 0}
                      </div>
                      <div className="text-sm text-gray-300">Creators Added</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-300">
                        {generationResult.stats?.themes_processed || 0}
                      </div>
                      <div className="text-sm text-gray-300">Themes Processed</div>
                    </div>
                  </div>

                  {generationResult.results && (
                    <div className="space-y-2">
                      <h4 className="font-medium text-green-300">Bundle Results:</h4>
                      {generationResult.results.map((result: any, index: number) => (
                        <div key={index} className="flex items-center justify-between p-2 bg-black/20 rounded">
                          <span className="text-white">{result.title}</span>
                          <Badge className={`text-xs ${
                            result.status === 'success' 
                              ? 'bg-green-600/20 text-green-300'
                              : result.status === 'skipped'
                              ? 'bg-yellow-600/20 text-yellow-300'
                              : 'bg-red-600/20 text-red-300'
                          }`}>
                            {result.status} {result.creators_added && `(${result.creators_added} creators)`}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-red-300">
                  <h4 className="font-medium mb-2">❌ Generation Failed</h4>
                  <p className="text-sm">{generationResult.error}</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* Refresh Results */}
        {refreshResult && (
          <Card className={`mb-8 ${
            refreshResult.success 
              ? 'bg-blue-600/20 border-blue-400/30' 
              : 'bg-red-600/20 border-red-400/30'
          }`}>
            <CardHeader>
              <CardTitle className={`flex items-center gap-2 ${
                refreshResult.success ? 'text-blue-300' : 'text-red-300'
              }`}>
                <RefreshCw className="h-5 w-5" />
                AI Refresh Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              {refreshResult.success ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold text-blue-300">
                        {refreshResult.stats?.bundles_refreshed || 0}
                      </div>
                      <div className="text-sm text-gray-300">Bundles Refreshed</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-blue-300">
                        {refreshResult.stats?.creators_updated || 0}
                      </div>
                      <div className="text-sm text-gray-300">Creators Updated</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-blue-300">
                        {refreshResult.stats?.bundles_checked || 0}
                      </div>
                      <div className="text-sm text-gray-300">Bundles Checked</div>
                    </div>
                  </div>

                  {refreshResult.results && (
                    <div className="space-y-2">
                      <h4 className="font-medium text-blue-300">Refresh Details:</h4>
                      {refreshResult.results.map((result: any, index: number) => (
                        <div key={index} className="p-3 bg-black/20 rounded">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-white font-medium">{result.title}</span>
                            <Badge className={`text-xs ${
                              result.status === 'success' 
                                ? 'bg-blue-600/20 text-blue-300'
                                : 'bg-red-600/20 text-red-300'
                            }`}>
                              {result.status}
                            </Badge>
                          </div>
                          {result.changes && (
                            <div className="text-sm text-gray-300">
                              +{result.changes.added} added, -{result.changes.removed} removed, {result.changes.retained} retained
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-red-300">
                  <h4 className="font-medium mb-2">❌ Refresh Failed</h4>
                  <p className="text-sm">{refreshResult.error}</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}

        {/* AI Curation Details */}
        <Card className="bg-white/10 border-white/20">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">🧠 AI Curation Engine Details</h3>
            <div className="grid md:grid-cols-2 gap-6 text-sm text-gray-300">
              <div>
                <h4 className="font-medium text-purple-300 mb-2">Scoring Algorithm:</h4>
                <ul className="space-y-1">
                  <li>• Trust Score: Creator reliability (0-10 scale)</li>
                  <li>• Activity: Recent content frequency analysis</li>
                  <li>• Relevance: Topic matching via AI summaries</li>
                  <li>• Quality: Content depth and references</li>
                  <li>• Verification: Platform verification bonus</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-purple-300 mb-2">Constraints & Filters:</h4>
                <ul className="space-y-1">
                  <li>• Minimum 3 posts in last 30 days</li>
                  <li>• Trust score threshold: 6.0+</li>
                  <li>• Diversity weighting: 0.2-0.5</li>
                  <li>• Handle similarity detection</li>
                  <li>• Genre distribution balancing</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

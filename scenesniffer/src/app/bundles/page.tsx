'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/providers/auth-provider'
import { BundleGrid } from '@/components/bundles/bundle-grid'
import { useBundles, useUserBundles } from '@/hooks/use-bundles'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Package, ArrowLeft, Sparkles, Users, TrendingUp, Clock } from 'lucide-react'

export default function BundlesPage() {
  const router = useRouter()
  const { user, loading: authLoading } = useAuth()
  const [filters, setFilters] = useState({
    platform: 'youtube',
    status: 'active'
  })

  const {
    bundles,
    loading: bundlesLoading,
    error: bundlesError,
    addBundle,
    fetchBundles,
    hasMore,
    loadMore,
    totalBundles
  } = useBundles(filters)

  const {
    userBundles,
    loading: userBundlesLoading,
    fetchUserBundles
  } = useUserBundles()

  // Redirect to auth if not authenticated
  useEffect(() => {
    if (!authLoading && !user) {
      router.push('/auth')
    }
  }, [user, authLoading, router])

  const handleAddBundle = async (bundleId: string) => {
    const result = await addBundle(bundleId)
    if (result.success) {
      // Refresh user bundles to update subscription status
      await fetchUserBundles()
    }
    return result
  }

  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters)
  }

  const getSubscribedBundleIds = () => {
    return userBundles.map(ub => ub.bundle_id)
  }

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-white">Loading...</div>
      </div>
    )
  }

  if (!user) {
    return null // Will redirect to auth
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <header className="border-b border-white/20 bg-black/20 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.back()}
                className="text-white border-white/30 hover:bg-white/10"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <div className="flex items-center gap-3">
                <Package className="h-8 w-8 text-purple-400" />
                <div>
                  <h1 className="text-2xl font-bold text-white">Creator Bundles</h1>
                  <p className="text-gray-300 text-sm">Discover curated creator collections</p>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-4">
              <Badge className="bg-purple-600/20 text-purple-300">
                {totalBundles} Available
              </Badge>
              <Badge className="bg-green-600/20 text-green-300">
                {userBundles.length} Subscribed
              </Badge>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Sparkles className="h-6 w-6 text-purple-400" />
            <h2 className="text-4xl font-bold text-white">Curated Creator Bundles</h2>
            <Sparkles className="h-6 w-6 text-purple-400" />
          </div>
          <p className="text-gray-300 text-lg max-w-3xl mx-auto mb-8">
            Discover themed collections of top creators, handpicked by our AI and editorial team. 
            Add entire bundles to instantly follow creators who share your interests.
          </p>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-2xl mx-auto">
            <Card className="bg-white/10 border-white/20 text-white">
              <CardContent className="p-4 text-center">
                <Package className="h-8 w-8 text-purple-400 mx-auto mb-2" />
                <div className="text-2xl font-bold">{totalBundles}</div>
                <div className="text-sm text-gray-300">Total Bundles</div>
              </CardContent>
            </Card>
            
            <Card className="bg-white/10 border-white/20 text-white">
              <CardContent className="p-4 text-center">
                <Users className="h-8 w-8 text-green-400 mx-auto mb-2" />
                <div className="text-2xl font-bold">
                  {userBundles.reduce((sum, ub) => sum + (ub.included_creator_ids?.length || 0), 0)}
                </div>
                <div className="text-sm text-gray-300">Creators Added</div>
              </CardContent>
            </Card>
            
            <Card className="bg-white/10 border-white/20 text-white">
              <CardContent className="p-4 text-center">
                <TrendingUp className="h-8 w-8 text-blue-400 mx-auto mb-2" />
                <div className="text-2xl font-bold">{userBundles.length}</div>
                <div className="text-sm text-gray-300">Subscribed</div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* How It Works */}
        <Card className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 border-purple-400/30 mb-8">
          <CardContent className="p-6">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
              <Sparkles className="h-5 w-5" />
              How Creator Bundles Work
            </h3>
            <div className="grid md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="bg-purple-600/20 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                  <span className="text-xl">🔍</span>
                </div>
                <h4 className="font-medium text-purple-300 mb-2">Discover</h4>
                <p className="text-gray-300 text-sm">
                  Browse themed bundles curated by AI and our editorial team
                </p>
              </div>
              <div className="text-center">
                <div className="bg-purple-600/20 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                  <span className="text-xl">📦</span>
                </div>
                <h4 className="font-medium text-purple-300 mb-2">Add Bundle</h4>
                <p className="text-gray-300 text-sm">
                  Add entire bundles with one click to follow all creators at once
                </p>
              </div>
              <div className="text-center">
                <div className="bg-purple-600/20 rounded-full w-12 h-12 flex items-center justify-center mx-auto mb-3">
                  <span className="text-xl">🎬</span>
                </div>
                <h4 className="font-medium text-purple-300 mb-2">Enjoy</h4>
                <p className="text-gray-300 text-sm">
                  Get personalized content from your new favorite creators
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bundle Grid */}
        <BundleGrid
          bundles={bundles}
          subscribedBundleIds={getSubscribedBundleIds()}
          loading={bundlesLoading}
          error={bundlesError}
          onAddBundle={handleAddBundle}
          onLoadMore={loadMore}
          onRefresh={() => fetchBundles()}
          hasMore={hasMore}
          showFilters={true}
          onFilterChange={handleFilterChange}
          currentFilters={filters}
        />

        {/* Footer Info */}
        <div className="mt-12 text-center">
          <Card className="bg-white/10 border-white/20 max-w-2xl mx-auto">
            <CardContent className="p-6">
              <Clock className="h-8 w-8 text-purple-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-white mb-2">Fresh Content Weekly</h3>
              <p className="text-gray-300 text-sm">
                Our bundles are refreshed weekly with the most active and engaging creators. 
                New bundles are added regularly based on trending topics and user feedback.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { AuthProvider } from "@/components/providers/auth-provider";
import { WatchlistProvider } from "@/contexts/WatchlistContext";
import { Toaster } from "sonner";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "SceneSniffer - Your Cinematic Pulse",
  description: "Discover movies and TV shows through trusted creators. Get personalized recommendations, AI-powered insights, and streaming availability all in one place.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AuthProvider>
          <WatchlistProvider>
            {children}
            <Toaster position="top-right" richColors />
          </WatchlistProvider>
        </AuthProvider>
      </body>
    </html>
  );
}

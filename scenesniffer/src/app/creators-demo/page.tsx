'use client'

import { CreatorManager } from '@/components/creators/creator-manager'

export default function CreatorsDemoPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            🎬 Creator Management Demo
          </h1>
          <p className="text-gray-300 text-lg max-w-2xl mx-auto">
            Add and remove creators you follow for personalized movie and TV recommendations.
            This demonstrates the dynamic creator management system.
          </p>
        </div>

        {/* Creator Manager Component */}
        <div className="max-w-4xl mx-auto">
          <CreatorManager />
        </div>

        {/* Instructions */}
        <div className="max-w-4xl mx-auto mt-8">
          <div className="bg-white/10 border border-white/20 rounded-lg p-6 text-white">
            <h3 className="text-lg font-semibold mb-4">🚀 How to Test:</h3>
            <div className="space-y-2 text-sm text-gray-300">
              <p><strong>1. Add Creators:</strong> Use the form above to follow new creators by platform and handle</p>
              <p><strong>2. View Your List:</strong> See all creators you're following with their trust scores</p>
              <p><strong>3. Remove Creators:</strong> Click the minus button to unfollow creators</p>
              <p><strong>4. Visit Profiles:</strong> Click the external link button to visit creator profiles</p>
              <p><strong>5. Platform Stats:</strong> See how many creators you follow per platform</p>
            </div>
            
            <div className="mt-4 p-4 bg-purple-600/20 rounded border border-purple-400/30">
              <h4 className="font-medium text-purple-300 mb-2">💡 Try These Examples:</h4>
              <div className="text-xs text-gray-300 space-y-1">
                <p>• YouTube: @CorridorCrew, @FilmTheorists, @CinemaWins</p>
                <p>• TikTok: @filmtok, @movietok</p>
                <p>• Instagram: @movies, @cinema</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

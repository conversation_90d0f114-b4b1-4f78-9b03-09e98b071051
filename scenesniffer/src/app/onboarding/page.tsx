'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/providers/auth-provider'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import { ArrowRight, ArrowLeft, CheckCircle } from 'lucide-react'
import { GenreSelection } from '@/components/onboarding/genre-selection'
import { CreatorSelection } from '@/components/onboarding/creator-selection'
import { StreamingSelection } from '@/components/onboarding/streaming-selection'
import { OnboardingComplete } from '@/components/onboarding/onboarding-complete'
import { OnboardingData } from '@/types'

const STEPS = [
  { id: 'genres', title: 'Choose Your Genres', description: 'What types of movies and shows do you love?' },
  { id: 'creators', title: 'Follow Creators', description: 'Which content creators do you trust for recommendations?' },
  { id: 'streaming', title: 'Streaming Services', description: 'Which platforms do you have access to?' },
  { id: 'complete', title: 'All Set!', description: 'Your personalized feed is ready' }
]

export default function OnboardingPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(0)
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    genres: [],
    creators: [],
    streaming_services: []
  })
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    const checkUserStatus = async () => {
      if (!loading && !user) {
        router.push('/auth')
        return
      }

      if (!loading && user) {
        // Check if user has already completed onboarding
        try {
          const response = await fetch('/api/user/preferences')
          if (response.ok) {
            const data = await response.json()
            if (data.preferences && (data.preferences.genres?.length > 0 || data.preferences.streaming_services?.length > 0)) {
              // User has already completed onboarding - redirect to feed
              console.log('User has already completed onboarding, redirecting to feed')
              router.push('/feed')
              return
            }
          }
        } catch (error) {
          console.error('Error checking user preferences:', error)
          // Continue with onboarding if there's an error
        }
      }
    }

    checkUserStatus()
  }, [user, loading, router])

  const updateOnboardingData = (key: keyof OnboardingData, value: any) => {
    setOnboardingData(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const nextStep = () => {
    if (currentStep < STEPS.length - 1) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const canProceed = () => {
    switch (currentStep) {
      case 0: // Genres
        return onboardingData.genres.length > 0
      case 1: // Creators
        return onboardingData.creators.length > 0
      case 2: // Streaming
        return onboardingData.streaming_services.length > 0
      default:
        return true
    }
  }

  const completeOnboarding = async () => {
    if (!user) return

    setSaving(true)
    try {
      // Save user preferences
      const response = await fetch('/api/user/preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(onboardingData)
      })

      if (!response.ok) {
        throw new Error('Failed to save preferences')
      }

      nextStep() // Go to completion step
    } catch (error) {
      console.error('Error saving onboarding data:', error)
      alert('Failed to save preferences. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  const handleFinish = () => {
    router.push('/feed')
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  const progress = ((currentStep + 1) / STEPS.length) * 100

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-4">
      <div className="max-w-4xl mx-auto py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">Welcome to SceneSniffer!</h1>
          <p className="text-xl text-gray-300">Let's personalize your experience</p>
        </div>

        {/* Progress */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm text-gray-300">Step {currentStep + 1} of {STEPS.length}</span>
            <span className="text-sm text-gray-300">{Math.round(progress)}% complete</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Step Content */}
        <Card className="bg-white/10 border-white/20 text-white mb-8">
          <CardHeader>
            <CardTitle className="text-2xl">{STEPS[currentStep].title}</CardTitle>
            <CardDescription className="text-gray-300 text-lg">
              {STEPS[currentStep].description}
            </CardDescription>
          </CardHeader>
          <CardContent>
            {currentStep === 0 && (
              <GenreSelection
                selectedGenres={onboardingData.genres}
                onGenresChange={(genres) => updateOnboardingData('genres', genres)}
              />
            )}
            {currentStep === 1 && (
              <CreatorSelection
                selectedCreators={onboardingData.creators}
                onCreatorsChange={(creators) => updateOnboardingData('creators', creators)}
              />
            )}
            {currentStep === 2 && (
              <StreamingSelection
                selectedServices={onboardingData.streaming_services}
                onServicesChange={(services) => updateOnboardingData('streaming_services', services)}
              />
            )}
            {currentStep === 3 && (
              <OnboardingComplete
                onboardingData={onboardingData}
                onFinish={handleFinish}
              />
            )}
          </CardContent>
        </Card>

        {/* Navigation */}
        {currentStep < 3 && (
          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={prevStep}
              disabled={currentStep === 0}
              className="text-white border-white hover:bg-white hover:text-black"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Previous
            </Button>

            {currentStep === 2 ? (
              <Button
                onClick={completeOnboarding}
                disabled={!canProceed() || saving}
                className="bg-purple-600 hover:bg-purple-700"
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <CheckCircle className="mr-2 h-4 w-4" />
                    Complete Setup
                  </>
                )}
              </Button>
            ) : (
              <Button
                onClick={nextStep}
                disabled={!canProceed()}
                className="bg-purple-600 hover:bg-purple-700"
              >
                Next
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

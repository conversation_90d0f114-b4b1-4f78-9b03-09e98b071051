'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/components/providers/auth-provider'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Film, Tv, Star, TrendingUp, Award, Calendar, Play, Plus } from 'lucide-react'
import { WatchlistButtonCompact } from '@/components/watchlist/WatchlistButton'

interface DiscoverItem {
  id: number
  tmdb_id: number
  title?: string
  name?: string
  overview: string
  poster_url: string | null
  backdrop_url: string | null
  vote_average: number
  release_date?: string
  first_air_date?: string
  content_type: 'movie' | 'tv'
  genre_ids: number[]
}

interface Genre {
  id: number
  name: string
}

export default function DiscoverPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  
  const [activeTab, setActiveTab] = useState<'movie' | 'tv'>('movie')
  const [category, setCategory] = useState('trending')
  const [timeWindow, setTimeWindow] = useState('week')
  const [selectedGenre, setSelectedGenre] = useState<string>('all')
  const [content, setContent] = useState<DiscoverItem[]>([])
  const [genres, setGenres] = useState<Genre[]>([])
  const [loadingContent, setLoadingContent] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Temporarily disable auth redirect for testing
  // useEffect(() => {
  //   if (!loading && !user) {
  //     router.push('/auth')
  //   }
  // }, [user, loading, router])

  // Fetch genres on mount
  useEffect(() => {
    fetchGenres()
  }, [activeTab])

  // Fetch content when filters change
  useEffect(() => {
    // Temporarily allow fetching without user for testing
    fetchContent()
  }, [activeTab, category, timeWindow, selectedGenre])

  const fetchGenres = async () => {
    try {
      const response = await fetch('/api/discover', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'genres', type: activeTab })
      })
      
      if (response.ok) {
        const data = await response.json()
        setGenres(data.genres || [])
      }
    } catch (error) {
      console.error('Error fetching genres:', error)
    }
  }

  const fetchContent = async () => {
    setLoadingContent(true)
    setError(null)
    
    try {
      const params = new URLSearchParams({
        type: activeTab,
        category,
        ...(category === 'trending' && { time_window: timeWindow }),
        ...(selectedGenre !== 'all' && { genre: selectedGenre })
      })

      const response = await fetch(`/api/discover?${params}`)
      
      if (response.ok) {
        const data = await response.json()
        setContent(data.results || [])
      } else {
        setError('Failed to fetch content')
      }
    } catch (error) {
      console.error('Error fetching content:', error)
      setError('Failed to fetch content')
    } finally {
      setLoadingContent(false)
    }
  }

  const getTitle = (item: DiscoverItem) => item.title || item.name || 'Unknown Title'
  const getReleaseDate = (item: DiscoverItem) => item.release_date || item.first_air_date
  const getReleaseYear = (item: DiscoverItem) => {
    const date = getReleaseDate(item)
    return date ? new Date(date).getFullYear() : null
  }

  // Temporarily allow access without user for testing
  // if (!user) {
  //   return null
  // }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">
            Discover Content
          </h1>
          <p className="text-gray-300">
            Find trending movies and TV shows, explore by genre, and discover your next favorite watch
          </p>
        </div>

        {/* Content Type Tabs */}
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'movie' | 'tv')} className="mb-6">
          <TabsList className="bg-white/10 border-white/20">
            <TabsTrigger value="movie" className="data-[state=active]:bg-white/20">
              <Film className="mr-2 h-4 w-4" />
              Movies
            </TabsTrigger>
            <TabsTrigger value="tv" className="data-[state=active]:bg-white/20">
              <Tv className="mr-2 h-4 w-4" />
              TV Shows
            </TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="space-y-6">
            {/* Filters */}
            <Card className="bg-white/10 border-white/20">
              <CardContent className="p-4">
                <div className="flex flex-wrap gap-4 items-center">
                  {/* Category Filter */}
                  <div className="flex items-center gap-2">
                    <label className="text-white text-sm font-medium">Category:</label>
                    <Select value={category} onValueChange={setCategory}>
                      <SelectTrigger className="w-40 bg-gray-800/50 border-gray-600 text-white">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="trending">
                          <div className="flex items-center">
                            <TrendingUp className="mr-2 h-4 w-4" />
                            Trending
                          </div>
                        </SelectItem>
                        <SelectItem value="popular">
                          <div className="flex items-center">
                            <Star className="mr-2 h-4 w-4" />
                            Popular
                          </div>
                        </SelectItem>
                        <SelectItem value="top_rated">
                          <div className="flex items-center">
                            <Award className="mr-2 h-4 w-4" />
                            Top Rated
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Time Window (only for trending) */}
                  {category === 'trending' && (
                    <div className="flex items-center gap-2">
                      <label className="text-white text-sm font-medium">Period:</label>
                      <Select value={timeWindow} onValueChange={setTimeWindow}>
                        <SelectTrigger className="w-32 bg-gray-800/50 border-gray-600 text-white">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="day">Today</SelectItem>
                          <SelectItem value="week">This Week</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  )}

                  {/* Genre Filter */}
                  <div className="flex items-center gap-2">
                    <label className="text-white text-sm font-medium">Genre:</label>
                    <Select value={selectedGenre} onValueChange={setSelectedGenre}>
                      <SelectTrigger className="w-40 bg-gray-800/50 border-gray-600 text-white">
                        <SelectValue placeholder="All Genres" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Genres</SelectItem>
                        {genres.map(genre => (
                          <SelectItem key={genre.id} value={genre.id.toString()}>
                            {genre.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Clear Filters */}
                  {selectedGenre !== 'all' && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedGenre('all')}
                      className="border-white/20 text-white hover:bg-white/10"
                    >
                      Clear Genre
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Content Grid */}
            {error && (
              <Card className="bg-red-500/20 border-red-400/30">
                <CardContent className="p-4">
                  <p className="text-red-300">{error}</p>
                </CardContent>
              </Card>
            )}

            {loadingContent ? (
              <div className="flex justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
              </div>
            ) : content.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
                {content.map((item) => (
                  <Card key={item.id} className="bg-white/10 border-white/20 hover:bg-white/15 transition-colors group overflow-hidden">
                    <div className="relative">
                      {/* Poster */}
                      <div className="aspect-[2/3] relative overflow-hidden">
                        {item.poster_url ? (
                          <img
                            src={item.poster_url}
                            alt={getTitle(item)}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-800 flex items-center justify-center">
                            {activeTab === 'movie' ? (
                              <Film className="h-12 w-12 text-gray-600" />
                            ) : (
                              <Tv className="h-12 w-12 text-gray-600" />
                            )}
                          </div>
                        )}
                        
                        {/* Overlay with actions */}
                        <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                          <Button
                            size="sm"
                            variant="secondary"
                            className="bg-white/20 text-white border-white/30 hover:bg-white/30"
                            onClick={() => {
                              // Navigate to content details or TMDb
                              const tmdbUrl = `https://www.themoviedb.org/${activeTab}/${item.tmdb_id}`
                              window.open(tmdbUrl, '_blank')
                            }}
                          >
                            <Play className="h-4 w-4" />
                          </Button>
                          
                          <WatchlistButtonCompact
                            tmdbId={item.tmdb_id}
                            contentType={activeTab}
                            title={getTitle(item)}
                          />
                        </div>
                      </div>

                      {/* Rating Badge */}
                      {item.vote_average > 0 && (
                        <div className="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded-full flex items-center gap-1">
                          <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                          {item.vote_average.toFixed(1)}
                        </div>
                      )}
                    </div>

                    <CardContent className="p-3">
                      <h3 className="text-white font-medium text-sm mb-1 line-clamp-2">
                        {getTitle(item)}
                      </h3>
                      
                      {getReleaseYear(item) && (
                        <div className="flex items-center gap-1 text-gray-400 text-xs mb-2">
                          <Calendar className="h-3 w-3" />
                          {getReleaseYear(item)}
                        </div>
                      )}

                      {item.overview && (
                        <p className="text-gray-300 text-xs line-clamp-3">
                          {item.overview}
                        </p>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <Card className="bg-white/10 border-white/20">
                <CardContent className="p-8 text-center">
                  <div className="text-gray-400 mb-4">
                    {activeTab === 'movie' ? (
                      <Film className="h-12 w-12 mx-auto" />
                    ) : (
                      <Tv className="h-12 w-12 mx-auto" />
                    )}
                  </div>
                  <h3 className="text-white text-lg font-medium mb-2">No Content Found</h3>
                  <p className="text-gray-300">
                    Try adjusting your filters or check back later for new content.
                  </p>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

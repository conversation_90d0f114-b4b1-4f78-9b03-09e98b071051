'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Search, Film, Tv } from 'lucide-react'

interface Movie {
  id: number
  title: string
  overview: string
  poster_path: string
  release_date: string
  vote_average: number
}

interface TVShow {
  id: number
  name: string
  overview: string
  poster_path: string
  first_air_date: string
  vote_average: number
}

export default function DemoPage() {
  const [query, setQuery] = useState('')
  const [movies, setMovies] = useState<Movie[]>([])
  const [tvShows, setTVShows] = useState<TVShow[]>([])
  const [loading, setLoading] = useState(false)

  const searchContent = async () => {
    if (!query.trim()) return
    
    setLoading(true)
    try {
      const response = await fetch(`/api/search?q=${encodeURIComponent(query)}&type=multi`)
      const data = await response.json()
      setMovies(data.movies || [])
      setTVShows(data.tvShows || [])
    } catch (error) {
      console.error('Search error:', error)
    } finally {
      setLoading(false)
    }
  }

  const getImageUrl = (path: string) => {
    return path ? `https://image.tmdb.org/t/p/w300${path}` : '/placeholder-poster.jpg'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">🎬 SceneSniffer Demo</h1>
          <p className="text-xl text-gray-300 mb-6">
            Search for movies and TV shows using our TMDb integration
          </p>
          
          <div className="flex gap-4 max-w-md mx-auto">
            <input
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && searchContent()}
              placeholder="Search for movies or TV shows..."
              className="flex-1 px-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-purple-500"
            />
            <Button 
              onClick={searchContent}
              disabled={loading || !query.trim()}
              className="bg-purple-600 hover:bg-purple-700"
            >
              <Search className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {loading && (
          <div className="text-center text-white">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto"></div>
            <p className="mt-4">Searching...</p>
          </div>
        )}

        {(movies.length > 0 || tvShows.length > 0) && !loading && (
          <div className="space-y-8">
            {/* Movies Section */}
            {movies.length > 0 && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-4 flex items-center gap-2">
                  <Film className="h-6 w-6" />
                  Movies ({movies.length})
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {movies.slice(0, 8).map((movie) => (
                    <Card key={movie.id} className="bg-white/10 border-white/20 text-white overflow-hidden">
                      <div className="aspect-[2/3] relative">
                        <img
                          src={getImageUrl(movie.poster_path)}
                          alt={movie.title}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.currentTarget.src = '/placeholder-poster.jpg'
                          }}
                        />
                      </div>
                      <CardHeader className="p-4">
                        <CardTitle className="text-sm font-semibold line-clamp-2">
                          {movie.title}
                        </CardTitle>
                        <CardDescription className="text-gray-300 text-xs">
                          {movie.release_date ? new Date(movie.release_date).getFullYear() : 'TBA'}
                          {movie.vote_average > 0 && (
                            <span className="ml-2">⭐ {movie.vote_average.toFixed(1)}</span>
                          )}
                        </CardDescription>
                      </CardHeader>
                      {movie.overview && (
                        <CardContent className="p-4 pt-0">
                          <p className="text-xs text-gray-300 line-clamp-3">
                            {movie.overview}
                          </p>
                        </CardContent>
                      )}
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* TV Shows Section */}
            {tvShows.length > 0 && (
              <div>
                <h2 className="text-2xl font-bold text-white mb-4 flex items-center gap-2">
                  <Tv className="h-6 w-6" />
                  TV Shows ({tvShows.length})
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {tvShows.slice(0, 8).map((show) => (
                    <Card key={show.id} className="bg-white/10 border-white/20 text-white overflow-hidden">
                      <div className="aspect-[2/3] relative">
                        <img
                          src={getImageUrl(show.poster_path)}
                          alt={show.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.currentTarget.src = '/placeholder-poster.jpg'
                          }}
                        />
                      </div>
                      <CardHeader className="p-4">
                        <CardTitle className="text-sm font-semibold line-clamp-2">
                          {show.name}
                        </CardTitle>
                        <CardDescription className="text-gray-300 text-xs">
                          {show.first_air_date ? new Date(show.first_air_date).getFullYear() : 'TBA'}
                          {show.vote_average > 0 && (
                            <span className="ml-2">⭐ {show.vote_average.toFixed(1)}</span>
                          )}
                        </CardDescription>
                      </CardHeader>
                      {show.overview && (
                        <CardContent className="p-4 pt-0">
                          <p className="text-xs text-gray-300 line-clamp-3">
                            {show.overview}
                          </p>
                        </CardContent>
                      )}
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {!loading && movies.length === 0 && tvShows.length === 0 && query && (
          <div className="text-center text-white">
            <p>No results found for "{query}". Try a different search term.</p>
          </div>
        )}
      </div>
    </div>
  )
}

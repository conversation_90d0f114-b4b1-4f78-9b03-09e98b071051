'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { CreatorManager } from '@/components/creators/creator-manager'
import { Film, Users, Settings, LogOut, Bell, Search, Play, ExternalLink, Clock, Star, UserPlus, X, Package } from 'lucide-react'
import { useRouter } from 'next/navigation'

export default function FeedDemoPage() {
  const router = useRouter()
  const [showCreatorManager, setShowCreatorManager] = useState(false)

  // Mock data for demo
  const mockPreferences = {
    preferences: {
      genres: ['Action', 'Sci-Fi', 'Horror'],
      streaming_services: ['Netflix', 'Disney+', 'HBO Max']
    },
    creators: [
      { platform: 'youtube', handle: 'CorridorCrew', name: 'Corridor Crew', verified: true },
      { platform: 'youtube', handle: 'FilmTheorists', name: 'The Film Theorists', verified: true },
      { platform: 'instagram', handle: 'movies', name: 'Movies', verified: false }
    ]
  }

  const handleCreatorManagerClose = () => {
    setShowCreatorManager(false)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <header className="border-b border-white/20 bg-black/20 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Film className="h-8 w-8 text-purple-400" />
              <h1 className="text-2xl font-bold text-white">SceneSniffer</h1>
              <span className="px-2 py-1 bg-purple-600/20 text-purple-300 text-xs rounded">DEMO</span>
            </div>

            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/bundles')}
                className="text-white border-white/30 hover:bg-white/10"
              >
                <Package className="h-4 w-4 mr-2" />
                Discover Bundles
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCreatorManager(true)}
                className="text-white border-white/30 hover:bg-white/10"
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Manage Creators
              </Button>
              <Button variant="outline" size="sm" className="text-white border-white/30 hover:bg-white/10">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
              <Button variant="outline" size="sm" className="text-white border-white/30 hover:bg-white/10">
                <Bell className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" className="text-white border-white/30 hover:bg-white/10">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-white mb-2">
            Welcome to SceneSniffer Feed Demo! 👋
          </h2>
          <p className="text-gray-300 text-lg">
            This demonstrates the main feed interface with navigation to creator management and bundle discovery.
          </p>
        </div>

        {/* User Stats */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card className="bg-white/10 border-white/20 text-white">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <Film className="h-5 w-5 text-purple-400" />
                Genres
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold mb-1">
                {mockPreferences.preferences?.genres?.length || 0}
              </div>
              <p className="text-gray-300 text-sm">Selected genres</p>
              <div className="flex flex-wrap gap-1 mt-2">
                {mockPreferences.preferences?.genres?.slice(0, 3).map((genre: string) => (
                  <span key={genre} className="px-2 py-1 bg-purple-600/20 text-purple-300 text-xs rounded">
                    {genre}
                  </span>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card 
            className="bg-white/10 border-white/20 text-white hover:bg-white/15 transition-colors cursor-pointer"
            onClick={() => setShowCreatorManager(true)}
          >
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between text-lg">
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-purple-400" />
                  Creators
                </div>
                <UserPlus className="h-4 w-4 text-gray-400" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold mb-1">
                {mockPreferences.creators?.length || 0}
              </div>
              <p className="text-gray-300 text-sm">Followed creators</p>
              <div className="space-y-1 mt-2">
                {mockPreferences.creators?.slice(0, 2).map((creator: any, index: number) => (
                  <div key={index} className="flex items-center gap-2">
                    <span className="text-xs text-gray-400 capitalize">{creator.platform}:</span>
                    <span className="text-white text-xs">{creator.handle}</span>
                  </div>
                ))}
                {(mockPreferences.creators?.length || 0) > 2 && (
                  <p className="text-gray-400 text-xs">
                    +{(mockPreferences.creators?.length || 0) - 2} more
                  </p>
                )}
              </div>
              <p className="text-purple-300 text-xs mt-2 opacity-75">
                Click to manage →
              </p>
            </CardContent>
          </Card>

          <Card className="bg-white/10 border-white/20 text-white">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2 text-lg">
                <svg className="h-5 w-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                </svg>
                Streaming
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold mb-1">
                {mockPreferences.preferences?.streaming_services?.length || 0}
              </div>
              <p className="text-gray-300 text-sm">Connected services</p>
            </CardContent>
          </Card>

          <Card
            className="bg-gradient-to-br from-purple-600/20 to-blue-600/20 border-purple-400/30 text-white hover:from-purple-600/30 hover:to-blue-600/30 transition-all cursor-pointer group"
            onClick={() => router.push('/bundles')}
          >
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center justify-between text-lg">
                <div className="flex items-center gap-2">
                  <Package className="h-5 w-5 text-purple-400" />
                  Creator Bundles
                </div>
                <ExternalLink className="h-4 w-4 text-gray-400 group-hover:text-purple-400 transition-colors" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold mb-1 text-purple-300">New</div>
              <p className="text-gray-300 text-sm mb-2">Curated collections</p>
              <div className="space-y-1">
                <div className="flex items-center gap-1">
                  <span className="text-xs text-purple-400">👻</span>
                  <span className="text-xs text-gray-300">Horror Masters</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-xs text-purple-400">🚀</span>
                  <span className="text-xs text-gray-300">Sci-Fi Experts</span>
                </div>
                <div className="flex items-center gap-1">
                  <span className="text-xs text-purple-400">🎭</span>
                  <span className="text-xs text-gray-300">Indie Cinema</span>
                </div>
              </div>
              <p className="text-purple-300 text-xs mt-2 opacity-75 group-hover:opacity-100 transition-opacity">
                Discover bundles →
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Demo Instructions */}
        <Card className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 border-purple-400/30 mb-8">
          <CardContent className="p-6">
            <h3 className="text-xl font-semibold text-white mb-4">🎬 Feed Navigation Demo</h3>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-purple-300 mb-2">Creator Management:</h4>
                <ul className="text-gray-300 text-sm space-y-1">
                  <li>• Click the "Manage Creators" button in the header</li>
                  <li>• Click on the "Creators" card above</li>
                  <li>• Both open the same creator management interface</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-purple-300 mb-2">Bundle Discovery:</h4>
                <ul className="text-gray-300 text-sm space-y-1">
                  <li>• Click the "Discover Bundles" button in the header</li>
                  <li>• Click on the "Creator Bundles" card above</li>
                  <li>• Both navigate to the bundle discovery interface</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-purple-300 mb-2">What You Can Do:</h4>
                <ul className="text-gray-300 text-sm space-y-1">
                  <li>• Add new creators by platform and handle</li>
                  <li>• View all followed creators with trust scores</li>
                  <li>• Remove creators you no longer want to follow</li>
                  <li>• See platform statistics and distribution</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Mock Content Feed */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-white">Your Personalized Feed</h2>
          
          <Card className="bg-white/10 border-white/20 text-white">
            <CardContent className="p-6">
              <div className="text-center py-8">
                <Film className="h-12 w-12 text-purple-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">Demo Mode</h3>
                <p className="text-gray-300 mb-4">
                  This is a demonstration of the creator management feature. In the real app, this would show content from your followed creators.
                </p>
                <Button
                  onClick={() => setShowCreatorManager(true)}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  <UserPlus className="mr-2 h-4 w-4" />
                  Try Creator Management
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Creator Manager Modal */}
      {showCreatorManager && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 rounded-lg border border-white/20 w-full max-w-4xl max-h-[90vh] overflow-hidden">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-white/20">
              <h2 className="text-2xl font-bold text-white">Manage Your Creators</h2>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCreatorManagerClose}
                className="text-white border-white/30 hover:bg-white/10"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            {/* Modal Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <CreatorManager />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { CheckCircle, XCircle, Loader2 } from 'lucide-react'

interface TestResult {
  status: 'pending' | 'success' | 'error'
  data: any
  error: string | null
}

interface TestResults {
  youtube: TestResult
  tmdb: TestResult
  openai: TestResult
  database: TestResult
}

export default function TestPage() {
  const [results, setResults] = useState<TestResults | null>(null)
  const [loading, setLoading] = useState(false)

  const runTests = async () => {
    setLoading(true)
    try {
      const [apiResponse, dbResponse] = await Promise.all([
        fetch('/api/test'),
        fetch('/api/test-db')
      ])

      const apiData = await apiResponse.json()
      const dbData = await dbResponse.json()

      // Add database test result to the results
      const updatedResults = {
        ...apiData.tests,
        database: {
          status: dbData.success ? 'success' : 'error',
          data: dbData.success ? {
            creatorsCount: dbData.data?.creatorsCount || 0,
            message: dbData.message
          } : null,
          error: dbData.success ? null : dbData.error
        }
      }

      setResults(updatedResults)
    } catch (error) {
      console.error('Error running tests:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-6 w-6 text-green-500" />
      case 'error':
        return <XCircle className="h-6 w-6 text-red-500" />
      default:
        return <Loader2 className="h-6 w-6 text-gray-400 animate-spin" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'border-green-200 bg-green-50'
      case 'error':
        return 'border-red-200 bg-red-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">🧪 SceneSniffer API Tests</h1>
          <p className="text-xl text-gray-300 mb-6">
            Test all external API integrations to ensure everything is working properly.
          </p>
          <Button 
            onClick={runTests} 
            disabled={loading}
            size="lg"
            className="bg-purple-600 hover:bg-purple-700"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                Running Tests...
              </>
            ) : (
              'Run API Tests'
            )}
          </Button>
        </div>

        {results && (
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* YouTube API Test */}
            <Card className={`${getStatusColor(results.youtube.status)} border-2`}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    {getStatusIcon(results.youtube.status)}
                    YouTube API
                  </CardTitle>
                </div>
                <CardDescription>
                  Tests YouTube Data API v3 integration
                </CardDescription>
              </CardHeader>
              <CardContent>
                {results.youtube.status === 'success' && results.youtube.data && (
                  <div className="space-y-2">
                    <p><strong>Channel:</strong> {results.youtube.data.channelName}</p>
                    <p><strong>Subscribers:</strong> {parseInt(results.youtube.data.subscriberCount).toLocaleString()}</p>
                    <p><strong>Videos:</strong> {parseInt(results.youtube.data.videoCount).toLocaleString()}</p>
                  </div>
                )}
                {results.youtube.status === 'error' && (
                  <p className="text-red-600">{results.youtube.error}</p>
                )}
              </CardContent>
            </Card>

            {/* TMDb API Test */}
            <Card className={`${getStatusColor(results.tmdb.status)} border-2`}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    {getStatusIcon(results.tmdb.status)}
                    TMDb API
                  </CardTitle>
                </div>
                <CardDescription>
                  Tests The Movie Database API integration
                </CardDescription>
              </CardHeader>
              <CardContent>
                {results.tmdb.status === 'success' && results.tmdb.data && (
                  <div className="space-y-2">
                    <p><strong>Movies Found:</strong> {results.tmdb.data.movieCount}</p>
                    {results.tmdb.data.firstMovie && (
                      <>
                        <p><strong>First Result:</strong> {results.tmdb.data.firstMovie.title}</p>
                        <p><strong>Release Date:</strong> {results.tmdb.data.firstMovie.releaseDate}</p>
                      </>
                    )}
                  </div>
                )}
                {results.tmdb.status === 'error' && (
                  <p className="text-red-600">{results.tmdb.error}</p>
                )}
              </CardContent>
            </Card>

            {/* OpenAI API Test */}
            <Card className={`${getStatusColor(results.openai.status)} border-2`}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    {getStatusIcon(results.openai.status)}
                    OpenAI API
                  </CardTitle>
                </div>
                <CardDescription>
                  Tests OpenAI GPT integration for AI features
                </CardDescription>
              </CardHeader>
              <CardContent>
                {results.openai.status === 'success' && results.openai.data && (
                  <div className="space-y-2">
                    <p><strong>Summary Length:</strong> {results.openai.data.summaryLength} chars</p>
                    <p><strong>Preview:</strong> {results.openai.data.summary}</p>
                  </div>
                )}
                {results.openai.status === 'error' && (
                  <div className="space-y-2">
                    <p className="text-red-600">{results.openai.error}</p>
                    <div className="mt-4 p-4 bg-yellow-100 border border-yellow-300 rounded">
                      <p className="text-sm text-yellow-800">
                        <strong>Fix:</strong> Your OpenAI API key appears to be invalid. 
                        Please check that you have:
                      </p>
                      <ul className="text-sm text-yellow-800 mt-2 list-disc list-inside">
                        <li>A valid OpenAI account with billing set up</li>
                        <li>Generated a new API key from the OpenAI dashboard</li>
                        <li>Copied the key correctly to your .env.local file</li>
                      </ul>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Database Test */}
            <Card className={`${getStatusColor(results.database.status)} border-2`}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    {getStatusIcon(results.database.status)}
                    Supabase DB
                  </CardTitle>
                </div>
                <CardDescription>
                  Tests Supabase database connection and operations
                </CardDescription>
              </CardHeader>
              <CardContent>
                {results.database.status === 'success' && results.database.data && (
                  <div className="space-y-2">
                    <p><strong>Status:</strong> Connected</p>
                    <p><strong>Creators:</strong> {results.database.data.creatorsCount}</p>
                    <p><strong>Message:</strong> {results.database.data.message}</p>
                  </div>
                )}
                {results.database.status === 'error' && (
                  <p className="text-red-600">{results.database.error}</p>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {results && (
          <div className="mt-8 text-center">
            <Card className="bg-white/10 border-white/20 text-white">
              <CardHeader>
                <CardTitle>Test Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-4 gap-4 text-center">
                  <div>
                    <div className="text-2xl font-bold text-green-400">
                      {Object.values(results).filter(r => r.status === 'success').length}
                    </div>
                    <div className="text-sm">Passed</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-red-400">
                      {Object.values(results).filter(r => r.status === 'error').length}
                    </div>
                    <div className="text-sm">Failed</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-yellow-400">
                      {Object.values(results).filter(r => r.status === 'pending').length}
                    </div>
                    <div className="text-sm">Pending</div>
                  </div>
                  <div>
                    <div className="text-2xl font-bold text-blue-400">
                      {Object.values(results).length}
                    </div>
                    <div className="text-sm">Total</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}

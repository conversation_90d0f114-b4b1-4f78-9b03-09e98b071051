'use client'

import { useState } from 'react'
import { BundleGrid } from '@/components/bundles/bundle-grid'
import { useBundles, useUserBundles } from '@/hooks/use-bundles'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Package, Sparkles, Users, TrendingUp, Clock, Database, Loader2 } from 'lucide-react'

export default function BundlesDemoPage() {
  const [seeding, setSeeding] = useState(false)
  const [seedResult, setSeedResult] = useState<any>(null)
  const [filters, setFilters] = useState({
    platform: 'youtube',
    status: 'active'
  })

  const {
    bundles,
    loading: bundlesLoading,
    error: bundlesError,
    addBundle,
    fetchBundles,
    hasMore,
    loadMore,
    totalBundles
  } = useBundles(filters)

  const {
    userBundles,
    loading: userBundlesLoading,
    fetchUserBundles
  } = useUserBundles()

  const handleSeedBundles = async () => {
    setSeeding(true)
    setSeedResult(null)
    
    try {
      const response = await fetch('/api/bundles/seed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      
      const data = await response.json()
      setSeedResult(data)
      
      if (data.success) {
        // Refresh bundles after seeding
        await fetchBundles()
      }
    } catch (error) {
      console.error('Error seeding bundles:', error)
      setSeedResult({
        success: false,
        error: 'Network error while seeding bundles'
      })
    } finally {
      setSeeding(false)
    }
  }

  const handleAddBundle = async (bundleId: string) => {
    const result = await addBundle(bundleId)
    if (result.success) {
      // Refresh user bundles to update subscription status
      await fetchUserBundles()
    }
    return result
  }

  const handleFilterChange = (newFilters: any) => {
    setFilters(newFilters)
  }

  const getSubscribedBundleIds = () => {
    return userBundles.map(ub => ub.bundle_id)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <header className="border-b border-white/20 bg-black/20 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Package className="h-8 w-8 text-purple-400" />
              <div>
                <h1 className="text-2xl font-bold text-white">Creator Bundles Demo</h1>
                <p className="text-gray-300 text-sm">Test the curated creator bundle system</p>
              </div>
              <Badge className="bg-purple-600/20 text-purple-300">DEMO</Badge>
            </div>

            <div className="flex items-center gap-4">
              <Badge className="bg-purple-600/20 text-purple-300">
                {totalBundles} Available
              </Badge>
              <Badge className="bg-green-600/20 text-green-300">
                {userBundles.length} Subscribed
              </Badge>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Demo Instructions */}
        <Card className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 border-purple-400/30 mb-8">
          <CardContent className="p-6">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
              <Sparkles className="h-5 w-5" />
              Creator Bundles Demo
            </h3>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-purple-300 mb-2">🎬 What are Creator Bundles?</h4>
                <ul className="text-gray-300 text-sm space-y-1">
                  <li>• Curated collections of ~10 creators per theme</li>
                  <li>• AI-assisted curation with editorial oversight</li>
                  <li>• One-click subscription to entire bundles</li>
                  <li>• Weekly refreshes with fresh content</li>
                  <li>• Platform-specific (YouTube, Instagram, etc.)</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-purple-300 mb-2">🚀 How to Test:</h4>
                <ul className="text-gray-300 text-sm space-y-1">
                  <li>• Click "Seed Sample Bundles" to create test data</li>
                  <li>• Browse different themed bundles</li>
                  <li>• Click "Add Bundle" to subscribe to creators</li>
                  <li>• View bundle details to see included creators</li>
                  <li>• Filter by platform and status</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Seeding Section */}
        <Card className="bg-white/10 border-white/20 text-white mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5 text-purple-400" />
              Sample Data
            </CardTitle>
            <CardDescription className="text-gray-300">
              Create sample bundles to test the functionality
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4 mb-4">
              <Button
                onClick={handleSeedBundles}
                disabled={seeding}
                className="bg-purple-600 hover:bg-purple-700"
              >
                {seeding ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Seeding Bundles...
                  </>
                ) : (
                  <>
                    <Database className="mr-2 h-4 w-4" />
                    Seed Sample Bundles
                  </>
                )}
              </Button>
              
              <Button
                onClick={() => fetchBundles()}
                variant="outline"
                className="text-white border-white/30 hover:bg-white/10"
              >
                Refresh Bundles
              </Button>
            </div>

            {seedResult && (
              <div className={`p-4 rounded border ${
                seedResult.success 
                  ? 'bg-green-600/20 border-green-400/30 text-green-300'
                  : 'bg-red-600/20 border-red-400/30 text-red-300'
              }`}>
                {seedResult.success ? (
                  <div>
                    <h4 className="font-medium mb-2">✅ Seeding Successful!</h4>
                    <div className="text-sm space-y-1">
                      <p>• {seedResult.stats?.bundles_created} bundles created</p>
                      <p>• {seedResult.stats?.creators_created} new creators added</p>
                      <p>• {seedResult.stats?.creator_links} creator-bundle links created</p>
                    </div>
                  </div>
                ) : (
                  <div>
                    <h4 className="font-medium mb-2">❌ Seeding Failed</h4>
                    <p className="text-sm">{seedResult.error}</p>
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Sample Bundles Preview */}
        <div className="mb-8">
          <h3 className="text-2xl font-bold text-white mb-4">Sample Bundle Themes</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {[
              { name: "Horror Masters", icon: "👻", color: "red" },
              { name: "Marvel & DC", icon: "🦸", color: "blue" },
              { name: "Sci-Fi Experts", icon: "🚀", color: "purple" },
              { name: "Action Breakdown", icon: "💥", color: "orange" },
              { name: "Indie Cinema", icon: "🎭", color: "green" },
              { name: "Streaming Guide", icon: "📺", color: "pink" }
            ].map((theme) => (
              <Card key={theme.name} className="bg-white/10 border-white/20 text-white">
                <CardContent className="p-4 text-center">
                  <div className="text-2xl mb-2">{theme.icon}</div>
                  <div className="text-sm font-medium">{theme.name}</div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Bundle Grid */}
        <BundleGrid
          bundles={bundles}
          subscribedBundleIds={getSubscribedBundleIds()}
          loading={bundlesLoading}
          error={bundlesError}
          onAddBundle={handleAddBundle}
          onLoadMore={loadMore}
          onRefresh={() => fetchBundles()}
          hasMore={hasMore}
          showFilters={true}
          onFilterChange={handleFilterChange}
          currentFilters={filters}
        />

        {/* Demo Notes */}
        <Card className="bg-white/10 border-white/20 mt-8">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">📝 Demo Notes</h3>
            <div className="text-gray-300 text-sm space-y-2">
              <p><strong>Authentication:</strong> Bundle subscriptions require user authentication (401 errors expected in demo)</p>
              <p><strong>Data Persistence:</strong> Sample data is stored in the database and persists between sessions</p>
              <p><strong>Real Integration:</strong> In production, bundles would be curated by AI algorithms and editorial teams</p>
              <p><strong>Refresh Schedule:</strong> Bundles would automatically refresh weekly with new creators and content</p>
              <p><strong>Analytics:</strong> User interactions (views, adds) are tracked for optimization</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

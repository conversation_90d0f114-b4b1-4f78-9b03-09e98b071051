import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Film, Users, Zap, Target, Play, BookOpen } from "lucide-react"
import Link from "next/link"

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Hero Section */}
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <div className="flex items-center justify-center mb-6">
            <Film className="h-12 w-12 text-purple-400 mr-3" />
            <h1 className="text-5xl font-bold text-white">SceneSniffer</h1>
          </div>
          <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto">
            Your personalized movie and TV content intelligence platform.
            Discover what to watch through trusted creators, AI-powered insights,
            and real-time streaming availability.
          </p>
          <div className="flex gap-4 justify-center">
            <Link href="/auth">
              <Button size="lg" className="bg-purple-600 hover:bg-purple-700">
                <Play className="mr-2 h-5 w-5" />
                Get Started
              </Button>
            </Link>
            <Link href="/feed">
              <Button variant="outline" size="lg" className="text-white border-white hover:bg-white hover:text-black">
                <BookOpen className="mr-2 h-5 w-5" />
                Explore Feed
              </Button>
            </Link>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid md:grid-cols-3 gap-8 mb-16">
          <Card className="bg-white/10 border-white/20 text-white">
            <CardHeader>
              <Users className="h-8 w-8 text-purple-400 mb-2" />
              <CardTitle>Creator-First Discovery</CardTitle>
              <CardDescription className="text-gray-300">
                Follow your favorite YouTube, Instagram, and Twitter creators.
                Get personalized recommendations based on their content.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="bg-white/10 border-white/20 text-white">
            <CardHeader>
              <Zap className="h-8 w-8 text-purple-400 mb-2" />
              <CardTitle>AI-Powered Insights</CardTitle>
              <CardDescription className="text-gray-300">
                Smart summarization of video content, automatic title detection,
                and content categorization with spoiler-free options.
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="bg-white/10 border-white/20 text-white">
            <CardHeader>
              <Target className="h-8 w-8 text-purple-400 mb-2" />
              <CardTitle>Streaming Intelligence</CardTitle>
              <CardDescription className="text-gray-300">
                Real-time "Where to Watch" information across all major platforms.
                Never miss when your watchlist items become available.
              </CardDescription>
            </CardHeader>
          </Card>
        </div>

        {/* How It Works */}
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-white mb-8">How SceneSniffer Works</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-purple-600 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-xl">1</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Choose Your Creators</h3>
              <p className="text-gray-300">
                Select your favorite movie and TV content creators from YouTube, Instagram, and Twitter.
              </p>
            </div>
            <div className="text-center">
              <div className="bg-purple-600 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-xl">2</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Get Your Feed</h3>
              <p className="text-gray-300">
                Receive a personalized "Cinematic Pulse" feed with AI-enhanced content and insights.
              </p>
            </div>
            <div className="text-center">
              <div className="bg-purple-600 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                <span className="text-white font-bold text-xl">3</span>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">Watch & Discover</h3>
              <p className="text-gray-300">
                Find where to stream, add to watchlist, and discover your next favorite show or movie.
              </p>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <Card className="bg-white/5 border-white/20 max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-white text-2xl">Ready to Transform Your Viewing Experience?</CardTitle>
              <CardDescription className="text-gray-300 text-lg">
                Join the beta and be among the first to experience the future of content discovery.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Link href="/auth">
                <Button size="lg" className="bg-purple-600 hover:bg-purple-700 w-full sm:w-auto">
                  <Play className="mr-2 h-5 w-5" />
                  Start Your Journey
                </Button>
              </Link>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

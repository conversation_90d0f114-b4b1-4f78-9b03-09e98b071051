import { NextRequest, NextResponse } from 'next/server'
// import { BundleAnalyticsService } from '@/lib/bundle-analytics'

// GET /api/bundles/[id]/analytics - Get analytics for a specific bundle
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Temporarily disabled - analytics service not available
    return NextResponse.json({
      success: true,
      data: {
        bundle_id: (await params).id,
        current_metrics: null,
        performance_history: [],
        period: {
          start: new Date().toISOString(),
          end: new Date().toISOString(),
          label: 'Analytics temporarily disabled'
        }
      }
    })

    // const { bundleId } = await params
    // const { searchParams } = new URL(request.url)
    // const period = searchParams.get('period') || '7d' // 7d, 30d, 90d
    // const calculate = searchParams.get('calculate') === 'true'

    // const analyticsService = new BundleAnalyticsService()

    // Calculate period dates
    const endDate = new Date()
    const startDate = new Date()
    
    switch (period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7)
        break
      case '30d':
        startDate.setDate(endDate.getDate() - 30)
        break
      case '90d':
        startDate.setDate(endDate.getDate() - 90)
        break
      default:
        startDate.setDate(endDate.getDate() - 7)
    }

    // Get existing performance data
    const performanceHistory = await analyticsService.getBundlePerformance(bundleId, 10)

    let currentMetrics = null
    if (calculate) {
      // Calculate current metrics for the period
      currentMetrics = await analyticsService.calculateBundleMetrics(bundleId, startDate, endDate)
    }

    return NextResponse.json({
      success: true,
      data: {
        bundle_id: bundleId,
        current_metrics: currentMetrics,
        performance_history: performanceHistory,
        period: {
          start: startDate.toISOString(),
          end: endDate.toISOString(),
          label: period
        }
      }
    })

  } catch (error) {
    console.error('Error fetching bundle analytics:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch bundle analytics',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// POST /api/bundles/[bundleId]/analytics - Calculate and store metrics for a bundle
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ bundleId: string }> }
) {
  try {
    const { bundleId } = await params
    const body = await request.json()
    const { period = '7d', store = true } = body

    const analyticsService = new BundleAnalyticsService()

    // Calculate period dates
    const endDate = new Date()
    const startDate = new Date()
    
    switch (period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7)
        break
      case '30d':
        startDate.setDate(endDate.getDate() - 30)
        break
      case '90d':
        startDate.setDate(endDate.getDate() - 90)
        break
      default:
        startDate.setDate(endDate.getDate() - 7)
    }

    // Calculate metrics
    const metrics = await analyticsService.calculateBundleMetrics(bundleId, startDate, endDate)

    // Store metrics if requested
    if (store) {
      const performanceData = {
        bundle_id: bundleId,
        metrics,
        raw_data: {
          total_views: 0, // These would be calculated in a real implementation
          total_interactions: 0,
          unique_users: 0,
          creators_added: 0,
          creators_removed: 0,
          user_ratings_count: 0,
          user_ratings_sum: 0
        },
        measurement_period: {
          start: startDate,
          end: endDate
        }
      }

      await analyticsService.storePerformanceMetrics(performanceData)
    }

    return NextResponse.json({
      success: true,
      data: {
        bundle_id: bundleId,
        metrics,
        period: {
          start: startDate.toISOString(),
          end: endDate.toISOString(),
          label: period
        },
        stored: store
      }
    })

  } catch (error) {
    console.error('Error calculating bundle metrics:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to calculate bundle metrics',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

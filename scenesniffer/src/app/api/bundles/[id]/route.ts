import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase-server'

interface RouteParams {
  params: {
    id: string
  }
}

// GET /api/bundles/[id] - Get bundle details with creators
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = await params
    const supabase = await createServerSupabaseClient()

    // Get bundle details
    const { data: bundle, error: bundleError } = await supabase
      .from('creator_bundles')
      .select(`
        id,
        title,
        description,
        tags,
        platform,
        creator_count,
        refreshed_at,
        ttl,
        status,
        created_at,
        updated_at
      `)
      .eq('id', id)
      .single()

    if (bundleError || !bundle) {
      return NextResponse.json({ 
        error: 'Bundle not found' 
      }, { status: 404 })
    }

    // Get creators in this bundle
    const { data: bundleCreators, error: creatorsError } = await supabase
      .from('bundle_creators')
      .select(`
        id,
        creator_id,
        position,
        score,
        created_at,
        creators (
          id,
          name,
          platform,
          handle,
          avatar_url,
          follower_count,
          verified,
          trust_score,
          created_at
        )
      `)
      .eq('bundle_id', id)
      .order('position', { ascending: true })

    if (creatorsError) {
      console.error('Error fetching bundle creators:', creatorsError)
      return NextResponse.json({ 
        error: 'Failed to fetch bundle creators' 
      }, { status: 500 })
    }

    // Format the response
    const bundleWithCreators = {
      ...bundle,
      creators: bundleCreators?.map(bc => ({
        id: bc.id,
        bundle_id: id,
        creator_id: bc.creator_id,
        position: bc.position,
        score: bc.score,
        created_at: bc.created_at,
        creator: bc.creators
      })) || []
    }

    return NextResponse.json({
      success: true,
      bundle: bundleWithCreators
    })

  } catch (error) {
    console.error('Error in bundle detail GET API:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}

// PUT /api/bundles/[id] - Update bundle (admin only)
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // TODO: Add admin role check here

    const body = await request.json()
    const {
      title,
      description,
      tags,
      status,
      ttl
    } = body

    // Update the bundle
    const { data: bundle, error: updateError } = await supabase
      .from('creator_bundles')
      .update({
        ...(title && { title }),
        ...(description !== undefined && { description }),
        ...(tags && { tags }),
        ...(status && { status }),
        ...(ttl && { ttl }),
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating bundle:', updateError)
      return NextResponse.json({ 
        error: 'Failed to update bundle' 
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: 'Bundle updated successfully',
      bundle
    })

  } catch (error) {
    console.error('Error in bundle PUT API:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}

// DELETE /api/bundles/[id] - Delete bundle (admin only)
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // TODO: Add admin role check here

    // Delete the bundle (cascade will handle bundle_creators)
    const { error: deleteError } = await supabase
      .from('creator_bundles')
      .delete()
      .eq('id', id)

    if (deleteError) {
      console.error('Error deleting bundle:', deleteError)
      return NextResponse.json({ 
        error: 'Failed to delete bundle' 
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: 'Bundle deleted successfully'
    })

  } catch (error) {
    console.error('Error in bundle DELETE API:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}

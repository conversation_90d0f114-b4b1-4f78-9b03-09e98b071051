import { NextRequest, NextResponse } from 'next/server'
import { createServiceRoleClient } from '@/lib/supabase-server'
import { BundleCurationEngine, CurationCriteria } from '@/lib/bundle-curation'

// Predefined bundle themes with AI curation criteria
const BUNDLE_THEMES = [
  {
    title: "Horror Movie Masters",
    description: "Top YouTube creators who specialize in horror movie reviews, theories, and deep dives into the scariest films ever made.",
    tags: ["horror", "scary", "supernatural", "thriller", "fear", "ghost", "zombie", "slasher"],
    platform: "youtube",
    criteria: {
      minTrustScore: 7.0,
      minActivityDays: 30,
      maxCreators: 10,
      diversityWeight: 0.3
    }
  },
  {
    title: "Marvel & DC Universe",
    description: "Your go-to creators for superhero movie breakdowns, comic book theories, and MCU/DCEU analysis.",
    tags: ["marvel", "dc", "superhero", "mcu", "dceu", "comics", "avengers", "batman", "superman"],
    platform: "youtube",
    criteria: {
      minTrustScore: 7.5,
      minActivityDays: 30,
      maxCreators: 12,
      diversityWeight: 0.2
    }
  },
  {
    title: "Sci-Fi & Fantasy Experts",
    description: "Creators who dive deep into science fiction and fantasy worlds, from Star Wars to Dune to Lord of the Rings.",
    tags: ["sci-fi", "fantasy", "star-wars", "dune", "lotr", "space", "alien", "future", "magic"],
    platform: "youtube",
    criteria: {
      minTrustScore: 7.0,
      minActivityDays: 30,
      maxCreators: 10,
      diversityWeight: 0.4
    }
  },
  {
    title: "Action & Thriller Breakdown",
    description: "High-octane creators covering action movies, thrillers, and adrenaline-pumping cinema analysis.",
    tags: ["action", "thriller", "stunts", "fight", "chase", "explosion", "spy", "heist"],
    platform: "youtube",
    criteria: {
      minTrustScore: 6.5,
      minActivityDays: 30,
      maxCreators: 8,
      diversityWeight: 0.3
    }
  },
  {
    title: "Indie & Art House Cinema",
    description: "Thoughtful creators exploring independent films, art house cinema, and underrated cinematic gems.",
    tags: ["indie", "art-house", "a24", "sundance", "cannes", "auteur", "experimental", "festival"],
    platform: "youtube",
    criteria: {
      minTrustScore: 8.0,
      minActivityDays: 45,
      maxCreators: 8,
      diversityWeight: 0.5
    }
  },
  {
    title: "Streaming Service Spotlights",
    description: "Creators who focus on what's new and noteworthy across Netflix, HBO Max, Disney+, and other streaming platforms.",
    tags: ["netflix", "hbo-max", "disney-plus", "streaming", "new-releases", "binge", "series"],
    platform: "youtube",
    criteria: {
      minTrustScore: 6.0,
      minActivityDays: 14,
      maxCreators: 12,
      diversityWeight: 0.2
    }
  }
]

// POST /api/bundles/generate - Generate AI-curated bundles
export async function POST(request: NextRequest) {
  try {
    console.log('🤖 Starting AI-powered bundle generation...')
    
    const serviceSupabase = createServiceRoleClient()
    const curationEngine = new BundleCurationEngine()
    
    let body = {}
    try {
      body = await request.json()
    } catch (error) {
      // No JSON body provided, use defaults
      console.log('No JSON body provided, using defaults')
    }

    const {
      themes = BUNDLE_THEMES,
      platform = 'youtube',
      forceRegenerate = false
    } = body

    let generatedBundles = 0
    let totalCreatorsAdded = 0
    const results = []

    for (const theme of themes) {
      console.log(`\n🎯 Generating bundle: ${theme.title}`)
      
      // Check if bundle already exists (unless force regenerate)
      if (!forceRegenerate) {
        const { data: existingBundle } = await serviceSupabase
          .from('creator_bundles')
          .select('id, title')
          .eq('title', theme.title)
          .eq('platform', platform)
          .single()

        if (existingBundle) {
          console.log(`⏭️ Bundle "${theme.title}" already exists, skipping...`)
          results.push({
            title: theme.title,
            status: 'skipped',
            reason: 'Already exists'
          })
          continue
        }
      }

      // Prepare curation criteria
      const criteria: CurationCriteria = {
        platform,
        theme: theme.title,
        tags: theme.tags,
        ...theme.criteria
      }

      try {
        // Use AI to curate creators for this bundle
        const curatedCreators = await curationEngine.curateBundle(criteria)
        
        if (curatedCreators.length === 0) {
          console.log(`❌ No suitable creators found for "${theme.title}"`)
          results.push({
            title: theme.title,
            status: 'failed',
            reason: 'No suitable creators found'
          })
          continue
        }

        // Create the bundle
        const { data: bundle, error: bundleError } = await serviceSupabase
          .from('creator_bundles')
          .insert({
            title: theme.title,
            description: theme.description,
            tags: theme.tags,
            platform,
            creator_count: curatedCreators.length,
            status: 'active',
            ttl: 604800 // 1 week
          })
          .select()
          .single()

        if (bundleError) {
          console.error(`Error creating bundle "${theme.title}":`, bundleError)
          results.push({
            title: theme.title,
            status: 'failed',
            reason: 'Database error creating bundle',
            error: bundleError
          })
          continue
        }

        // Add creators to the bundle
        const bundleCreators = curatedCreators.map((scoredCreator, index) => ({
          bundle_id: bundle.id,
          creator_id: scoredCreator.creator.id,
          position: index,
          score: scoredCreator.score
        }))

        const { error: creatorsError } = await serviceSupabase
          .from('bundle_creators')
          .insert(bundleCreators)

        if (creatorsError) {
          console.error(`Error adding creators to bundle "${theme.title}":`, creatorsError)
          results.push({
            title: theme.title,
            status: 'partial',
            reason: 'Bundle created but failed to add creators',
            bundle_id: bundle.id,
            error: creatorsError
          })
          continue
        }

        generatedBundles++
        totalCreatorsAdded += curatedCreators.length

        console.log(`✅ Successfully created "${theme.title}" with ${curatedCreators.length} creators`)
        
        results.push({
          title: theme.title,
          status: 'success',
          bundle_id: bundle.id,
          creators_added: curatedCreators.length,
          top_creators: curatedCreators.slice(0, 3).map(sc => ({
            name: sc.creator.name,
            handle: sc.creator.handle,
            score: sc.score,
            reasons: sc.reasons
          }))
        })

      } catch (error) {
        console.error(`Error processing bundle "${theme.title}":`, error)
        results.push({
          title: theme.title,
          status: 'failed',
          reason: 'Processing error',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    console.log(`\n🎉 AI Bundle Generation Complete!`)
    console.log(`📊 Generated ${generatedBundles} bundles with ${totalCreatorsAdded} total creators`)

    return NextResponse.json({
      success: true,
      message: 'AI-powered bundle generation completed',
      stats: {
        bundles_generated: generatedBundles,
        total_creators_added: totalCreatorsAdded,
        themes_processed: themes.length
      },
      results,
      ai_curation: {
        engine: 'BundleCurationEngine v1.0',
        criteria_used: {
          trust_score_range: '6.0-8.0+',
          activity_window: '14-45 days',
          max_creators_per_bundle: '8-12',
          diversity_weighting: '0.2-0.5'
        },
        scoring_factors: [
          'Trust Score (25%)',
          'Activity Score (20%)', 
          'Topic Relevance (30%)',
          'Quality Score (20%)',
          'Verification Bonus (5%)'
        ]
      }
    })

  } catch (error) {
    console.error('Error in AI bundle generation:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error during AI bundle generation',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { createServiceRoleClient } from '@/lib/supabase-server'
import { BundleCurationEngine, CurationCriteria } from '@/lib/bundle-curation'
import { BackgroundJobService } from '@/lib/background-jobs'

// POST /api/bundles/refresh - Refresh existing bundles with fresh AI curation (Background Job)
export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Starting bundle refresh process...')

    const serviceSupabase = createServiceRoleClient()
    const jobService = new BackgroundJobService()

    const body = await request.json()
    const {
      platform = 'youtube',
      maxAge = 604800, // 1 week in seconds
      forceRefresh = false
    } = body

    // Create background job immediately
    const jobId = await jobService.createJob('bundle_refresh', {
      platform,
      maxAge,
      forceRefresh
    })

    // Start background processing (don't await this)
    processRefreshInBackground(jobId, platform, maxAge, forceRefresh)
      .catch(error => {
        console.error('Background refresh failed:', error)
        jobService.failJob(jobId, error.message)
      })

    // Return immediately with job ID
    return NextResponse.json({
      success: true,
      message: 'Bundle refresh started in background',
      jobId,
      status: 'started'
    })

  } catch (error) {
    console.error('Error starting bundle refresh:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to start bundle refresh',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// Background processing function
async function processRefreshInBackground(
  jobId: string,
  platform: string,
  maxAge: number,
  forceRefresh: boolean
) {
  const serviceSupabase = createServiceRoleClient()
  const curationEngine = new BundleCurationEngine()
  const jobService = new BackgroundJobService()

  try {
    await jobService.startJob(jobId)

    // Get bundles that need refreshing
    const cutoffDate = new Date()
    cutoffDate.setSeconds(cutoffDate.getSeconds() - maxAge)

    let bundleQuery = serviceSupabase
      .from('creator_bundles')
      .select('*')
      .eq('platform', platform)
      .eq('status', 'active')

    if (!forceRefresh) {
      bundleQuery = bundleQuery.lt('refreshed_at', cutoffDate.toISOString())
    }

    const { data: bundlesToRefresh, error: fetchError } = await bundleQuery

    if (fetchError) {
      throw new Error(`Failed to fetch bundles for refresh: ${fetchError.message}`)
    }

    if (!bundlesToRefresh || bundlesToRefresh.length === 0) {
      await jobService.completeJob(jobId, {
        message: 'No bundles need refreshing',
        stats: {
          bundles_checked: 0,
          bundles_refreshed: 0,
          creators_updated: 0
        }
      })
      return
    }

    console.log(`📦 Found ${bundlesToRefresh.length} bundles to refresh`)
    await jobService.updateProgress(jobId, 0, bundlesToRefresh.length)

    let refreshedBundles = 0
    let totalCreatorsUpdated = 0
    const results = []

    for (let i = 0; i < bundlesToRefresh.length; i++) {
      const bundle = bundlesToRefresh[i]
      console.log(`\n🔄 Refreshing bundle: ${bundle.title}`)
      
      try {
        // Prepare curation criteria based on bundle metadata
        const criteria: CurationCriteria = {
          platform: bundle.platform,
          theme: bundle.title,
          tags: bundle.tags || [],
          minTrustScore: 6.0,
          minActivityDays: 30,
          maxCreators: bundle.creator_count || 10,
          diversityWeight: 0.3
        }

        // Get current creators in bundle
        const { data: currentCreators, error: currentError } = await serviceSupabase
          .from('bundle_creators')
          .select(`
            creator_id,
            creators (
              id,
              name,
              handle,
              trust_score
            )
          `)
          .eq('bundle_id', bundle.id)

        if (currentError) {
          console.error(`Error fetching current creators for ${bundle.title}:`, currentError)
          results.push({
            bundle_id: bundle.id,
            title: bundle.title,
            status: 'failed',
            reason: 'Failed to fetch current creators'
          })
          continue
        }

        // Use AI to curate fresh creators
        const freshCreators = await curationEngine.curateBundle(criteria)

        if (freshCreators.length === 0) {
          console.log(`❌ No suitable creators found for refreshing "${bundle.title}"`)
          results.push({
            bundle_id: bundle.id,
            title: bundle.title,
            status: 'failed',
            reason: 'No suitable creators found'
          })
          // Update progress
          await jobService.updateProgress(jobId, i + 1, bundlesToRefresh.length)
          continue
        }

        // Calculate changes
        const currentCreatorIds = new Set(currentCreators?.map(cc => cc.creator_id) || [])
        const freshCreatorIds = new Set(freshCreators.map(fc => fc.creator.id))
        
        const addedCreators = freshCreators.filter(fc => !currentCreatorIds.has(fc.creator.id))
        const removedCreatorIds = Array.from(currentCreatorIds).filter(id => !freshCreatorIds.has(id))
        const retainedCount = freshCreators.filter(fc => currentCreatorIds.has(fc.creator.id)).length

        console.log(`📊 Changes: +${addedCreators.length} added, -${removedCreatorIds.length} removed, ${retainedCount} retained`)

        // Remove old creators
        if (removedCreatorIds.length > 0) {
          const { error: removeError } = await serviceSupabase
            .from('bundle_creators')
            .delete()
            .eq('bundle_id', bundle.id)
            .in('creator_id', removedCreatorIds)

          if (removeError) {
            console.error(`Error removing old creators from ${bundle.title}:`, removeError)
          }
        }

        // Update all creators (this will update positions and scores)
        const { error: deleteAllError } = await serviceSupabase
          .from('bundle_creators')
          .delete()
          .eq('bundle_id', bundle.id)

        if (deleteAllError) {
          console.error(`Error clearing bundle creators for ${bundle.title}:`, deleteAllError)
          results.push({
            bundle_id: bundle.id,
            title: bundle.title,
            status: 'failed',
            reason: 'Failed to clear existing creators'
          })
          continue
        }

        // Add fresh creators
        const bundleCreators = freshCreators.map((scoredCreator, index) => ({
          bundle_id: bundle.id,
          creator_id: scoredCreator.creator.id,
          position: index,
          score: scoredCreator.score
        }))

        const { error: addError } = await serviceSupabase
          .from('bundle_creators')
          .insert(bundleCreators)

        if (addError) {
          console.error(`Error adding fresh creators to ${bundle.title}:`, addError)
          results.push({
            bundle_id: bundle.id,
            title: bundle.title,
            status: 'failed',
            reason: 'Failed to add fresh creators'
          })
          continue
        }

        // Update bundle metadata
        const { error: updateError } = await serviceSupabase
          .from('creator_bundles')
          .update({
            creator_count: freshCreators.length,
            refreshed_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
          .eq('id', bundle.id)

        if (updateError) {
          console.error(`Error updating bundle metadata for ${bundle.title}:`, updateError)
        }

        refreshedBundles++
        totalCreatorsUpdated += freshCreators.length

        console.log(`✅ Successfully refreshed "${bundle.title}" with ${freshCreators.length} creators`)
        
        results.push({
          bundle_id: bundle.id,
          title: bundle.title,
          status: 'success',
          changes: {
            added: addedCreators.length,
            removed: removedCreatorIds.length,
            retained: retainedCount,
            total: freshCreators.length
          },
          top_new_creators: addedCreators.slice(0, 3).map(sc => ({
            name: sc.creator.name,
            handle: sc.creator.handle,
            score: sc.score,
            reasons: sc.reasons
          }))
        })

      } catch (error) {
        console.error(`Error refreshing bundle "${bundle.title}":`, error)
        results.push({
          bundle_id: bundle.id,
          title: bundle.title,
          status: 'failed',
          reason: 'Processing error',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }

      // Update progress after each bundle
      await jobService.updateProgress(jobId, i + 1, bundlesToRefresh.length)
    }

    console.log(`\n🎉 Bundle Refresh Complete!`)
    console.log(`📊 Refreshed ${refreshedBundles}/${bundlesToRefresh.length} bundles`)

    // Complete the job with results
    await jobService.completeJob(jobId, {
      success: true,
      message: 'Bundle refresh completed',
      stats: {
        bundles_checked: bundlesToRefresh.length,
        bundles_refreshed: refreshedBundles,
        creators_updated: totalCreatorsUpdated
      },
      results,
      refresh_criteria: {
        platform,
        max_age_seconds: maxAge,
        force_refresh: forceRefresh,
        cutoff_date: cutoffDate.toISOString()
      }
    })

  } catch (error) {
    console.error('Error in bundle refresh:', error)
    await jobService.failJob(jobId, error instanceof Error ? error.message : 'Unknown error')
  }
}

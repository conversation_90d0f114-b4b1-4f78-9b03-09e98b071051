import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase-server'

// GET /api/bundles - List all active bundles
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const platform = searchParams.get('platform') || 'youtube'
    const status = searchParams.get('status') || 'active'
    const limit = parseInt(searchParams.get('limit') || '20')
    const offset = parseInt(searchParams.get('offset') || '0')

    const supabase = await createServerSupabaseClient()

    // Get bundles with creator count and basic info
    const { data: bundles, error: bundlesError } = await supabase
      .from('creator_bundles')
      .select(`
        id,
        title,
        description,
        tags,
        platform,
        creator_count,
        refreshed_at,
        ttl,
        status,
        created_at,
        updated_at
      `)
      .eq('platform', platform)
      .eq('status', status)
      .order('refreshed_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (bundlesError) {
      console.error('Error fetching bundles:', bundlesError)
      return NextResponse.json({ 
        error: 'Failed to fetch bundles' 
      }, { status: 500 })
    }

    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from('creator_bundles')
      .select('*', { count: 'exact', head: true })
      .eq('platform', platform)
      .eq('status', status)

    if (countError) {
      console.error('Error counting bundles:', countError)
    }

    return NextResponse.json({
      success: true,
      bundles: bundles || [],
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit
      },
      filters: {
        platform,
        status
      }
    })

  } catch (error) {
    console.error('Error in bundles GET API:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}

// POST /api/bundles - Create a new bundle (admin only)
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // TODO: Add admin role check here
    // For now, any authenticated user can create bundles for demo purposes

    const body = await request.json()
    const {
      title,
      description,
      tags = [],
      platform = 'youtube',
      creator_ids = [],
      ttl = 604800 // 1 week default
    } = body

    // Validate required fields
    if (!title || !platform) {
      return NextResponse.json({ 
        error: 'Title and platform are required' 
      }, { status: 400 })
    }

    // Validate platform
    const validPlatforms = ['youtube', 'instagram', 'twitter', 'tiktok', 'twitch']
    if (!validPlatforms.includes(platform)) {
      return NextResponse.json({ 
        error: 'Invalid platform' 
      }, { status: 400 })
    }

    // Create the bundle
    const { data: bundle, error: bundleError } = await supabase
      .from('creator_bundles')
      .insert({
        title,
        description,
        tags,
        platform,
        creator_count: creator_ids.length,
        ttl,
        status: 'active'
      })
      .select()
      .single()

    if (bundleError) {
      console.error('Error creating bundle:', bundleError)
      return NextResponse.json({ 
        error: 'Failed to create bundle' 
      }, { status: 500 })
    }

    // Add creators to the bundle if provided
    if (creator_ids.length > 0) {
      const bundleCreators = creator_ids.map((creator_id: string, index: number) => ({
        bundle_id: bundle.id,
        creator_id,
        position: index,
        score: 10.0 - (index * 0.1) // Simple scoring based on position
      }))

      const { error: creatorsError } = await supabase
        .from('bundle_creators')
        .insert(bundleCreators)

      if (creatorsError) {
        console.error('Error adding creators to bundle:', creatorsError)
        // Don't fail the request, just log the error
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Bundle created successfully',
      bundle: {
        ...bundle,
        creator_count: creator_ids.length
      }
    })

  } catch (error) {
    console.error('Error in bundles POST API:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { AIBundleDiscoveryEngine } from '@/lib/ai-bundle-discovery'

export async function POST(request: NextRequest) {
  try {
    console.log('🤖 Starting AI-powered bundle discovery...')
    
    const body = await request.json()
    const { 
      mode = 'trending', // 'trending' | 'custom' | 'seasonal'
      customTheme = null,
      maxBundles = 5,
      platform = 'youtube'
    } = body

    const discoveryEngine = new AIBundleDiscoveryEngine()
    const results = []

    if (mode === 'trending') {
      // Generate trending themes and discover creators
      console.log('🔥 Generating trending bundle themes...')
      const themes = await discoveryEngine.generateTrendingThemes()
      
      for (const theme of themes.slice(0, maxBundles)) {
        try {
          console.log(`🎯 Processing theme: ${theme.title}`)
          
          // Discover creators for this theme
          const creators = await discoveryEngine.discoverCreatorsForTheme(theme)
          
          if (creators.length >= 6) { // Minimum viable bundle size
            // Generate the bundle
            const bundle = await discoveryEngine.generateBundle(theme, creators)
            
            results.push({
              bundle,
              theme,
              creators: creators.length,
              status: 'created'
            })
            
            console.log(`✅ Created bundle: ${theme.title} with ${creators.length} creators`)
          } else {
            results.push({
              theme,
              creators: creators.length,
              status: 'insufficient_creators',
              reason: `Only found ${creators.length} creators, need at least 6`
            })
            
            console.log(`⚠️ Skipped theme: ${theme.title} - insufficient creators`)
          }
          
          // Rate limiting pause
          await new Promise(resolve => setTimeout(resolve, 2000))
          
        } catch (error) {
          console.error(`❌ Error processing theme ${theme.title}:`, error)
          results.push({
            theme,
            status: 'error',
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }
    } else if (mode === 'custom' && customTheme) {
      // Process custom theme
      console.log(`🎨 Processing custom theme: ${customTheme.title}`)
      
      const creators = await discoveryEngine.discoverCreatorsForTheme(customTheme)
      
      if (creators.length >= 6) {
        const bundle = await discoveryEngine.generateBundle(customTheme, creators)
        results.push({
          bundle,
          theme: customTheme,
          creators: creators.length,
          status: 'created'
        })
      } else {
        results.push({
          theme: customTheme,
          creators: creators.length,
          status: 'insufficient_creators'
        })
      }
    }

    const successful = results.filter(r => r.status === 'created')
    const failed = results.filter(r => r.status !== 'created')

    return NextResponse.json({
      success: successful.length > 0,
      message: `AI Discovery completed: ${successful.length} bundles created, ${failed.length} failed`,
      results: {
        created: successful,
        failed: failed,
        summary: {
          totalBundles: successful.length,
          totalCreators: successful.reduce((sum, r) => sum + r.creators, 0),
          averageCreatorsPerBundle: successful.length > 0 
            ? Math.round(successful.reduce((sum, r) => sum + r.creators, 0) / successful.length)
            : 0
        }
      },
      metadata: {
        mode,
        platform,
        timestamp: new Date().toISOString(),
        processingTime: Date.now()
      }
    })

  } catch (error) {
    console.error('❌ AI Discovery error:', error)
    return NextResponse.json({
      success: false,
      error: 'AI Discovery failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET endpoint for discovery status and configuration
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')

    if (action === 'status') {
      // Return discovery engine status
      return NextResponse.json({
        status: 'ready',
        capabilities: {
          sources: ['youtube', 'reddit', 'twitter', 'webscraping', 'tmdb'],
          modes: ['trending', 'custom', 'seasonal'],
          maxBundlesPerRun: 10,
          minCreatorsPerBundle: 6,
          maxCreatorsPerBundle: 15
        },
        rateLimit: {
          youtube: '10,000 requests/day',
          reddit: '1,000 requests/day',
          twitter: '500 requests/day',
          openai: '3,000 requests/day'
        }
      })
    }

    if (action === 'themes') {
      // Return available theme templates
      const themeTemplates = [
        {
          title: "Horror Movie Masters",
          description: "Creators who specialize in horror movie analysis and reviews",
          keywords: ["horror", "scary", "supernatural", "thriller"],
          targetAudience: "Horror movie fans",
          expectedCreators: 10
        },
        {
          title: "Marvel & DC Universe",
          description: "Superhero movie experts and comic book analysts",
          keywords: ["marvel", "dc", "superhero", "mcu", "dceu"],
          targetAudience: "Comic book and superhero fans",
          expectedCreators: 12
        },
        {
          title: "Indie Cinema Curators",
          description: "Creators focused on independent and art house films",
          keywords: ["indie", "independent", "art house", "festival"],
          targetAudience: "Art film enthusiasts",
          expectedCreators: 8
        },
        {
          title: "Sci-Fi Visionaries",
          description: "Science fiction movie analysis and futurism",
          keywords: ["sci-fi", "science fiction", "space", "future", "alien"],
          targetAudience: "Science fiction fans",
          expectedCreators: 10
        }
      ]

      return NextResponse.json({
        success: true,
        themes: themeTemplates,
        customThemeSupport: true
      })
    }

    // Default: return discovery engine info
    return NextResponse.json({
      name: "AI Bundle Discovery Engine",
      version: "1.0.0",
      description: "Intelligent creator bundle discovery using multi-source AI analysis",
      endpoints: {
        discover: "POST /api/bundles/ai-discover",
        status: "GET /api/bundles/ai-discover?action=status",
        themes: "GET /api/bundles/ai-discover?action=themes"
      },
      usage: {
        trending: "POST with {mode: 'trending', maxBundles: 5}",
        custom: "POST with {mode: 'custom', customTheme: {...}}",
        seasonal: "POST with {mode: 'seasonal'}"
      }
    })

  } catch (error) {
    console.error('❌ Discovery API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Discovery API failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

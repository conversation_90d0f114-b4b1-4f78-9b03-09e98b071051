import { NextRequest, NextResponse } from 'next/server'
// import { BundleAnalyticsService } from '@/lib/bundle-analytics'

// GET /api/bundles/analytics - Get analytics overview for all bundles
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    const period = searchParams.get('period') || '7d' // 7d, 30d, 90d

    const analyticsService = new BundleAnalyticsService()

    // Calculate period dates
    const endDate = new Date()
    const startDate = new Date()
    
    switch (period) {
      case '7d':
        startDate.setDate(endDate.getDate() - 7)
        break
      case '30d':
        startDate.setDate(endDate.getDate() - 30)
        break
      case '90d':
        startDate.setDate(endDate.getDate() - 90)
        break
      default:
        startDate.setDate(endDate.getDate() - 7)
    }

    // Get trending topics
    const trendingTopics = await analyticsService.getTrendingTopics(20)

    return NextResponse.json({
      success: true,
      data: {
        trending_topics: trendingTopics,
        period: {
          start: startDate.toISOString(),
          end: endDate.toISOString(),
          label: period
        }
      }
    })

  } catch (error) {
    console.error('Error fetching bundle analytics:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch bundle analytics',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// POST /api/bundles/analytics - Record user interaction
export async function POST(request: NextRequest) {
  try {
    // Temporarily disabled - analytics service not available
    return NextResponse.json({
      success: true,
      message: 'Analytics temporarily disabled'
    })

    // const body = await request.json()
    // const {
    //   bundle_id,
    //   user_id,
    //   interaction_type,
    //   interaction_data,
    //   session_id,
    //   user_agent,
    //   referrer
    // } = body

    // if (!bundle_id || !interaction_type) {
    //   return NextResponse.json({
    //     success: false,
    //     error: 'Missing required fields: bundle_id and interaction_type'
    //   }, { status: 400 })
    // }

    // const analyticsService = new BundleAnalyticsService()

    // await analyticsService.recordInteraction({
    //   bundle_id,
    //   user_id,
    //   interaction_type,
    //   interaction_data,
    //   session_id,
    //   user_agent,
    //   referrer
    // })

    // return NextResponse.json({
    //   success: true,
    //   message: 'Interaction recorded successfully'
    // })

  } catch (error) {
    console.error('Error recording interaction:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to record interaction',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

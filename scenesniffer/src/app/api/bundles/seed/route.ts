import { NextRequest, NextResponse } from 'next/server'
import { createServiceRoleClient } from '@/lib/supabase-server'

// Sample YouTube creator bundles for seeding
const SAMPLE_BUNDLES = [
  {
    title: "Horror Movie Masters",
    description: "Top YouTube creators who specialize in horror movie reviews, theories, and deep dives into the scariest films ever made.",
    tags: ["horror", "scary", "reviews", "theories", "supernatural"],
    platform: "youtube",
    creators: [
      { handle: "DeadMeatJames", name: "Dead Meat", score: 10.0 },
      { handle: "CinemaSins", name: "CinemaSins", score: 9.5 },
      { handle: "FoundFlix", name: "FoundFlix", score: 9.0 },
      { handle: "SpookyRice", name: "Spooky Rice", score: 8.5 },
      { handle: "HorrorStories", name: "Horror Stories", score: 8.0 },
      { handle: "Mr<PERSON>reepyPasta", name: "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", score: 7.5 },
      { handle: "Chi<PERSON><PERSON>arra<PERSON>", name: "<PERSON><PERSON>", score: 7.0 },
      { handle: "NukesTop5", name: "Nuke's Top 5", score: 6.5 }
    ]
  },
  {
    title: "Marvel & DC Universe",
    description: "Your go-to creators for superhero movie breakdowns, comic book theories, and MCU/DCEU analysis.",
    tags: ["marvel", "dc", "superhero", "mcu", "dceu", "comics"],
    platform: "youtube",
    creators: [
      { handle: "NewRockstars", name: "New Rockstars", score: 10.0 },
      { handle: "ScreenCrush", name: "ScreenCrush", score: 9.5 },
      { handle: "ComicBookCast2", name: "ComicBookCast2", score: 9.0 },
      { handle: "EmergencyAwesome", name: "Emergency Awesome", score: 8.5 },
      { handle: "ComicsExplained", name: "Comics Explained", score: 8.0 },
      { handle: "HeavySpoilers", name: "Heavy Spoilers", score: 7.5 },
      { handle: "FilmTheorists", name: "The Film Theorists", score: 7.0 },
      { handle: "CBR", name: "CBR", score: 6.5 }
    ]
  },
  {
    title: "Sci-Fi & Fantasy Experts",
    description: "Creators who dive deep into science fiction and fantasy worlds, from Star Wars to Dune to Lord of the Rings.",
    tags: ["sci-fi", "fantasy", "star-wars", "dune", "lotr", "space"],
    platform: "youtube",
    creators: [
      { handle: "StarWarsTheory", name: "Star Wars Theory", score: 10.0 },
      { handle: "HelloGreedo", name: "HelloGreedo", score: 9.5 },
      { handle: "NerdOfTheRings", name: "Nerd of the Rings", score: 9.0 },
      { handle: "AltShiftX", name: "Alt Shift X", score: 8.5 },
      { handle: "IdeasOfIceAndFire", name: "Ideas of Ice and Fire", score: 8.0 },
      { handle: "CivilizationEx", name: "CivilizationEx", score: 7.5 },
      { handle: "IsaacArthur", name: "Isaac Arthur", score: 7.0 },
      { handle: "TempestFilms", name: "Tempest Films", score: 6.5 }
    ]
  },
  {
    title: "Action & Thriller Breakdown",
    description: "High-octane creators covering action movies, thrillers, and adrenaline-pumping cinema analysis.",
    tags: ["action", "thriller", "stunts", "fight-scenes", "explosions"],
    platform: "youtube",
    creators: [
      { handle: "CorridorCrew", name: "Corridor Crew", score: 10.0 },
      { handle: "StuntmenReact", name: "Stuntmen React", score: 9.5 },
      { handle: "JohnWickBreakdown", name: "John Wick Breakdown", score: 9.0 },
      { handle: "ActionMovieAnatomy", name: "Action Movie Anatomy", score: 8.5 },
      { handle: "FilmmakerIQ", name: "Filmmaker IQ", score: 8.0 },
      { handle: "RocketJump", name: "RocketJump", score: 7.5 },
      { handle: "FilmRiot", name: "Film Riot", score: 7.0 },
      { handle: "IndieMogul", name: "Indie Mogul", score: 6.5 }
    ]
  },
  {
    title: "Indie & Art House Cinema",
    description: "Thoughtful creators exploring independent films, art house cinema, and underrated cinematic gems.",
    tags: ["indie", "art-house", "a24", "sundance", "cannes", "auteur"],
    platform: "youtube",
    creators: [
      { handle: "TheCloserLook", name: "The Closer Look", score: 10.0 },
      { handle: "LessonsFromTheScreenplay", name: "Lessons from the Screenplay", score: 9.5 },
      { handle: "NerdWriter1", name: "Nerdwriter1", score: 9.0 },
      { handle: "KaptainKristian", name: "kaptainkristian", score: 8.5 },
      { handle: "FilmJoy", name: "FilmJoy", score: 8.0 },
      { handle: "RalphTheMovieMaker", name: "ralphthemoviemaker", score: 7.5 },
      { handle: "YourMovieSucks", name: "YourMovieSucks", score: 7.0 },
      { handle: "RedLetterMedia", name: "RedLetterMedia", score: 6.5 }
    ]
  },
  {
    title: "Streaming Service Spotlights",
    description: "Creators who focus on what's new and noteworthy across Netflix, HBO Max, Disney+, and other streaming platforms.",
    tags: ["netflix", "hbo-max", "disney-plus", "streaming", "new-releases"],
    platform: "youtube",
    creators: [
      { handle: "WhatToWatch", name: "What to Watch", score: 10.0 },
      { handle: "StreamingGuide", name: "Streaming Guide", score: 9.5 },
      { handle: "NetflixLife", name: "Netflix Life", score: 9.0 },
      { handle: "HBOMaxReviews", name: "HBO Max Reviews", score: 8.5 },
      { handle: "DisneyPlusNews", name: "Disney+ News", score: 8.0 },
      { handle: "StreamingWars", name: "Streaming Wars", score: 7.5 },
      { handle: "BingeWatch", name: "Binge Watch", score: 7.0 },
      { handle: "CordCutters", name: "Cord Cutters", score: 6.5 }
    ]
  }
]

// POST /api/bundles/seed - Seed sample bundles (admin only)
export async function POST(request: NextRequest) {
  try {
    const serviceSupabase = createServiceRoleClient()
    
    console.log('🌱 Starting bundle seeding process...')
    
    let createdBundles = 0
    let createdCreators = 0
    let linkedCreators = 0

    for (const bundleData of SAMPLE_BUNDLES) {
      console.log(`Creating bundle: ${bundleData.title}`)
      
      // Create the bundle
      const { data: bundle, error: bundleError } = await serviceSupabase
        .from('creator_bundles')
        .insert({
          title: bundleData.title,
          description: bundleData.description,
          tags: bundleData.tags,
          platform: bundleData.platform,
          creator_count: bundleData.creators.length,
          status: 'active',
          ttl: 604800 // 1 week
        })
        .select()
        .single()

      if (bundleError) {
        console.error(`Error creating bundle ${bundleData.title}:`, bundleError)
        console.error('Full error details:', JSON.stringify(bundleError, null, 2))
        continue
      }

      createdBundles++
      console.log(`✅ Created bundle: ${bundle.title}`)

      // Create/link creators
      for (let i = 0; i < bundleData.creators.length; i++) {
        const creatorData = bundleData.creators[i]
        
        // Check if creator exists
        const { data: existingCreator } = await serviceSupabase
          .from('creators')
          .select('id')
          .eq('platform', bundleData.platform)
          .eq('handle', creatorData.handle)
          .single()

        let creatorId: string

        if (existingCreator) {
          creatorId = existingCreator.id
        } else {
          // Create new creator
          const { data: newCreator, error: creatorError } = await serviceSupabase
            .from('creators')
            .insert({
              name: creatorData.name,
              platform: bundleData.platform,
              handle: creatorData.handle,
              verified: Math.random() > 0.5, // Random verification
              trust_score: Math.round((Math.random() * 3 + 7) * 10) / 10, // 7.0-10.0
              follower_count: Math.floor(Math.random() * 1000000) + 100000 // 100k-1.1M
            })
            .select('id')
            .single()

          if (creatorError) {
            console.error(`Error creating creator ${creatorData.handle}:`, creatorError)
            continue
          }

          creatorId = newCreator.id
          createdCreators++
        }

        // Link creator to bundle
        const { error: linkError } = await serviceSupabase
          .from('bundle_creators')
          .insert({
            bundle_id: bundle.id,
            creator_id: creatorId,
            position: i,
            score: creatorData.score
          })

        if (linkError) {
          console.error(`Error linking creator ${creatorData.handle} to bundle:`, linkError)
        } else {
          linkedCreators++
        }
      }
    }

    console.log('🎉 Bundle seeding completed!')
    
    return NextResponse.json({
      success: true,
      message: 'Bundle seeding completed successfully',
      stats: {
        bundles_created: createdBundles,
        creators_created: createdCreators,
        creator_links: linkedCreators,
        total_bundles: SAMPLE_BUNDLES.length
      }
    })

  } catch (error) {
    console.error('Error in bundle seeding:', error)
    return NextResponse.json({ 
      error: 'Internal server error during seeding',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

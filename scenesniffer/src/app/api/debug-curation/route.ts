import { NextRequest, NextResponse } from 'next/server'
import { createServiceRoleClient } from '@/lib/supabase-server'

export async function POST(request: NextRequest) {
  try {
    console.log('🔍 Debugging AI curation system...')
    
    const serviceSupabase = createServiceRoleClient()
    
    // Step 1: Check all creators
    console.log('Step 1: Checking all creators...')
    const { data: allCreators, error: creatorsError } = await serviceSupabase
      .from('creators')
      .select('*')
    
    if (creatorsError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch creators',
        details: creatorsError
      }, { status: 500 })
    }
    
    console.log(`Found ${allCreators?.length || 0} total creators`)
    
    // Step 2: Check creators by platform
    const { data: youtubeCreators, error: youtubeError } = await serviceSupabase
      .from('creators')
      .select('*')
      .eq('platform', 'youtube')
    
    console.log(`Found ${youtubeCreators?.length || 0} YouTube creators`)
    
    // Step 3: Check creators with trust score >= 6.0
    const { data: trustedCreators, error: trustedError } = await serviceSupabase
      .from('creators')
      .select('*')
      .eq('platform', 'youtube')
      .gte('trust_score', 6.0)
    
    console.log(`Found ${trustedCreators?.length || 0} trusted YouTube creators`)
    
    // Step 4: Check content for creators
    console.log('Step 4: Checking content...')
    const { data: allContent, error: contentError } = await serviceSupabase
      .from('content')
      .select('*')
    
    console.log(`Found ${allContent?.length || 0} total content pieces`)
    
    // Step 5: Check recent content (last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
    
    const { data: recentContent, error: recentError } = await serviceSupabase
      .from('content')
      .select('*')
      .gte('published_at', thirtyDaysAgo.toISOString())
    
    console.log(`Found ${recentContent?.length || 0} recent content pieces`)
    
    // Step 6: Check creators with recent content
    const creatorsWithRecentContent = new Set()
    recentContent?.forEach(content => {
      creatorsWithRecentContent.add(content.creator_id)
    })
    
    console.log(`Found ${creatorsWithRecentContent.size} creators with recent content`)
    
    // Step 7: Check content by creator
    const contentByCreator = {}
    allContent?.forEach(content => {
      if (!contentByCreator[content.creator_id]) {
        contentByCreator[content.creator_id] = []
      }
      contentByCreator[content.creator_id].push(content)
    })
    
    // Step 8: Detailed creator analysis
    const creatorAnalysis = []
    for (const creator of trustedCreators || []) {
      const creatorContent = contentByCreator[creator.id] || []
      const recentCreatorContent = creatorContent.filter(content => 
        new Date(content.published_at) > thirtyDaysAgo
      )
      
      creatorAnalysis.push({
        name: creator.name,
        handle: creator.handle,
        trust_score: creator.trust_score,
        total_content: creatorContent.length,
        recent_content: recentCreatorContent.length,
        meets_activity_requirement: recentCreatorContent.length >= 3,
        sample_content: creatorContent.slice(0, 2).map(c => ({
          title: c.title,
          published_at: c.published_at,
          tags: c.tags
        }))
      })
    }
    
    // Step 9: Test the actual curation query
    console.log('Step 9: Testing curation query...')
    const { data: curationTest, error: curationError } = await serviceSupabase
      .from('creators')
      .select(`
        *,
        content (
          id,
          published_at,
          tags,
          ai_summary
        )
      `)
      .eq('platform', 'youtube')
      .gte('trust_score', 6.0)
      .order('trust_score', { ascending: false })
    
    console.log(`Curation query returned ${curationTest?.length || 0} creators`)
    
    if (curationError) {
      console.error('Curation query error:', curationError)
    }
    
    // Filter creators with recent activity
    const activeCreators = curationTest?.filter(creator => {
      const recentContent = creator.content?.filter(content => 
        new Date(content.published_at) > thirtyDaysAgo
      )
      return (recentContent?.length || 0) >= 3
    }) || []
    
    console.log(`Found ${activeCreators.length} active creators after filtering`)
    
    return NextResponse.json({
      success: true,
      debug_results: {
        total_creators: allCreators?.length || 0,
        youtube_creators: youtubeCreators?.length || 0,
        trusted_creators: trustedCreators?.length || 0,
        total_content: allContent?.length || 0,
        recent_content: recentContent?.length || 0,
        creators_with_recent_content: creatorsWithRecentContent.size,
        active_creators_after_filtering: activeCreators.length,
        thirty_days_ago: thirtyDaysAgo.toISOString(),
        creator_analysis: creatorAnalysis,
        curation_query_error: curationError,
        sample_active_creators: activeCreators.slice(0, 3).map(c => ({
          name: c.name,
          handle: c.handle,
          trust_score: c.trust_score,
          content_count: c.content?.length || 0
        }))
      }
    })
    
  } catch (error) {
    console.error('Debug error:', error)
    return NextResponse.json({
      success: false,
      error: 'Debug error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

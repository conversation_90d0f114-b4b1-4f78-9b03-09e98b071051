import { NextRequest, NextResponse } from 'next/server'
import { createServiceRoleClient } from '@/lib/supabase-server'

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Setting up bundle analytics tables...')
    
    const serviceSupabase = createServiceRoleClient()
    
    // Test if the analytics tables exist by trying to query them
    const tables = [
      'bundle_performance_metrics',
      'trending_topics', 
      'bundle_lifecycle_events',
      'bundle_user_interactions'
    ]

    const results = []

    for (const table of tables) {
      try {
        const { error } = await serviceSupabase
          .from(table)
          .select('id')
          .limit(1)

        if (error && error.message.includes('does not exist')) {
          results.push({
            table,
            exists: false,
            error: error.message
          })
        } else {
          results.push({
            table,
            exists: true,
            error: null
          })
        }
      } catch (err) {
        results.push({
          table,
          exists: false,
          error: err instanceof Error ? err.message : 'Unknown error'
        })
      }
    }

    const missingTables = results.filter(r => !r.exists)
    const existingTables = results.filter(r => r.exists)

    if (missingTables.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'Some analytics tables do not exist',
        details: {
          missing_tables: missingTables.map(t => t.table),
          existing_tables: existingTables.map(t => t.table),
          instructions: 'Please run the migration file: supabase/migrations/20240806000001_create_bundle_analytics.sql'
        },
        migration_sql_location: 'supabase/migrations/20240806000001_create_bundle_analytics.sql'
      }, { status: 500 })
    }

    // Test inserting and deleting a record from each table
    for (const table of tables) {
      try {
        let testData: any = {}
        
        switch (table) {
          case 'bundle_performance_metrics':
            // Need a valid bundle_id, so let's skip this test for now
            continue
            
          case 'trending_topics':
            testData = {
              topic_name: 'test_topic',
              trend_score: 5.0,
              source: 'ai_analysis'
            }
            break
            
          case 'bundle_lifecycle_events':
            // Need a valid bundle_id, so let's skip this test for now
            continue
            
          case 'bundle_user_interactions':
            // Need valid bundle_id and user_id, so let's skip this test for now
            continue
        }

        if (Object.keys(testData).length > 0) {
          const { data: testRecord, error: insertError } = await serviceSupabase
            .from(table)
            .insert(testData)
            .select()
            .single()

          if (insertError) {
            throw new Error(`Insert test failed for ${table}: ${insertError.message}`)
          }

          // Clean up test record
          await serviceSupabase
            .from(table)
            .delete()
            .eq('id', testRecord.id)
        }
      } catch (err) {
        console.error(`Test failed for table ${table}:`, err)
      }
    }

    console.log('✅ Bundle analytics tables setup complete!')

    return NextResponse.json({
      success: true,
      message: 'Bundle analytics tables are ready',
      tables_status: results,
      ready_for_analytics: true
    })

  } catch (error) {
    console.error('Error setting up analytics tables:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to setup analytics tables',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

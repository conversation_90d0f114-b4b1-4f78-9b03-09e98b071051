import { createServiceRoleClient } from '@/lib/supabase-server'
import { streamingService } from '@/lib/streaming-availability'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('🎬 Complete Streaming Integration Demo...')
    
    const serviceSupabase = createServiceRoleClient()
    
    // Get AI-enhanced content with referenced titles
    const { data: content, error: contentError } = await serviceSupabase
      .from('content')
      .select(`
        id,
        title,
        description,
        content_type,
        ai_summary,
        referenced_titles,
        tags,
        published_at,
        creators (
          name,
          handle,
          verified
        )
      `)
      .not('ai_summary', 'is', null)
      .not('referenced_titles', 'is', null)
      .limit(3)
    
    if (contentError || !content || content.length === 0) {
      return NextResponse.json({ 
        success: true,
        message: 'No AI-enhanced content found. Run AI enhancement first.',
        step: 'content_fetch'
      })
    }

    console.log(`Found ${content.length} AI-enhanced content items`)
    
    // Mock user preferences
    const userPreferences = {
      streamingServices: ['netflix', 'disney', 'hbo', 'amazon'],
      genres: ['action', 'sci-fi', 'thriller']
    }
    
    const enhancedContent = []
    
    // Process each content item
    for (const item of content) {
      try {
        console.log(`\n🎬 Processing: ${item.title}`)
        console.log(`Referenced titles: ${item.referenced_titles?.join(', ')}`)
        
        // Get streaming availability for all platforms
        const allStreamingData = await streamingService.getStreamingForContent(
          item.referenced_titles || [],
          [] // No filtering for complete data
        )
        
        // Get user-filtered streaming data
        const userStreamingData = await streamingService.getStreamingForContent(
          item.referenced_titles || [],
          userPreferences.streamingServices
        )
        
        // Calculate relevance score (enhanced with streaming availability)
        let relevanceScore = 50 // Base score
        
        // AI content bonuses
        if (item.ai_summary) relevanceScore += 15
        if (item.referenced_titles?.length > 0) relevanceScore += item.referenced_titles.length * 10
        if (item.tags?.length > 0) relevanceScore += item.tags.length * 2
        
        // Streaming availability bonuses
        if (allStreamingData.length > 0) relevanceScore += 20 // Has streaming data
        if (userStreamingData.length > 0) relevanceScore += 30 // Available on user's services
        
        // Subscription service bonus (higher than rental/purchase)
        const hasSubscriptionAvailability = userStreamingData.some(s => 
          s.providers.flatrate && s.providers.flatrate.length > 0
        )
        if (hasSubscriptionAvailability) relevanceScore += 25
        
        enhancedContent.push({
          id: item.id,
          title: item.title,
          description: item.description,
          contentType: item.content_type,
          aiSummary: item.ai_summary,
          referencedTitles: item.referenced_titles,
          tags: item.tags,
          publishedAt: item.published_at,
          creator: {
            name: item.creators?.name,
            handle: item.creators?.handle,
            verified: item.creators?.verified
          },
          streamingAvailability: {
            all: allStreamingData.map(s => ({
              title: s.title,
              type: s.content_type,
              tmdbId: s.tmdb_id,
              posterPath: s.poster_path,
              availableOn: {
                subscription: s.providers.flatrate?.map(p => p.provider_name) || [],
                rental: s.providers.rent?.map(p => p.provider_name) || [],
                purchase: s.providers.buy?.map(p => p.provider_name) || []
              }
            })),
            userFiltered: userStreamingData.map(s => ({
              title: s.title,
              type: s.content_type,
              availableOn: {
                subscription: s.providers.flatrate?.map(p => p.provider_name) || [],
                rental: s.providers.rent?.map(p => p.provider_name) || [],
                purchase: s.providers.buy?.map(p => p.provider_name) || []
              }
            }))
          },
          relevanceScore,
          streamingMetrics: {
            totalTitlesFound: allStreamingData.length,
            userServiceMatches: userStreamingData.length,
            hasSubscriptionAccess: hasSubscriptionAvailability,
            uniqueServices: new Set([
              ...allStreamingData.flatMap(s => s.providers.flatrate?.map(p => p.provider_name) || []),
              ...allStreamingData.flatMap(s => s.providers.rent?.map(p => p.provider_name) || []),
              ...allStreamingData.flatMap(s => s.providers.buy?.map(p => p.provider_name) || [])
            ]).size
          }
        })
        
        console.log(`✅ Enhanced ${item.title}:`)
        console.log(`   - Streaming titles found: ${allStreamingData.length}`)
        console.log(`   - User service matches: ${userStreamingData.length}`)
        console.log(`   - Relevance score: ${relevanceScore}`)
        
      } catch (error) {
        console.error(`Error processing ${item.title}:`, error)
        enhancedContent.push({
          id: item.id,
          title: item.title,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
    
    // Sort by relevance score
    const rankedContent = enhancedContent
      .filter(item => !item.error)
      .sort((a, b) => (b.relevanceScore || 0) - (a.relevanceScore || 0))
    
    // Calculate overall metrics
    const metrics = {
      totalContentProcessed: content.length,
      contentWithStreaming: rankedContent.filter(c => c.streamingAvailability?.all.length > 0).length,
      contentWithUserServiceMatches: rankedContent.filter(c => c.streamingAvailability?.userFiltered.length > 0).length,
      totalStreamingTitlesFound: rankedContent.reduce((sum, c) => sum + (c.streamingMetrics?.totalTitlesFound || 0), 0),
      averageRelevanceScore: Math.round(
        rankedContent.reduce((sum, c) => sum + (c.relevanceScore || 0), 0) / rankedContent.length
      ),
      topStreamingServices: getTopServices(rankedContent),
      userPreferences
    }

    return NextResponse.json({
      success: true,
      message: 'Complete streaming integration demo completed successfully',
      metrics,
      rankedContent,
      summary: {
        description: 'This demo shows the complete SceneSniffer streaming integration pipeline',
        steps: [
          '1. ✅ AI extracts referenced movie/TV titles from content',
          '2. ✅ TMDb API finds streaming availability for each title',
          '3. ✅ User preferences filter results to preferred services',
          '4. ✅ Enhanced relevance scoring includes streaming availability',
          '5. ✅ UI components display "Where to Watch" information'
        ],
        keyFeatures: [
          '🎬 Automatic title extraction from video descriptions',
          '📺 Real-time streaming availability lookup',
          '🎯 User preference filtering',
          '⭐ Enhanced content ranking with streaming data',
          '🎨 Beautiful UI components for streaming display'
        ]
      }
    })

  } catch (error) {
    console.error('Complete streaming demo error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

function getTopServices(content: any[]): string[] {
  const serviceCount: { [key: string]: number } = {}
  
  content.forEach(item => {
    item.streamingAvailability?.all.forEach((streaming: any) => {
      streaming.availableOn.subscription.forEach((service: string) => {
        serviceCount[service] = (serviceCount[service] || 0) + 1
      })
    })
  })
  
  return Object.entries(serviceCount)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5)
    .map(([service]) => service)
}

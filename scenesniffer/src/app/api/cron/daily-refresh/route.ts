import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    console.log('🕒 Daily bundle refresh cron job started at:', new Date().toISOString())
    
    // Optional: Add basic auth for security
    const authHeader = request.headers.get('authorization')
    const cronSecret = process.env.CRON_SECRET
    
    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      console.log('❌ Unauthorized cron request')
      return NextResponse.json({ 
        success: false, 
        error: 'Unauthorized' 
      }, { status: 401 })
    }

    // Import and call the bundle generation function directly
    const { POST } = await import('../../bundles/generate/route')

    const mockRequest = new Request('http://localhost:3000/api/bundles/generate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        forceRegenerate: false,  // Only refresh if needed
        source: 'scheduled_cron'
      })
    })

    const response = await POST(mockRequest)
    const result = await response.json()

    if (!response.ok) {
      throw new Error(`Bundle generation failed: ${result.error || 'Unknown error'}`)
    }

    console.log('✅ Daily bundle refresh completed:', {
      bundlesGenerated: result.stats?.bundles_generated || 0,
      creatorsAdded: result.stats?.total_creators_added || 0,
      timestamp: new Date().toISOString()
    })

    return NextResponse.json({
      success: true,
      message: 'Daily bundle refresh completed successfully',
      timestamp: new Date().toISOString(),
      stats: result.stats,
      nextRun: getNextRunTime()
    })

  } catch (error) {
    console.error('❌ Daily bundle refresh failed:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
      nextRun: getNextRunTime()
    }, { status: 500 })
  }
}

// Calculate next run time (daily at 6 AM UTC)
function getNextRunTime(): string {
  const now = new Date()
  const nextRun = new Date(now)
  nextRun.setUTCHours(6, 0, 0, 0)
  
  // If it's already past 6 AM today, schedule for tomorrow
  if (now.getUTCHours() >= 6) {
    nextRun.setUTCDate(nextRun.getUTCDate() + 1)
  }
  
  return nextRun.toISOString()
}

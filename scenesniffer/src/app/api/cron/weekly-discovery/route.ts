import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    console.log('🕒 Weekly AI discovery cron job started at:', new Date().toISOString())
    
    // Optional: Add basic auth for security
    const authHeader = request.headers.get('authorization')
    const cronSecret = process.env.CRON_SECRET
    
    if (cronSecret && authHeader !== `Bearer ${cronSecret}`) {
      console.log('❌ Unauthorized cron request')
      return NextResponse.json({ 
        success: false, 
        error: 'Unauthorized' 
      }, { status: 401 })
    }

    // Call the existing AI discovery API (if OpenAI key is working)
    try {
      const { POST } = await import('../../bundles/ai-discover/route')

      const mockRequest = new Request('http://localhost:3000/api/bundles/ai-discover', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          mode: 'trending',
          maxBundles: 3,
          platform: 'youtube',
          source: 'scheduled_cron'
        })
      })

      const response = await POST(mockRequest)
      const result = await response.json()

      if (!response.ok) {
        // If AI discovery fails, fall back to regular bundle refresh
        console.log('⚠️ AI discovery failed, falling back to regular refresh')
        return await fallbackToRegularRefresh()
      }

      console.log('✅ Weekly AI discovery completed:', {
        bundlesCreated: result.results?.length || 0,
        timestamp: new Date().toISOString()
      })

      return NextResponse.json({
        success: true,
        message: 'Weekly AI discovery completed successfully',
        timestamp: new Date().toISOString(),
        results: result.results,
        nextRun: getNextWeeklyRunTime()
      })

    } catch (aiError) {
      console.log('⚠️ AI discovery error, falling back to regular refresh:', aiError)
      return await fallbackToRegularRefresh()
    }

  } catch (error) {
    console.error('❌ Weekly discovery failed:', error)
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString(),
      nextRun: getNextWeeklyRunTime()
    }, { status: 500 })
  }
}

// Fallback to regular bundle generation if AI discovery fails
async function fallbackToRegularRefresh() {
  const { POST } = await import('../../bundles/generate/route')

  const mockRequest = new Request('http://localhost:3000/api/bundles/generate', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      forceRegenerate: true,  // Force refresh for weekly run
      source: 'weekly_fallback'
    })
  })

  const response = await POST(mockRequest)
  const result = await response.json()

  return NextResponse.json({
    success: true,
    message: 'Weekly discovery fell back to regular bundle refresh',
    timestamp: new Date().toISOString(),
    stats: result.stats,
    nextRun: getNextWeeklyRunTime()
  })
}

// Calculate next run time (weekly on Sunday at 6 PM UTC)
function getNextWeeklyRunTime(): string {
  const now = new Date()
  const nextRun = new Date(now)
  
  // Set to next Sunday at 6 PM UTC
  const daysUntilSunday = (7 - now.getUTCDay()) % 7
  nextRun.setUTCDate(now.getUTCDate() + (daysUntilSunday || 7))
  nextRun.setUTCHours(18, 0, 0, 0)
  
  return nextRun.toISOString()
}

import { NextRequest, NextResponse } from 'next/server'
import { createServiceRoleClient } from '@/lib/supabase-server'

// Sample creators with content for AI curation testing
const SAMPLE_CREATORS = [
  // Horror creators
  {
    name: "Dead Meat",
    platform: "youtube",
    handle: "DeadMeatJames",
    verified: true,
    trust_score: 9.2,
    follower_count: 6200000,
    genres: ["horror", "reviews"],
    content: [
      {
        title: "The Conjuring Kill Count",
        content_type: "breakdown",
        tags: ["horror", "supernatural", "scary", "ghost"],
        ai_summary: "Detailed breakdown of deaths and scares in The Conjuring horror film, analyzing supernatural elements and jump scares.",
        referenced_titles: ["The Conjuring", "Annabelle"]
      },
      {
        title: "Halloween Kills Review",
        content_type: "review",
        tags: ["horror", "slasher", "michael-myers"],
        ai_summary: "In-depth review of Halloween Kills, discussing the return of Michael <PERSON> and slasher film elements.",
        referenced_titles: ["Halloween Kills", "Halloween"]
      }
    ]
  },
  {
    name: "FoundFlix",
    platform: "youtube", 
    handle: "FoundFlix",
    verified: true,
    trust_score: 8.8,
    follower_count: 2100000,
    genres: ["horror", "explained"],
    content: [
      {
        title: "Hereditary Ending Explained",
        content_type: "theory",
        tags: ["horror", "supernatural", "explained", "a24"],
        ai_summary: "Comprehensive explanation of Hereditary's complex ending, covering cult symbolism and supernatural horror elements.",
        referenced_titles: ["Hereditary", "Midsommar"]
      }
    ]
  },
  // Marvel/DC creators
  {
    name: "New Rockstars",
    platform: "youtube",
    handle: "NewRockstars", 
    verified: true,
    trust_score: 9.0,
    follower_count: 2800000,
    genres: ["marvel", "dc", "superhero"],
    content: [
      {
        title: "Spider-Man No Way Home Easter Eggs",
        content_type: "breakdown",
        tags: ["marvel", "spider-man", "mcu", "easter-eggs"],
        ai_summary: "Detailed breakdown of hidden references and easter eggs in Spider-Man No Way Home, connecting to broader MCU.",
        referenced_titles: ["Spider-Man: No Way Home", "Avengers: Endgame"]
      }
    ]
  },
  {
    name: "Emergency Awesome",
    platform: "youtube",
    handle: "EmergencyAwesome",
    verified: true,
    trust_score: 8.5,
    follower_count: 3500000,
    genres: ["marvel", "dc", "superhero"],
    content: [
      {
        title: "The Batman Trailer Breakdown",
        content_type: "breakdown",
        tags: ["dc", "batman", "trailer", "breakdown"],
        ai_summary: "Frame-by-frame analysis of The Batman trailer, discussing new take on the Dark Knight and Gotham City.",
        referenced_titles: ["The Batman", "Batman Begins"]
      }
    ]
  },
  // Sci-Fi creators
  {
    name: "Alt Shift X",
    platform: "youtube",
    handle: "AltShiftX",
    verified: true,
    trust_score: 9.5,
    follower_count: 1800000,
    genres: ["sci-fi", "fantasy", "explained"],
    content: [
      {
        title: "Dune Explained",
        content_type: "theory",
        tags: ["sci-fi", "dune", "space", "explained"],
        ai_summary: "Deep dive into the complex world of Dune, explaining the politics, technology, and mythology of Frank Herbert's universe.",
        referenced_titles: ["Dune", "Blade Runner 2049"]
      }
    ]
  },
  // Action creators
  {
    name: "Corridor Crew",
    platform: "youtube",
    handle: "CorridorCrew",
    verified: true,
    trust_score: 9.3,
    follower_count: 5600000,
    genres: ["action", "vfx", "stunts"],
    content: [
      {
        title: "John Wick Stunts React",
        content_type: "breakdown",
        tags: ["action", "stunts", "john-wick", "fight"],
        ai_summary: "Professional stunt performers react to and analyze the incredible action sequences in John Wick films.",
        referenced_titles: ["John Wick", "The Matrix"]
      }
    ]
  },
  // Indie creators
  {
    name: "Nerdwriter1",
    platform: "youtube",
    handle: "NerdWriter1",
    verified: true,
    trust_score: 9.1,
    follower_count: 3200000,
    genres: ["indie", "analysis", "art-house"],
    content: [
      {
        title: "The Art of A24 Films",
        content_type: "theory",
        tags: ["indie", "a24", "art-house", "cinematography"],
        ai_summary: "Analysis of A24's distinctive visual style and storytelling approach in independent cinema.",
        referenced_titles: ["Moonlight", "Lady Bird", "Hereditary"]
      }
    ]
  },
  // Streaming creators
  {
    name: "What's on Netflix",
    platform: "youtube",
    handle: "WhatsOnNetflix",
    verified: false,
    trust_score: 7.8,
    follower_count: 890000,
    genres: ["streaming", "netflix", "reviews"],
    content: [
      {
        title: "Best Netflix Movies This Month",
        content_type: "recommendation",
        tags: ["netflix", "streaming", "new-releases", "recommendations"],
        ai_summary: "Monthly roundup of the best new movies and shows coming to Netflix, with recommendations and reviews.",
        referenced_titles: ["Stranger Things", "The Crown"]
      }
    ]
  }
]

export async function POST(request: NextRequest) {
  try {
    console.log('🌱 Seeding sample creators for AI curation...')
    
    const serviceSupabase = createServiceRoleClient()
    
    let creatorsCreated = 0
    let contentCreated = 0
    const results = []

    for (const creatorData of SAMPLE_CREATORS) {
      console.log(`Creating creator: ${creatorData.name}`)
      
      // Check if creator already exists
      const { data: existingCreator } = await serviceSupabase
        .from('creators')
        .select('id')
        .eq('platform', creatorData.platform)
        .eq('handle', creatorData.handle)
        .single()

      let creatorId: string

      if (existingCreator) {
        creatorId = existingCreator.id
        console.log(`Creator ${creatorData.name} already exists`)
      } else {
        // Create new creator
        const { data: newCreator, error: creatorError } = await serviceSupabase
          .from('creators')
          .insert({
            name: creatorData.name,
            platform: creatorData.platform,
            handle: creatorData.handle,
            verified: creatorData.verified,
            trust_score: creatorData.trust_score,
            follower_count: creatorData.follower_count,
            genres: creatorData.genres
          })
          .select('id')
          .single()

        if (creatorError) {
          console.error(`Error creating creator ${creatorData.name}:`, creatorError)
          results.push({
            creator: creatorData.name,
            status: 'failed',
            error: creatorError.message
          })
          continue
        }

        creatorId = newCreator.id
        creatorsCreated++
        console.log(`✅ Created creator: ${creatorData.name}`)
      }

      // Create content for this creator
      for (const contentData of creatorData.content) {
        const { error: contentError } = await serviceSupabase
          .from('content')
          .insert({
            creator_id: creatorId,
            title: contentData.title,
            content_type: contentData.content_type,
            platform_id: Math.random().toString(36).substring(7), // Generate fake platform ID
            platform_url: `https://youtube.com/watch?v=${Math.random().toString(36).substring(7)}`, // Generate fake YouTube URL
            tags: contentData.tags,
            ai_summary: contentData.ai_summary,
            referenced_titles: contentData.referenced_titles,
            published_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString() // Random date within last 30 days
          })

        if (contentError) {
          console.error(`Error creating content for ${creatorData.name}:`, contentError)
        } else {
          contentCreated++
        }
      }

      results.push({
        creator: creatorData.name,
        status: 'success',
        content_pieces: creatorData.content.length
      })
    }

    console.log(`🎉 Sample creator seeding completed!`)
    console.log(`📊 Created ${creatorsCreated} creators with ${contentCreated} content pieces`)

    return NextResponse.json({
      success: true,
      message: 'Sample creators seeded successfully',
      stats: {
        creators_created: creatorsCreated,
        content_created: contentCreated,
        total_processed: SAMPLE_CREATORS.length
      },
      results
    })

  } catch (error) {
    console.error('Error seeding sample creators:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error during creator seeding',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

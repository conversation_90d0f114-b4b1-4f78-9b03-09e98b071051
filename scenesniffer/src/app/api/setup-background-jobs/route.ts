import { NextRequest, NextResponse } from 'next/server'
import { createServiceRoleClient } from '@/lib/supabase-server'

const BACKGROUND_JOBS_SQL = `
-- Background Jobs table for tracking async operations
CREATE TABLE IF NOT EXISTS public.background_jobs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    job_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    total_items INTEGER DEFAULT 0,
    processed_items INTEGER DEFAULT 0,
    result JSONB DEFAULT '{}',
    error_message TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_background_jobs_status ON public.background_jobs(status);
CREATE INDEX IF NOT EXISTS idx_background_jobs_job_type ON public.background_jobs(job_type);
CREATE INDEX IF NOT EXISTS idx_background_jobs_created_at ON public.background_jobs(created_at DESC);

-- RLS Policy (allow all operations for now, can be restricted later)
ALTER TABLE public.background_jobs ENABLE ROW LEVEL SECURITY;

-- Allow all authenticated users to view and manage jobs
DROP POLICY IF EXISTS "Allow authenticated users to manage jobs" ON public.background_jobs;
CREATE POLICY "Allow authenticated users to manage jobs" ON public.background_jobs 
  FOR ALL USING (auth.role() = 'authenticated');
`

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Setting up background jobs table...')

    const serviceSupabase = createServiceRoleClient()

    // Since we can't execute raw SQL, we'll test if the table exists by trying to insert a test record
    // This is a workaround - in production, tables should be created via Supabase dashboard or migrations

    try {
      // Test the table by inserting a test job
      const { data: testJob, error: testError } = await serviceSupabase
        .from('background_jobs')
        .insert({
          job_type: 'test',
          status: 'completed',
          progress: 100,
          metadata: { test: true }
        })
        .select()
        .single()

      if (testError) {
        console.error('Error testing background jobs table:', testError)
        return NextResponse.json({
          success: false,
          error: 'Background jobs table does not exist. Please create it manually using the SQL in the migration file.',
          details: testError.message,
          instructions: 'Run the SQL from supabase/migrations/20240805000001_create_background_jobs.sql in your Supabase dashboard'
        }, { status: 500 })
      }

      // Clean up test job
      await serviceSupabase
        .from('background_jobs')
        .delete()
        .eq('id', testJob.id)

      console.log('✅ Background jobs table setup complete!')

      return NextResponse.json({
        success: true,
        message: 'Background jobs table exists and is working correctly',
        table_exists: true,
        test_passed: true
      })

    } catch (err) {
      return NextResponse.json({
        success: false,
        error: 'Failed to test background jobs table',
        details: err instanceof Error ? err.message : 'Unknown error',
        instructions: 'Please create the background_jobs table manually using Supabase dashboard'
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Error setting up background jobs:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to setup background jobs',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

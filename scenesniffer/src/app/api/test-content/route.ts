import { createServerSupabaseClient } from '@/lib/supabase-server'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const supabase = await createServerSupabaseClient()
    
    // Test content aggregation system
    console.log('🎬 Testing Content Aggregation System...')
    
    // Check if we have any creators in the database
    const { data: creators, error: creatorsError } = await supabase
      .from('creators')
      .select('*')
      .limit(5)
    
    if (creatorsError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch creators',
        details: creatorsError
      }, { status: 500 })
    }
    
    // Check if we have any content in the database
    const { data: content, error: contentError } = await supabase
      .from('content')
      .select(`
        id,
        title,
        content_type,
        published_at,
        creators (name, platform, handle)
      `)
      .limit(5)
      .order('created_at', { ascending: false })
    
    if (contentError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch content',
        details: contentError
      }, { status: 500 })
    }
    
    // Test YouTube API connection
    let youtubeApiTest = null
    try {
      const testUrl = `https://www.googleapis.com/youtube/v3/search?` +
        `part=snippet&` +
        `q=movie review&` +
        `type=video&` +
        `maxResults=1&` +
        `key=${process.env.YOUTUBE_API_KEY}`
      
      const response = await fetch(testUrl)
      youtubeApiTest = {
        status: response.status,
        ok: response.ok,
        hasApiKey: !!process.env.YOUTUBE_API_KEY
      }
      
      if (response.ok) {
        const data = await response.json()
        youtubeApiTest.sampleResult = data.items?.[0]?.snippet?.title || 'No results'
      }
    } catch (error) {
      youtubeApiTest = {
        error: error instanceof Error ? error.message : 'Unknown error',
        hasApiKey: !!process.env.YOUTUBE_API_KEY
      }
    }
    
    return NextResponse.json({
      success: true,
      message: 'Content system test completed',
      data: {
        creators: {
          count: creators?.length || 0,
          sample: creators?.slice(0, 3) || []
        },
        content: {
          count: content?.length || 0,
          sample: content?.slice(0, 3) || []
        },
        youtubeApi: youtubeApiTest,
        environment: {
          hasYouTubeKey: !!process.env.YOUTUBE_API_KEY,
          hasOpenAIKey: !!process.env.OPENAI_API_KEY,
          nodeEnv: process.env.NODE_ENV
        }
      }
    })
    
  } catch (error) {
    console.error('Content test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// POST endpoint to manually trigger content aggregation for testing
export async function POST() {
  try {
    // Trigger content aggregation
    const aggregateUrl = `${process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3001'}/api/content/aggregate`
    
    const response = await fetch(aggregateUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    
    const result = await response.json()
    
    return NextResponse.json({
      success: true,
      message: 'Content aggregation triggered',
      aggregationResult: result
    })
    
  } catch (error) {
    console.error('Content aggregation trigger error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

import { createServerSupabaseClient, createServiceRoleClient } from '@/lib/supabase-server'
import { NextRequest, NextResponse } from 'next/server'

export async function GET() {
  try {
    const supabase = await createServerSupabaseClient()
    const serviceSupabase = createServiceRoleClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Check user's data
    const { data: userProfile } = await supabase
      .from('users')
      .select('*')
      .eq('id', user.id)
      .single()

    const { data: userPrefs } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', user.id)

    const { data: userCreators } = await supabase
      .from('user_creators')
      .select('*')
      .eq('user_id', user.id)

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        email: user.email
      },
      profile: userProfile,
      preferences: userPrefs,
      creators: userCreators
    })

  } catch (error) {
    console.error('Debug user error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// DELETE endpoint to clean up user data for testing
export async function DELETE() {
  try {
    const supabase = await createServerSupabaseClient()
    const serviceSupabase = createServiceRoleClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Delete user preferences (this will help test the fix)
    const { error: prefsError } = await serviceSupabase
      .from('user_preferences')
      .delete()
      .eq('user_id', user.id)

    // Delete user creators
    const { error: creatorsError } = await serviceSupabase
      .from('user_creators')
      .delete()
      .eq('user_id', user.id)

    return NextResponse.json({
      success: true,
      message: 'User data cleaned up',
      errors: {
        preferences: prefsError?.message,
        creators: creatorsError?.message
      }
    })

  } catch (error) {
    console.error('Debug cleanup error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

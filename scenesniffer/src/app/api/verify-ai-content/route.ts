import { createServiceRoleClient } from '@/lib/supabase-server'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const serviceSupabase = createServiceRoleClient()
    
    console.log('🔍 Verifying AI-Enhanced Content...')
    
    // Get content with AI summaries
    const { data: aiContent, error: contentError } = await serviceSupabase
      .from('content')
      .select(`
        id,
        title,
        description,
        content_type,
        ai_summary,
        referenced_titles,
        tags,
        published_at,
        creators (name, handle, platform)
      `)
      .not('ai_summary', 'is', null)
      .order('published_at', { ascending: false })
      .limit(5)
    
    if (contentError) {
      return NextResponse.json({ error: 'Failed to fetch content' }, { status: 500 })
    }

    // Get content without AI summaries for comparison
    const { data: basicContent, error: basicError } = await serviceSupabase
      .from('content')
      .select(`
        id,
        title,
        description,
        content_type,
        ai_summary,
        referenced_titles,
        tags
      `)
      .is('ai_summary', null)
      .limit(3)

    return NextResponse.json({
      success: true,
      message: 'Content verification completed',
      aiEnhancedContent: {
        count: aiContent?.length || 0,
        items: aiContent?.map(item => ({
          title: item.title,
          creator: item.creators?.name,
          contentType: item.content_type,
          aiSummary: item.ai_summary?.substring(0, 100) + '...',
          referencedTitles: item.referenced_titles,
          tags: item.tags,
          originalDescription: item.description?.substring(0, 100) + '...'
        })) || []
      },
      basicContent: {
        count: basicContent?.length || 0,
        items: basicContent?.map(item => ({
          title: item.title,
          contentType: item.content_type,
          hasAiSummary: !!item.ai_summary,
          tags: item.tags
        })) || []
      }
    })

  } catch (error) {
    console.error('Verification error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

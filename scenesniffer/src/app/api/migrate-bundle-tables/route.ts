import { NextRequest, NextResponse } from 'next/server'
import { createServiceRoleClient } from '@/lib/supabase-server'

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Creating sample bundle data in Supabase...')

    const serviceSupabase = createServiceRoleClient()

    // Since we can't create tables via API, let's create sample data
    // First, let's test if the tables exist by trying to insert sample data

    console.log('Testing creator_bundles table...')
    const { data: testBundle, error: testError } = await serviceSupabase
      .from('creator_bundles')
      .insert({
        title: 'Test Bundle',
        description: 'A test bundle to verify table exists',
        tags: ['test'],
        platform: 'youtube',
        creator_count: 0,
        status: 'active'
      })
      .select()
      .single()

    if (testError) {
      return NextResponse.json({
        success: false,
        error: 'creator_bundles table does not exist',
        details: testError,
        solution: 'Please create the bundle tables manually in Supabase dashboard'
      }, { status: 500 })
    }

    console.log('✅ creator_bundles table exists and working')

    // Clean up test data
    await serviceSupabase
      .from('creator_bundles')
      .delete()
      .eq('id', testBundle.id)
    
    // Create bundle_creators table
    const createBundleCreatorsTable = `
      CREATE TABLE IF NOT EXISTS public.bundle_creators (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        bundle_id UUID REFERENCES public.creator_bundles(id) ON DELETE CASCADE NOT NULL,
        creator_id UUID REFERENCES public.creators(id) ON DELETE CASCADE NOT NULL,
        position INTEGER DEFAULT 0,
        score DECIMAL(5,2) DEFAULT 0.0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(bundle_id, creator_id)
      );
    `
    
    const { error: bundleCreatorsError } = await serviceSupabase.rpc('exec_sql', { sql: createBundleCreatorsTable })
    
    // Create user_bundles table
    const createUserBundlesTable = `
      CREATE TABLE IF NOT EXISTS public.user_bundles (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
        bundle_id UUID REFERENCES public.creator_bundles(id) ON DELETE CASCADE NOT NULL,
        added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        included_creator_ids UUID[] DEFAULT '{}',
        UNIQUE(user_id, bundle_id)
      );
    `
    
    const { error: userBundlesError } = await serviceSupabase.rpc('exec_sql', { sql: createUserBundlesTable })
    
    // Create indexes
    const createIndexes = `
      CREATE INDEX IF NOT EXISTS idx_creator_bundles_platform ON public.creator_bundles(platform);
      CREATE INDEX IF NOT EXISTS idx_creator_bundles_status ON public.creator_bundles(status);
      CREATE INDEX IF NOT EXISTS idx_bundle_creators_bundle_id ON public.bundle_creators(bundle_id);
      CREATE INDEX IF NOT EXISTS idx_user_bundles_user_id ON public.user_bundles(user_id);
    `
    
    const { error: indexError } = await serviceSupabase.rpc('exec_sql', { sql: createIndexes })
    
    // Test if tables were created
    const { data: testQuery, error: testError } = await serviceSupabase
      .from('creator_bundles')
      .select('count(*)', { count: 'exact', head: true })
    
    if (testError) {
      return NextResponse.json({
        success: false,
        error: 'Tables creation failed or tables not accessible',
        details: {
          bundles_error: bundlesError,
          bundle_creators_error: bundleCreatorsError,
          user_bundles_error: userBundlesError,
          index_error: indexError,
          test_error: testError
        }
      }, { status: 500 })
    }
    
    console.log('✅ Bundle tables created successfully')
    
    return NextResponse.json({
      success: true,
      message: 'Bundle tables created successfully',
      tables_created: [
        'creator_bundles',
        'bundle_creators', 
        'user_bundles'
      ]
    })
    
  } catch (error) {
    console.error('Error creating bundle tables:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

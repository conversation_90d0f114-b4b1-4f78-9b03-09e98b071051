import { NextRequest, NextResponse } from 'next/server'
import { tmdbAPI } from '@/lib/tmdb'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type') || 'movie' // 'movie' | 'tv'
    const category = searchParams.get('category') || 'trending' // 'trending' | 'popular' | 'top_rated'
    const genre = searchParams.get('genre') // genre ID
    const page = parseInt(searchParams.get('page') || '1')
    const timeWindow = searchParams.get('time_window') as 'day' | 'week' || 'week'

    let results: any[] = []
    let totalPages = 1

    try {
      if (genre) {
        // Discover by genre
        if (type === 'movie') {
          results = await tmdbAPI.discoverMoviesByGenre(parseInt(genre), page)
        } else {
          results = await tmdbAPI.discoverTVShowsByGenre(parseInt(genre), page)
        }
      } else {
        // Discover by category
        switch (category) {
          case 'trending':
            if (type === 'movie') {
              results = await tmdbAPI.getTrendingMovies(timeWindow)
            } else {
              results = await tmdbAPI.getTrendingTVShows(timeWindow)
            }
            break

          case 'popular':
            if (type === 'movie') {
              results = await tmdbAPI.getPopularMovies(page)
            } else {
              results = await tmdbAPI.getPopularTVShows(page)
            }
            break

          case 'top_rated':
            if (type === 'movie') {
              results = await tmdbAPI.getTopRatedMovies(page)
            } else {
              results = await tmdbAPI.getTopRatedTVShows(page)
            }
            break

          default:
            return NextResponse.json({ error: 'Invalid category' }, { status: 400 })
        }
      }

      // Add image URLs and format the data
      const formattedResults = results.map(item => ({
        ...item,
        poster_url: item.poster_path ? tmdbAPI.getImageUrl(item.poster_path, 'w500') : null,
        backdrop_url: item.backdrop_path ? tmdbAPI.getImageUrl(item.backdrop_path, 'w1280') : null,
        content_type: type,
        tmdb_id: item.id
      }))

      return NextResponse.json({
        success: true,
        results: formattedResults,
        pagination: {
          page,
          total_pages: totalPages,
          has_more: page < totalPages
        },
        filters: {
          type,
          category,
          genre,
          time_window: timeWindow
        }
      })

    } catch (tmdbError) {
      console.error('TMDb API error:', tmdbError)
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch content from TMDb',
        details: tmdbError instanceof Error ? tmdbError.message : 'Unknown error'
      }, { status: 500 })
    }

  } catch (error) {
    console.error('Discovery API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

// GET /api/discover/genres - Get available genres
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, type = 'movie' } = body

    if (action === 'genres') {
      let genres: { id: number; name: string }[] = []

      if (type === 'movie') {
        genres = await tmdbAPI.getMovieGenres()
      } else if (type === 'tv') {
        genres = await tmdbAPI.getTVGenres()
      } else {
        // Get both movie and TV genres
        const [movieGenres, tvGenres] = await Promise.all([
          tmdbAPI.getMovieGenres(),
          tmdbAPI.getTVGenres()
        ])
        
        // Merge and deduplicate genres
        const allGenres = [...movieGenres, ...tvGenres]
        const genreMap = new Map()
        allGenres.forEach(genre => {
          if (!genreMap.has(genre.name)) {
            genreMap.set(genre.name, genre)
          }
        })
        genres = Array.from(genreMap.values())
      }

      return NextResponse.json({
        success: true,
        genres,
        type
      })
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 })

  } catch (error) {
    console.error('Genres API error:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch genres'
    }, { status: 500 })
  }
}

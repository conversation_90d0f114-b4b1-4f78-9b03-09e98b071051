import { NextRequest, NextResponse } from 'next/server'
import { createServiceRoleClient } from '@/lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServiceRoleClient()
    
    // Get all creators with their genres
    const { data: creators, error: creatorsError } = await supabase
      .from('creators')
      .select('id, name, genres')
      .not('genres', 'is', null)

    if (creatorsError) {
      throw new Error(`Failed to fetch creators: ${creatorsError.message}`)
    }

    // Aggregate genre usage statistics
    const genreStats: { [key: string]: { count: number; creators: string[] } } = {}
    
    creators?.forEach(creator => {
      if (creator.genres && Array.isArray(creator.genres)) {
        creator.genres.forEach((genre: string) => {
          if (!genreStats[genre]) {
            genreStats[genre] = { count: 0, creators: [] }
          }
          genreStats[genre].count++
          genreStats[genre].creators.push(creator.name)
        })
      }
    })

    // Convert to array and sort by usage
    const genres = Object.entries(genreStats)
      .map(([name, stats]) => ({
        name,
        count: stats.count,
        creators: stats.creators,
        percentage: Math.round((stats.count / (creators?.length || 1)) * 100)
      }))
      .sort((a, b) => b.count - a.count)

    return NextResponse.json({
      success: true,
      genres,
      totalGenres: genres.length,
      totalCreators: creators?.length || 0,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('Genre fetch error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { genreName } = await request.json()
    
    if (!genreName || typeof genreName !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Genre name is required'
      }, { status: 400 })
    }

    const supabase = createServiceRoleClient()
    
    // Check if genre already exists
    const { data: existingCreators } = await supabase
      .from('creators')
      .select('genres')
      .contains('genres', [genreName])
      .limit(1)

    if (existingCreators && existingCreators.length > 0) {
      return NextResponse.json({
        success: false,
        error: 'Genre already exists'
      }, { status: 409 })
    }

    return NextResponse.json({
      success: true,
      message: `Genre "${genreName}" is available to use`,
      genreName
    })

  } catch (error) {
    console.error('Genre creation error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { oldGenre, newGenre } = await request.json()
    
    if (!oldGenre || !newGenre) {
      return NextResponse.json({
        success: false,
        error: 'Both old and new genre names are required'
      }, { status: 400 })
    }

    const supabase = createServiceRoleClient()
    
    // Get all creators with the old genre
    const { data: creatorsWithGenre, error: fetchError } = await supabase
      .from('creators')
      .select('id, name, genres')
      .contains('genres', [oldGenre])

    if (fetchError) {
      throw new Error(`Failed to fetch creators: ${fetchError.message}`)
    }

    if (!creatorsWithGenre || creatorsWithGenre.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No creators found with this genre'
      }, { status: 404 })
    }

    // Update each creator's genres
    let updated = 0
    let errors = 0

    for (const creator of creatorsWithGenre) {
      try {
        const updatedGenres = creator.genres.map((genre: string) => 
          genre === oldGenre ? newGenre : genre
        )

        const { error: updateError } = await supabase
          .from('creators')
          .update({ genres: updatedGenres })
          .eq('id', creator.id)

        if (updateError) {
          console.error(`Failed to update creator ${creator.name}:`, updateError)
          errors++
        } else {
          updated++
        }
      } catch (error) {
        console.error(`Error updating creator ${creator.name}:`, error)
        errors++
      }
    }

    return NextResponse.json({
      success: true,
      message: `Genre renamed from "${oldGenre}" to "${newGenre}"`,
      updated,
      errors,
      affectedCreators: creatorsWithGenre.map(c => c.name)
    })

  } catch (error) {
    console.error('Genre update error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const genreToDelete = searchParams.get('genre')
    const replacementGenre = searchParams.get('replacement')
    
    if (!genreToDelete) {
      return NextResponse.json({
        success: false,
        error: 'Genre name is required'
      }, { status: 400 })
    }

    const supabase = createServiceRoleClient()
    
    // Get all creators with this genre
    const { data: creatorsWithGenre, error: fetchError } = await supabase
      .from('creators')
      .select('id, name, genres')
      .contains('genres', [genreToDelete])

    if (fetchError) {
      throw new Error(`Failed to fetch creators: ${fetchError.message}`)
    }

    if (!creatorsWithGenre || creatorsWithGenre.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'Genre not found or not in use',
        updated: 0
      })
    }

    // Update each creator's genres
    let updated = 0
    let errors = 0

    for (const creator of creatorsWithGenre) {
      try {
        let updatedGenres = creator.genres.filter((genre: string) => genre !== genreToDelete)
        
        // Add replacement genre if specified
        if (replacementGenre && !updatedGenres.includes(replacementGenre)) {
          updatedGenres.push(replacementGenre)
        }

        const { error: updateError } = await supabase
          .from('creators')
          .update({ genres: updatedGenres })
          .eq('id', creator.id)

        if (updateError) {
          console.error(`Failed to update creator ${creator.name}:`, updateError)
          errors++
        } else {
          updated++
        }
      } catch (error) {
        console.error(`Error updating creator ${creator.name}:`, error)
        errors++
      }
    }

    return NextResponse.json({
      success: true,
      message: replacementGenre 
        ? `Genre "${genreToDelete}" deleted and replaced with "${replacementGenre}"`
        : `Genre "${genreToDelete}" deleted`,
      updated,
      errors,
      affectedCreators: creatorsWithGenre.map(c => c.name)
    })

  } catch (error) {
    console.error('Genre deletion error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { createServiceRoleClient } from '@/lib/supabase-server'

export async function POST(request: NextRequest) {
  try {
    const { genresToMerge, targetGenre } = await request.json()
    
    if (!genresToMerge || !Array.isArray(genresToMerge) || genresToMerge.length < 2) {
      return NextResponse.json({
        success: false,
        error: 'At least 2 genres are required for merging'
      }, { status: 400 })
    }

    if (!targetGenre || typeof targetGenre !== 'string') {
      return NextResponse.json({
        success: false,
        error: 'Target genre name is required'
      }, { status: 400 })
    }

    const supabase = createServiceRoleClient()
    
    // Get all creators that have any of the genres to merge
    const { data: creatorsToUpdate, error: fetchError } = await supabase
      .from('creators')
      .select('id, name, genres')
      .or(genresToMerge.map(genre => `genres.cs.{${genre}}`).join(','))

    if (fetchError) {
      throw new Error(`Failed to fetch creators: ${fetchError.message}`)
    }

    if (!creatorsToUpdate || creatorsToUpdate.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No creators found with the specified genres',
        updated: 0
      })
    }

    let updated = 0
    let errors = 0
    const affectedCreators: string[] = []

    for (const creator of creatorsToUpdate) {
      try {
        // Check if creator has any of the genres to merge
        const hasGenresToMerge = creator.genres.some((genre: string) => 
          genresToMerge.includes(genre)
        )

        if (!hasGenresToMerge) continue

        // Remove all genres to merge and add target genre
        let updatedGenres = creator.genres.filter((genre: string) => 
          !genresToMerge.includes(genre)
        )

        // Add target genre if not already present
        if (!updatedGenres.includes(targetGenre)) {
          updatedGenres.push(targetGenre)
        }

        const { error: updateError } = await supabase
          .from('creators')
          .update({ genres: updatedGenres })
          .eq('id', creator.id)

        if (updateError) {
          console.error(`Failed to update creator ${creator.name}:`, updateError)
          errors++
        } else {
          updated++
          affectedCreators.push(creator.name)
        }
      } catch (error) {
        console.error(`Error updating creator ${creator.name}:`, error)
        errors++
      }
    }

    return NextResponse.json({
      success: true,
      message: `Merged genres [${genresToMerge.join(', ')}] into "${targetGenre}"`,
      updated,
      errors,
      affectedCreators,
      mergedGenres: genresToMerge,
      targetGenre
    })

  } catch (error) {
    console.error('Genre merge error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

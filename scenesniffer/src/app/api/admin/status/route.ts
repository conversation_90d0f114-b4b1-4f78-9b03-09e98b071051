import { NextRequest, NextResponse } from 'next/server'
import { createServiceRoleClient } from '@/lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    const supabase = createServiceRoleClient()
    
    // Get content statistics
    const { data: contentStats, error: contentError } = await supabase
      .from('content')
      .select('id, ai_summary, tags, referenced_titles')
      .order('created_at', { ascending: false })
      .limit(100)

    // Get creator statistics
    const { data: creatorStats, error: creatorError } = await supabase
      .from('creators')
      .select('id, genres')

    // Get user statistics
    const { data: userStats, error: userError } = await supabase
      .from('users')
      .select('id')

    // Calculate content AI data completion rates
    const totalContent = contentStats?.length || 0
    const contentWithAISummary = contentStats?.filter(c => c.ai_summary && c.ai_summary.trim() !== '').length || 0
    const contentWithTags = contentStats?.filter(c => c.tags && c.tags.length > 0).length || 0
    const contentWithReferencedTitles = contentStats?.filter(c => c.referenced_titles && c.referenced_titles.length > 0).length || 0

    // Calculate creator genre completion rate
    const totalCreators = creatorStats?.length || 0
    const creatorsWithGenres = creatorStats?.filter(c => c.genres && c.genres.length > 0).length || 0

    const totalUsers = userStats?.length || 0

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      stats: {
        content: {
          total: totalContent,
          aiSummaryCompletion: totalContent > 0 ? Math.round((contentWithAISummary / totalContent) * 100) : 0,
          tagsCompletion: totalContent > 0 ? Math.round((contentWithTags / totalContent) * 100) : 0,
          referencedTitlesCompletion: totalContent > 0 ? Math.round((contentWithReferencedTitles / totalContent) * 100) : 0,
          breakdown: {
            withAISummary: contentWithAISummary,
            withTags: contentWithTags,
            withReferencedTitles: contentWithReferencedTitles
          }
        },
        creators: {
          total: totalCreators,
          genreCompletion: totalCreators > 0 ? Math.round((creatorsWithGenres / totalCreators) * 100) : 0,
          withGenres: creatorsWithGenres
        },
        users: {
          total: totalUsers
        }
      },
      health: {
        contentProcessing: contentWithAISummary > 0 ? 'healthy' : 'needs_attention',
        creatorGenres: creatorsWithGenres > 0 ? 'healthy' : 'needs_attention',
        overallStatus: (contentWithAISummary > 0 && creatorsWithGenres > 0) ? 'healthy' : 'needs_attention'
      }
    })

  } catch (error) {
    console.error('Admin status error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

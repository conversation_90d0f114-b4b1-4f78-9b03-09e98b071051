import { NextRequest, NextResponse } from 'next/server'
import { createServiceRoleClient } from '@/lib/supabase-server'

export async function POST(request: NextRequest) {
  try {
    const supabase = createServiceRoleClient()
    
    console.log('🚀 Starting content engagement migration...')

    // Step 1: Add engagement columns to content table
    console.log('📝 Adding engagement columns to content table...')
    
    try {
      // Check if columns already exist by trying to select them
      const { data: testData, error: testError } = await supabase
        .from('content')
        .select('view_count, engagement_score, last_viewed_at')
        .limit(1)

      if (testError && testError.code === '42703') {
        // Columns don't exist, we need to add them
        console.log('⚠️ Engagement columns do not exist. They need to be added manually via Supabase dashboard.')
        
        return NextResponse.json({
          success: false,
          message: 'Engagement columns need to be added manually',
          instructions: [
            '1. Go to your Supabase dashboard',
            '2. Navigate to Table Editor > content table',
            '3. Add these columns:',
            '   - view_count (int4, default: 0)',
            '   - engagement_score (numeric, default: 0.0)',
            '   - last_viewed_at (timestamptz, nullable)',
            '4. Add behavior_profile column to user_preferences table:',
            '   - behavior_profile (jsonb, nullable)',
            '5. Then restart your application'
          ]
        })
      } else {
        console.log('✅ Engagement columns already exist!')
      }
    } catch (error) {
      console.error('Error checking columns:', error)
    }

    // Step 2: Check user_preferences table
    console.log('📝 Checking user_preferences table...')
    
    try {
      const { data: prefsTest, error: prefsError } = await supabase
        .from('user_preferences')
        .select('behavior_profile')
        .limit(1)

      if (prefsError && prefsError.code === '42703') {
        console.log('⚠️ behavior_profile column does not exist in user_preferences.')
      } else {
        console.log('✅ behavior_profile column exists!')
      }
    } catch (error) {
      console.error('Error checking user_preferences:', error)
    }

    // Step 3: Create content_interactions table if it doesn't exist
    console.log('📝 Checking content_interactions table...')
    
    try {
      const { data: interactionsTest, error: interactionsError } = await supabase
        .from('content_interactions')
        .select('id')
        .limit(1)

      if (interactionsError && interactionsError.code === '42P01') {
        console.log('⚠️ content_interactions table does not exist.')
        
        return NextResponse.json({
          success: false,
          message: 'Database schema needs manual setup',
          instructions: [
            '1. Go to your Supabase dashboard',
            '2. Navigate to SQL Editor',
            '3. Run the migration SQL from: supabase/migrations/20250110000001_add_content_engagement.sql',
            '4. Or add the columns manually as described above',
            '5. Then restart your application'
          ],
          sqlFile: 'supabase/migrations/20250110000001_add_content_engagement.sql'
        })
      } else {
        console.log('✅ content_interactions table exists!')
      }
    } catch (error) {
      console.error('Error checking content_interactions:', error)
    }

    return NextResponse.json({
      success: true,
      message: 'Migration check completed',
      status: 'All required tables and columns exist'
    })

  } catch (error) {
    console.error('Migration error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET endpoint to check migration status
export async function GET() {
  try {
    const supabase = createServiceRoleClient()
    
    const status = {
      content_engagement_columns: false,
      behavior_profile_column: false,
      content_interactions_table: false
    }

    // Check content table columns
    try {
      await supabase.from('content').select('view_count, engagement_score, last_viewed_at').limit(1)
      status.content_engagement_columns = true
    } catch (error) {
      // Columns don't exist
    }

    // Check user_preferences behavior_profile column
    try {
      await supabase.from('user_preferences').select('behavior_profile').limit(1)
      status.behavior_profile_column = true
    } catch (error) {
      // Column doesn't exist
    }

    // Check content_interactions table
    try {
      await supabase.from('content_interactions').select('id').limit(1)
      status.content_interactions_table = true
    } catch (error) {
      // Table doesn't exist
    }

    return NextResponse.json({
      success: true,
      migration_status: status,
      all_ready: Object.values(status).every(Boolean)
    })

  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

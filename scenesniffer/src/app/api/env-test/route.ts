import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  return NextResponse.json({
    openai_key_exists: !!process.env.OPENAI_API_KEY,
    openai_key_length: process.env.OPENAI_API_KEY?.length || 0,
    openai_key_prefix: process.env.OPENAI_API_KEY?.substring(0, 10) || 'none',
    youtube_key_exists: !!process.env.YOUTUBE_API_KEY,
    tmdb_key_exists: !!process.env.TMDB_API_KEY,
    supabase_url_exists: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
    all_env_vars: Object.keys(process.env).filter(key => 
      key.includes('OPENAI') || 
      key.includes('YOUTUBE') || 
      key.includes('TMDB') || 
      key.includes('SUPABASE')
    )
  })
}

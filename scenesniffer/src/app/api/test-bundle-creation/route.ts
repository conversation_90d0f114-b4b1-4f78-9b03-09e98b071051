import { NextRequest, NextResponse } from 'next/server'
import { createServiceRoleClient } from '@/lib/supabase-server'

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing bundle creation...')

    const serviceSupabase = createServiceRoleClient()

    // Test 0: Check what tables exist
    console.log('Testing what tables exist...')

    // Try to query existing tables
    const tables = ['users', 'creators', 'content', 'creator_bundles']
    const tableStatus = {}

    for (const table of tables) {
      try {
        const { data, error } = await serviceSupabase
          .from(table)
          .select('*', { count: 'exact', head: true })

        tableStatus[table] = error ? `Error: ${error.message}` : `Exists`
      } catch (err) {
        tableStatus[table] = `Exception: ${err instanceof Error ? err.message : 'Unknown'}`
      }
    }

    console.log('Table status:', tableStatus)

    // If creator_bundles doesn't exist, return the status
    if (tableStatus.creator_bundles.includes('Error') || tableStatus.creator_bundles.includes('Exception')) {
      return NextResponse.json({
        success: false,
        error: 'Bundle tables do not exist',
        table_status: tableStatus,
        step: 'table_check'
      }, { status: 500 })
    }
    
    // Test 1: Simple bundle creation
    console.log('Creating test bundle...')
    console.log('Service role client URL:', serviceSupabase.supabaseUrl)

    const insertData = {
      title: 'Test Bundle API',
      description: 'Testing bundle creation via API',
      tags: ['test', 'api'],
      platform: 'youtube',
      creator_count: 1,
      status: 'active'
    }
    console.log('Insert data:', insertData)

    const { data: bundle, error: bundleError } = await serviceSupabase
      .from('creator_bundles')
      .insert(insertData)
      .select()
      .single()

    console.log('Bundle creation result:', { bundle, bundleError })
    console.log('Error type:', typeof bundleError)
    console.log('Error keys:', bundleError ? Object.keys(bundleError) : 'no error')
    console.log('Error string:', bundleError ? JSON.stringify(bundleError) : 'no error')

    if (bundleError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to create bundle',
        details: bundleError,
        step: 'bundle_creation'
      }, { status: 500 })
    }

    // Test 2: Create a test creator
    console.log('Creating test creator...')
    const { data: creator, error: creatorError } = await serviceSupabase
      .from('creators')
      .insert({
        name: 'Test Creator API',
        platform: 'youtube',
        handle: 'testcreator' + Date.now(),
        verified: false,
        trust_score: 8.5
      })
      .select()
      .single()

    console.log('Creator creation result:', { creator, creatorError })

    if (creatorError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to create creator',
        details: creatorError,
        step: 'creator_creation',
        bundle_created: bundle
      }, { status: 500 })
    }

    // Test 3: Link creator to bundle
    console.log('Linking creator to bundle...')
    const { data: link, error: linkError } = await serviceSupabase
      .from('bundle_creators')
      .insert({
        bundle_id: bundle.id,
        creator_id: creator.id,
        position: 0,
        score: 10.0
      })
      .select()
      .single()

    console.log('Link creation result:', { link, linkError })

    if (linkError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to link creator to bundle',
        details: linkError,
        step: 'creator_linking',
        bundle_created: bundle,
        creator_created: creator
      }, { status: 500 })
    }

    // Test 4: Fetch the complete bundle
    console.log('Fetching complete bundle...')
    const { data: completeBundle, error: fetchError } = await serviceSupabase
      .from('creator_bundles')
      .select(`
        *,
        bundle_creators (
          *,
          creators (*)
        )
      `)
      .eq('id', bundle.id)
      .single()

    console.log('Complete bundle fetch result:', { completeBundle, fetchError })

    return NextResponse.json({
      success: true,
      message: 'Bundle creation test completed successfully',
      results: {
        bundle_created: bundle,
        creator_created: creator,
        link_created: link,
        complete_bundle: completeBundle
      }
    })

  } catch (error) {
    console.error('Bundle creation test error:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error during test',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

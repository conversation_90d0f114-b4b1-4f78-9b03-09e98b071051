import { NextRequest, NextResponse } from 'next/server'
import { youtubeAPI } from '@/lib/youtube'
import { tmdbAPI } from '@/lib/tmdb'
import { createOpenAIService } from '@/lib/openai'

export async function GET(request: NextRequest) {
  const tests = {
    youtube: { status: 'pending', data: null, error: null },
    tmdb: { status: 'pending', data: null, error: null },
    openai: { status: 'pending', data: null, error: null }
  }

  // Test YouTube API
  try {
    console.log('Testing YouTube API...')
    const channelInfo = await youtubeAPI.getChannelInfo('@mkbhd')
    tests.youtube = {
      status: 'success',
      data: {
        channelName: channelInfo.snippet.title,
        subscriberCount: channelInfo.statistics.subscriberCount,
        videoCount: channelInfo.statistics.videoCount
      },
      error: null
    }
    console.log('YouTube API test passed')
  } catch (error) {
    console.error('YouTube API test failed:', error)
    tests.youtube = {
      status: 'error',
      data: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }

  // Test TMDb API
  try {
    console.log('Testing TMDb API...')
    const movies = await tmdbAPI.searchMovies('The Matrix')
    tests.tmdb = {
      status: 'success',
      data: {
        movieCount: movies.length,
        firstMovie: movies[0] ? {
          title: movies[0].title,
          releaseDate: movies[0].release_date,
          id: movies[0].id
        } : null
      },
      error: null
    }
    console.log('TMDb API test passed')
  } catch (error) {
    console.error('TMDb API test failed:', error)
    tests.tmdb = {
      status: 'error',
      data: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }

  // Test OpenAI API
  try {
    console.log('Testing OpenAI API...')
    console.log('OpenAI API Key available:', !!process.env.OPENAI_API_KEY)
    console.log('OpenAI API Key length:', process.env.OPENAI_API_KEY?.length || 0)
    const openaiService = createOpenAIService()
    const summary = await openaiService.summarizeContent(
      'The Matrix Review - Mind-Blowing Sci-Fi Classic',
      'A detailed review of The Matrix, exploring its groundbreaking visual effects, philosophical themes, and impact on cinema.',
      'review'
    )
    tests.openai = {
      status: 'success',
      data: {
        summaryLength: summary.length,
        summary: summary.substring(0, 100) + '...'
      },
      error: null
    }
    console.log('OpenAI API test passed')
  } catch (error) {
    console.error('OpenAI API test failed:', error)
    tests.openai = {
      status: 'error',
      data: null,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }

  const allPassed = Object.values(tests).every(test => test.status === 'success')

  return NextResponse.json({
    success: allPassed,
    message: allPassed ? 'All API tests passed!' : 'Some API tests failed',
    tests,
    timestamp: new Date().toISOString()
  })
}

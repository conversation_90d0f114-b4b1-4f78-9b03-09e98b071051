import { NextRequest, NextResponse } from 'next/server'
import { createServiceRoleClient } from '@/lib/supabase-server'

// GET /api/trends - Get trending topics
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '20')
    const source = searchParams.get('source') // Filter by source
    const status = searchParams.get('status') || 'active'

    const supabase = createServiceRoleClient()

    let query = supabase
      .from('trending_topics')
      .select('*')
      .eq('status', status)
      .order('trend_score', { ascending: false })
      .limit(limit)

    if (source) {
      query = query.eq('source', source)
    }

    const { data: trends, error } = await query

    if (error) {
      throw new Error(`Failed to fetch trends: ${error.message}`)
    }

    return NextResponse.json({
      success: true,
      data: trends || [],
      meta: {
        count: trends?.length || 0,
        filters: { source, status, limit }
      }
    })

  } catch (error) {
    console.error('Error fetching trends:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch trends',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// POST /api/trends - Add a new trending topic
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const {
      topic_name,
      topic_keywords = [],
      trend_score,
      source,
      source_data = {},
      expires_at
    } = body

    if (!topic_name || !trend_score || !source) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: topic_name, trend_score, and source'
      }, { status: 400 })
    }

    if (trend_score < 0 || trend_score > 10) {
      return NextResponse.json({
        success: false,
        error: 'trend_score must be between 0 and 10'
      }, { status: 400 })
    }

    const validSources = ['social', 'news', 'platform', 'ai_analysis', 'user_behavior']
    if (!validSources.includes(source)) {
      return NextResponse.json({
        success: false,
        error: `Invalid source. Must be one of: ${validSources.join(', ')}`
      }, { status: 400 })
    }

    const supabase = createServiceRoleClient()

    const { data: trend, error } = await supabase
      .from('trending_topics')
      .insert({
        topic_name,
        topic_keywords,
        trend_score,
        source,
        source_data,
        expires_at: expires_at ? new Date(expires_at).toISOString() : null
      })
      .select()
      .single()

    if (error) {
      throw new Error(`Failed to create trend: ${error.message}`)
    }

    return NextResponse.json({
      success: true,
      data: trend,
      message: 'Trending topic created successfully'
    })

  } catch (error) {
    console.error('Error creating trend:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to create trend',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// PATCH /api/trends - Update trending topics (bulk operations)
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json()
    const { action, topic_ids = [], updates = {} } = body

    if (!action || !topic_ids.length) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: action and topic_ids'
      }, { status: 400 })
    }

    const supabase = createServiceRoleClient()

    let result
    switch (action) {
      case 'expire':
        result = await supabase
          .from('trending_topics')
          .update({ 
            status: 'expired',
            expires_at: new Date().toISOString()
          })
          .in('id', topic_ids)
        break

      case 'activate':
        result = await supabase
          .from('trending_topics')
          .update({ status: 'active' })
          .in('id', topic_ids)
        break

      case 'update':
        result = await supabase
          .from('trending_topics')
          .update(updates)
          .in('id', topic_ids)
        break

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Must be one of: expire, activate, update'
        }, { status: 400 })
    }

    if (result.error) {
      throw new Error(`Failed to ${action} trends: ${result.error.message}`)
    }

    return NextResponse.json({
      success: true,
      message: `Successfully ${action}d ${topic_ids.length} trending topics`,
      affected_count: topic_ids.length
    })

  } catch (error) {
    console.error('Error updating trends:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to update trends',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

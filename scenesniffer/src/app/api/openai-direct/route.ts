import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'

export async function GET(request: NextRequest) {
  try {
    // Use the exact API key you tested
    const apiKey = '********************************************************************************************************************************************************************'
    
    const client = new OpenAI({
      apiKey: apiKey
    })

    const response = await client.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: 'You are a helpful assistant.'
        },
        {
          role: 'user',
          content: 'Say "Hello from SceneSniffer!" in exactly 5 words.'
        }
      ],
      max_tokens: 20,
      temperature: 0.1
    })

    const message = response.choices[0]?.message?.content?.trim() || 'No response'

    return NextResponse.json({
      success: true,
      message: message,
      apiKeyPrefix: apiKey.substring(0, 10),
      envKeyPrefix: process.env.OPENAI_API_KEY?.substring(0, 10) || 'none'
    })

  } catch (error: any) {
    console.error('Direct OpenAI test error:', error)
    return NextResponse.json({
      success: false,
      error: error.message || 'Unknown error',
      code: error.code || 'unknown',
      apiKeyPrefix: 'sk-proj-qH',
      envKeyPrefix: process.env.OPENAI_API_KEY?.substring(0, 10) || 'none'
    })
  }
}

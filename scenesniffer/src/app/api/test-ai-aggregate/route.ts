import { createServerSupabaseClient, createServiceRoleClient } from '@/lib/supabase-server'
import { createOpenAIService } from '@/lib/openai'
import { NextRequest, NextResponse } from 'next/server'

interface YouTubeVideo {
  id: string
  title: string
  description: string
  publishedAt: string
  thumbnails: {
    default: { url: string }
    medium: { url: string }
    high: { url: string }
  }
  channelId: string
  channelTitle: string
}

interface YouTubeSearchResponse {
  items: Array<{
    id: { videoId: string }
    snippet: YouTubeVideo
  }>
}

export async function POST(request: NextRequest) {
  try {
    const serviceSupabase = createServiceRoleClient()
    
    console.log('🤖 Testing AI-Enhanced Content Aggregation...')
    
    // Get one YouTube creator for testing
    const { data: creators, error: creatorsError } = await serviceSupabase
      .from('creators')
      .select('*')
      .eq('platform', 'youtube')
      .limit(1) // Test with just 1 creator
    
    if (creatorsError || !creators || creators.length === 0) {
      return NextResponse.json({ error: 'No YouTube creators found' }, { status: 500 })
    }

    const creator = creators[0]
    console.log(`Testing AI processing with creator: ${creator.name} (${creator.handle})`)
    
    // Get channel ID
    let actualChannelId: string | null = null
    
    if (creator.handle.startsWith('@')) {
      const handle = creator.handle.substring(1)
      const channelInfoUrl = `https://www.googleapis.com/youtube/v3/channels?` +
        `part=snippet&` +
        `forHandle=${handle}&` +
        `key=${process.env.YOUTUBE_API_KEY}`
      
      const channelInfoResponse = await fetch(channelInfoUrl)
      if (channelInfoResponse.ok) {
        const channelInfoData = await channelInfoResponse.json()
        if (channelInfoData.items && channelInfoData.items.length > 0) {
          actualChannelId = channelInfoData.items[0].id
        }
      }
    }
    
    if (!actualChannelId) {
      return NextResponse.json({ error: 'Could not resolve channel ID' }, { status: 500 })
    }
    
    // Get recent videos (expand time range to get more content for testing)
    const videoSearchUrl = `https://www.googleapis.com/youtube/v3/search?` +
      `part=snippet&` +
      `channelId=${actualChannelId}&` +
      `order=date&` +
      `type=video&` +
      `maxResults=3&` +
      `publishedAfter=${new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()}&` + // 30 days instead of 7
      `key=${process.env.YOUTUBE_API_KEY}`
    
    console.log(`Fetching videos for AI processing test...`)
    const videoResponse = await fetch(videoSearchUrl)
    
    if (!videoResponse.ok) {
      return NextResponse.json({ error: 'Failed to fetch videos' }, { status: 500 })
    }
    
    const videoData: YouTubeSearchResponse = await videoResponse.json()
    console.log(`Found ${videoData.items?.length || 0} videos for AI processing`)
    
    // Process videos with AI (but don't save to avoid duplicates)
    const results = await testAIProcessing(videoData, creator)
    
    return NextResponse.json({
      success: true,
      message: 'AI processing test completed',
      creator: creator.name,
      videosProcessed: videoData.items?.length || 0,
      results
    })

  } catch (error) {
    console.error('AI test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

async function testAIProcessing(data: YouTubeSearchResponse, creator: any): Promise<any[]> {
  if (!data.items || data.items.length === 0) {
    return []
  }

  const openaiService = createOpenAIService()
  const results: any[] = []

  for (const item of data.items) {
    const video = item.snippet
    const videoId = item.id.videoId

    try {
      console.log(`🤖 AI Processing: ${video.title}`)
      
      // AI Processing - test each function individually for better error handling
      const startTime = Date.now()

      let aiContentType = 'review'
      let aiSummary = 'Summary not available'
      let referencedTitles: string[] = []
      let aiTags: string[] = []

      try {
        aiContentType = await openaiService.categorizeContent(video.title, video.description)
        console.log(`✅ Content type: ${aiContentType}`)
      } catch (error) {
        console.error('❌ Content categorization failed:', error)
      }

      try {
        aiSummary = await openaiService.summarizeContent(video.title, video.description, aiContentType)
        console.log(`✅ Summary generated: ${aiSummary.substring(0, 50)}...`)
      } catch (error) {
        console.error('❌ Summary generation failed:', error)
      }

      try {
        referencedTitles = await openaiService.extractTitles(video.title, video.description)
        console.log(`✅ Titles extracted: ${referencedTitles.join(', ')}`)
      } catch (error) {
        console.error('❌ Title extraction failed:', error)
      }

      try {
        aiTags = await openaiService.generateTags(video.title, video.description, referencedTitles)
        console.log(`✅ Tags generated: ${aiTags.join(', ')}`)
      } catch (error) {
        console.error('❌ Tag generation failed:', error)
      }

      const processingTime = Date.now() - startTime
      
      results.push({
        title: video.title,
        originalDescription: video.description?.substring(0, 100) + '...',
        aiProcessing: {
          contentType: aiContentType,
          summary: aiSummary,
          referencedTitles,
          tags: aiTags,
          processingTimeMs: processingTime
        }
      })
      
      console.log(`✅ AI processed "${video.title}" in ${processingTime}ms`)
      console.log(`   Type: ${aiContentType}`)
      console.log(`   Summary: ${aiSummary.substring(0, 50)}...`)
      console.log(`   Referenced: ${referencedTitles.join(', ')}`)
      console.log(`   Tags: ${aiTags.join(', ')}`)
      
    } catch (error) {
      console.error(`❌ AI processing failed for ${video.title}:`, error)
      results.push({
        title: video.title,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    }
  }

  return results
}

import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient, createServiceRoleClient } from '@/lib/supabase-server'
import { OnboardingData } from '@/types'

export async function POST(request: NextRequest) {
  console.log('🔧 Preferences API - POST request received')
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

  // Ensure user profile exists
  const { data: userProfile, error: userError } = await supabase
    .from('users')
    .select('id')
    .eq('id', user.id)
    .single()

  if (userError && userError.code === 'PGRST116') {
    // User doesn't exist, create profile using service role
    const serviceSupabase = createServiceRoleClient()
    const { error: createError } = await serviceSupabase
      .from('users')
      .insert({
        id: user.id,
        username: user.email?.split('@')[0] || 'user',
        avatar_url: user.user_metadata?.avatar_url
      })

    if (createError) {
      console.error('Error creating user profile:', createError)
      return NextResponse.json({ error: 'Failed to create user profile' }, { status: 500 })
    }
  }

    const body: OnboardingData = await request.json()
    const { genres, creators, streaming_services } = body

    // Validate input
    if (!Array.isArray(genres) || !Array.isArray(creators) || !Array.isArray(streaming_services)) {
      return NextResponse.json({ error: 'Invalid input format' }, { status: 400 })
    }

    // Use service role client for preferences to bypass any RLS issues
    const serviceSupabase = createServiceRoleClient()

    // Delete existing preferences first, then insert new ones
    await serviceSupabase
      .from('user_preferences')
      .delete()
      .eq('user_id', user.id)

    // Insert new preferences
    const { error: preferencesError } = await serviceSupabase
      .from('user_preferences')
      .insert({
        user_id: user.id,
        genres,
        streaming_services
      })

    if (preferencesError) {
      console.error('Error saving preferences:', preferencesError)
      return NextResponse.json({ error: 'Failed to save preferences' }, { status: 500 })
    }

    console.log('Preferences saved successfully for user:', user.id)

    // Process creators - add them to the creators table and link to user
    for (const creator of creators) {
      try {
        // First, try to create or get the creator
        const { data: existingCreator, error: fetchError } = await supabase
          .from('creators')
          .select('id')
          .eq('platform', creator.platform)
          .eq('handle', creator.handle)
          .single()

        let creatorId: string

        if (existingCreator) {
          creatorId = existingCreator.id
        } else {
          // Create new creator
          const { data: newCreator, error: createError } = await supabase
            .from('creators')
            .insert({
              name: creator.handle,
              platform: creator.platform,
              handle: creator.handle,
              verified: false,
              trust_score: 5.0
            })
            .select('id')
            .single()

          if (createError) {
            console.error('Error creating creator:', createError)
            continue // Skip this creator and continue with others
          }

          creatorId = newCreator.id
        }

        // Link creator to user
        const { error: linkError } = await supabase
          .from('user_creators')
          .upsert({
            user_id: user.id,
            creator_id: creatorId
          })

        if (linkError && linkError.code !== '23505') { // Ignore duplicate key error
          console.error('Error linking creator to user:', linkError)
        }
      } catch (error) {
        console.error('Error processing creator:', creator, error)
        // Continue with other creators
      }
    }

    return NextResponse.json({ 
      success: true,
      message: 'Preferences saved successfully'
    })

  } catch (error) {
    console.error('Error in preferences API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user preferences
    const { data: preferences, error: preferencesError } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single()

    if (preferencesError && preferencesError.code !== 'PGRST116') {
      console.error('Error fetching preferences:', preferencesError)
      return NextResponse.json({ error: 'Failed to fetch preferences' }, { status: 500 })
    }

    // Get user's followed creators
    const { data: userCreators, error: creatorsError } = await supabase
      .from('user_creators')
      .select(`
        creators (
          platform,
          handle,
          name,
          verified,
          trust_score
        )
      `)
      .eq('user_id', user.id)

    if (creatorsError) {
      console.error('Error fetching creators:', creatorsError)
      return NextResponse.json({ error: 'Failed to fetch creators' }, { status: 500 })
    }

    const creators = userCreators?.map(uc => ({
      platform: uc.creators.platform,
      handle: uc.creators.handle,
      name: uc.creators.name,
      verified: uc.creators.verified,
      trust_score: uc.creators.trust_score
    })) || []

    return NextResponse.json({
      preferences: preferences || {
        genres: [],
        streaming_services: []
      },
      creators
    })

  } catch (error) {
    console.error('Error in preferences GET API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

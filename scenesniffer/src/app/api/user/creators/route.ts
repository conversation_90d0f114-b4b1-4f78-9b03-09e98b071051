import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient, createServiceRoleClient } from '@/lib/supabase-server'

interface CreatorData {
  platform: string
  handle: string
  name?: string
}

// GET - Fetch user's followed creators
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's followed creators with detailed info
    const { data: userCreators, error: creatorsError } = await supabase
      .from('user_creators')
      .select(`
        creator_id,
        created_at,
        creators (
          id,
          platform,
          handle,
          name,
          verified,
          trust_score,
          avatar_url,
          follower_count,
          created_at
        )
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })

    if (creatorsError) {
      console.error('Error fetching creators:', creatorsError)
      return NextResponse.json({ error: 'Failed to fetch creators' }, { status: 500 })
    }

    const creators = userCreators?.map(uc => ({
      id: uc.creators.id,
      platform: uc.creators.platform,
      handle: uc.creators.handle,
      name: uc.creators.name,
      verified: uc.creators.verified,
      trust_score: uc.creators.trust_score,
      avatar_url: uc.creators.avatar_url,
      follower_count: uc.creators.follower_count,
      followed_at: uc.created_at, // Use created_at as followed_at
      created_at: uc.creators.created_at
    })) || []

    return NextResponse.json({
      success: true,
      creators,
      count: creators.length
    })

  } catch (error) {
    console.error('Error in creators GET API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST - Add/Follow a creator
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const serviceSupabase = createServiceRoleClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body: CreatorData = await request.json()
    const { platform, handle, name } = body

    // Validate input
    if (!platform || !handle) {
      return NextResponse.json({ 
        error: 'Platform and handle are required' 
      }, { status: 400 })
    }

    // Check if creator already exists
    const { data: existingCreator, error: fetchError } = await supabase
      .from('creators')
      .select('id, name, verified, trust_score')
      .eq('platform', platform)
      .eq('handle', handle)
      .single()

    let creatorId: string
    let creatorInfo: any

    if (existingCreator) {
      creatorId = existingCreator.id
      creatorInfo = existingCreator
      console.log('Found existing creator:', handle)
    } else {
      // Create new creator
      const { data: newCreator, error: createError } = await serviceSupabase
        .from('creators')
        .insert({
          name: name || handle,
          platform: platform,
          handle: handle,
          verified: false,
          trust_score: 5.0
        })
        .select('id, name, verified, trust_score')
        .single()

      if (createError) {
        console.error('Error creating creator:', createError)
        return NextResponse.json({ 
          error: 'Failed to create creator' 
        }, { status: 500 })
      }

      creatorId = newCreator.id
      creatorInfo = newCreator
      console.log('Created new creator:', handle)
    }

    // Check if user is already following this creator
    const { data: existingFollow } = await supabase
      .from('user_creators')
      .select('creator_id')
      .eq('user_id', user.id)
      .eq('creator_id', creatorId)
      .single()

    if (existingFollow) {
      return NextResponse.json({
        success: false,
        error: 'Already following this creator',
        creator: {
          id: creatorId,
          platform,
          handle,
          name: creatorInfo.name
        }
      }, { status: 409 })
    }

    // Link creator to user
    const { error: linkError } = await serviceSupabase
      .from('user_creators')
      .insert({
        user_id: user.id,
        creator_id: creatorId
      })

    if (linkError) {
      console.error('Error linking creator to user:', linkError)
      return NextResponse.json({ 
        error: 'Failed to follow creator' 
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: `Successfully followed ${handle}`,
      creator: {
        id: creatorId,
        platform,
        handle,
        name: creatorInfo.name,
        verified: creatorInfo.verified,
        trust_score: creatorInfo.trust_score
      }
    })

  } catch (error) {
    console.error('Error in creators POST API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE - Unfollow a creator
export async function DELETE(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const serviceSupabase = createServiceRoleClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const creatorId = searchParams.get('creator_id')
    const platform = searchParams.get('platform')
    const handle = searchParams.get('handle')

    // Support deletion by creator_id OR by platform+handle
    let deleteConditions: any = { user_id: user.id }

    if (creatorId) {
      deleteConditions.creator_id = creatorId
    } else if (platform && handle) {
      // First find the creator ID
      const { data: creator } = await supabase
        .from('creators')
        .select('id')
        .eq('platform', platform)
        .eq('handle', handle)
        .single()

      if (!creator) {
        return NextResponse.json({ 
          error: 'Creator not found' 
        }, { status: 404 })
      }

      deleteConditions.creator_id = creator.id
    } else {
      return NextResponse.json({ 
        error: 'Either creator_id or platform+handle must be provided' 
      }, { status: 400 })
    }

    // Remove the follow relationship
    const { error: deleteError } = await serviceSupabase
      .from('user_creators')
      .delete()
      .match(deleteConditions)

    if (deleteError) {
      console.error('Error unfollowing creator:', deleteError)
      return NextResponse.json({ 
        error: 'Failed to unfollow creator' 
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: 'Successfully unfollowed creator'
    })

  } catch (error) {
    console.error('Error in creators DELETE API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient, createServiceRoleClient } from '@/lib/supabase-server'

// GET /api/users/bundles - Get user's subscribed bundles
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's subscribed bundles
    const { data: userBundles, error: bundlesError } = await supabase
      .from('user_bundles')
      .select(`
        id,
        bundle_id,
        added_at,
        included_creator_ids,
        creator_bundles (
          id,
          title,
          description,
          tags,
          platform,
          creator_count,
          refreshed_at,
          status
        )
      `)
      .eq('user_id', user.id)
      .order('added_at', { ascending: false })

    if (bundlesError) {
      console.error('Error fetching user bundles:', bundlesError)
      return NextResponse.json({ 
        error: 'Failed to fetch user bundles' 
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      bundles: userBundles || [],
      count: userBundles?.length || 0
    })

  } catch (error) {
    console.error('Error in user bundles GET API:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}

// POST /api/users/bundles - Add a bundle to user's subscriptions
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const serviceSupabase = createServiceRoleClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { bundle_id } = body

    if (!bundle_id) {
      return NextResponse.json({ 
        error: 'Bundle ID is required' 
      }, { status: 400 })
    }

    // Check if bundle exists and is active
    const { data: bundle, error: bundleError } = await supabase
      .from('creator_bundles')
      .select('id, title, status')
      .eq('id', bundle_id)
      .eq('status', 'active')
      .single()

    if (bundleError || !bundle) {
      return NextResponse.json({ 
        error: 'Bundle not found or inactive' 
      }, { status: 404 })
    }

    // Check if user already subscribed to this bundle
    const { data: existingSubscription } = await supabase
      .from('user_bundles')
      .select('id')
      .eq('user_id', user.id)
      .eq('bundle_id', bundle_id)
      .single()

    if (existingSubscription) {
      return NextResponse.json({
        success: false,
        error: 'Already subscribed to this bundle'
      }, { status: 409 })
    }

    // Get all creators in the bundle
    const { data: bundleCreators, error: creatorsError } = await supabase
      .from('bundle_creators')
      .select('creator_id')
      .eq('bundle_id', bundle_id)
      .order('position', { ascending: true })

    if (creatorsError) {
      console.error('Error fetching bundle creators:', creatorsError)
      return NextResponse.json({ 
        error: 'Failed to fetch bundle creators' 
      }, { status: 500 })
    }

    const creatorIds = bundleCreators?.map(bc => bc.creator_id) || []

    // Add user bundle subscription
    const { data: userBundle, error: subscriptionError } = await serviceSupabase
      .from('user_bundles')
      .insert({
        user_id: user.id,
        bundle_id,
        included_creator_ids: creatorIds
      })
      .select()
      .single()

    if (subscriptionError) {
      console.error('Error creating user bundle subscription:', subscriptionError)
      return NextResponse.json({ 
        error: 'Failed to subscribe to bundle' 
      }, { status: 500 })
    }

    // Add all creators from the bundle to user's creators list
    const userCreatorInserts = creatorIds.map(creator_id => ({
      user_id: user.id,
      creator_id
    }))

    if (userCreatorInserts.length > 0) {
      // Use upsert to avoid duplicates
      const { error: userCreatorsError } = await serviceSupabase
        .from('user_creators')
        .upsert(userCreatorInserts, { 
          onConflict: 'user_id,creator_id',
          ignoreDuplicates: true 
        })

      if (userCreatorsError) {
        console.error('Error adding creators to user:', userCreatorsError)
        // Don't fail the request, just log the error
      }
    }

    // Track analytics event
    try {
      await serviceSupabase
        .from('bundle_analytics')
        .insert({
          bundle_id,
          user_id: user.id,
          event_type: 'add',
          metadata: {
            creators_added: creatorIds.length,
            bundle_title: bundle.title
          }
        })
    } catch (analyticsError) {
      console.error('Error tracking bundle analytics:', analyticsError)
      // Don't fail the request for analytics errors
    }

    return NextResponse.json({
      success: true,
      message: `Successfully added bundle "${bundle.title}" with ${creatorIds.length} creators`,
      user_bundle: userBundle,
      creators_added: creatorIds.length
    })

  } catch (error) {
    console.error('Error in user bundles POST API:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}

// DELETE /api/users/bundles - Remove a bundle from user's subscriptions
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const bundle_id = searchParams.get('bundle_id')

    if (!bundle_id) {
      return NextResponse.json({ 
        error: 'Bundle ID is required' 
      }, { status: 400 })
    }

    const supabase = await createServerSupabaseClient()
    const serviceSupabase = createServiceRoleClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get the user bundle subscription
    const { data: userBundle, error: fetchError } = await supabase
      .from('user_bundles')
      .select('id, included_creator_ids')
      .eq('user_id', user.id)
      .eq('bundle_id', bundle_id)
      .single()

    if (fetchError || !userBundle) {
      return NextResponse.json({ 
        error: 'Bundle subscription not found' 
      }, { status: 404 })
    }

    // Remove the user bundle subscription
    const { error: deleteError } = await serviceSupabase
      .from('user_bundles')
      .delete()
      .eq('user_id', user.id)
      .eq('bundle_id', bundle_id)

    if (deleteError) {
      console.error('Error removing user bundle:', deleteError)
      return NextResponse.json({ 
        error: 'Failed to remove bundle subscription' 
      }, { status: 500 })
    }

    // Track analytics event
    try {
      await serviceSupabase
        .from('bundle_analytics')
        .insert({
          bundle_id,
          user_id: user.id,
          event_type: 'remove',
          metadata: {
            creators_removed: userBundle.included_creator_ids?.length || 0
          }
        })
    } catch (analyticsError) {
      console.error('Error tracking bundle analytics:', analyticsError)
    }

    return NextResponse.json({
      success: true,
      message: 'Bundle subscription removed successfully'
    })

  } catch (error) {
    console.error('Error in user bundles DELETE API:', error)
    return NextResponse.json({ 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}

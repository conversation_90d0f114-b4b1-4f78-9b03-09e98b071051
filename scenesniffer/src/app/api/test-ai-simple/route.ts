import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'

export async function POST(request: NextRequest) {
  try {
    console.log('🤖 Testing Simple AI...')
    
    // Use the working API key directly
    const apiKey = '********************************************************************************************************************************************************************'
    
    const client = new OpenAI({
      apiKey: apiKey
    })

    // Test simple categorization
    const response = await client.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: 'You are an expert at categorizing movie and TV content. Return only the category name.'
        },
        {
          role: 'user',
          content: `
            Categorize the following content into one of these types:
            - review: Reviews, ratings, opinions about movies/shows
            - theory: Fan theories, explanations, analysis
            - news: News, updates, announcements
            - spoiler-free: Spoiler-free reviews or discussions
            - breakdown: Scene breakdowns, behind-the-scenes content
            - recommendation: Recommendations, "must watch" lists
            
            Title: The Batman (2022) - Movie Review
            Description: My thoughts on Matt Reeves' The Batman starring Robert Pattinson. This dark and gritty take on the Dark Knight brings a noir detective story to Gotham City. Spoiler-free review covering the cinematography, performances, and overall direction.
            
            Return only the category name (lowercase).
          `
        }
      ],
      max_tokens: 10,
      temperature: 0.1
    })

    const category = response.choices[0]?.message?.content?.trim().toLowerCase()
    
    // Test simple summary
    const summaryResponse = await client.chat.completions.create({
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: 'You are an expert at summarizing movie and TV content. Provide concise, engaging summaries.'
        },
        {
          role: 'user',
          content: `
            Summarize this review content in 2-3 sentences. Be engaging and informative.
            
            Title: The Batman (2022) - Movie Review
            Description: My thoughts on Matt Reeves' The Batman starring Robert Pattinson. This dark and gritty take on the Dark Knight brings a noir detective story to Gotham City. Spoiler-free review covering the cinematography, performances, and overall direction.
            
            Focus on the main points and what makes this content valuable to viewers.
          `
        }
      ],
      max_tokens: 200,
      temperature: 0.7
    })

    const summary = summaryResponse.choices[0]?.message?.content?.trim()

    return NextResponse.json({
      success: true,
      message: 'Simple AI test completed',
      results: {
        category,
        summary,
        apiKeyPrefix: apiKey.substring(0, 10)
      }
    })

  } catch (error: any) {
    console.error('Simple AI test error:', error)
    return NextResponse.json({
      success: false,
      error: error.message || 'Unknown error',
      code: error.code || 'unknown'
    }, { status: 500 })
  }
}

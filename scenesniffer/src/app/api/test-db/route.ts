import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase-server'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()

    // Test database connection
    const { data: tables, error: tablesError } = await supabase
      .from('creators')
      .select('count')
      .limit(1)

    if (tablesError) {
      return NextResponse.json({
        success: false,
        error: 'Database connection failed',
        details: tablesError.message
      })
    }

    // Test inserting a sample creator
    const { data: creator, error: insertError } = await supabase
      .from('creators')
      .insert({
        name: '<PERSON><PERSON>',
        platform: 'youtube',
        handle: '@mkbhd',
        verified: true,
        trust_score: 9.5,
        follower_count: 20100000
      })
      .select()
      .single()

    if (insertError && insertError.code !== '23505') { // Ignore duplicate key error
      return NextResponse.json({
        success: false,
        error: 'Failed to insert test creator',
        details: insertError.message
      })
    }

    // Test fetching creators
    const { data: creators, error: fetchError } = await supabase
      .from('creators')
      .select('*')
      .limit(5)

    if (fetchError) {
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch creators',
        details: fetchError.message
      })
    }

    return NextResponse.json({
      success: true,
      message: 'Database tests passed!',
      data: {
        creatorsCount: creators?.length || 0,
        sampleCreators: creators?.slice(0, 3) || []
      }
    })

  } catch (error) {
    console.error('Database test error:', error)
    return NextResponse.json({
      success: false,
      error: 'Database test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
import { createServiceRoleClient } from '@/lib/supabase-server'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const serviceSupabase = createServiceRoleClient()
    
    console.log('⚖️ Comparing Old vs Enhanced Feed Algorithms...')
    
    // Mock user preferences
    const mockUserPrefs = {
      genres: ['action', 'sci-fi', 'horror'],
      streaming_services: ['netflix', 'disney']
    }
    
    // Get content for comparison
    const { data: content, error: contentError } = await serviceSupabase
      .from('content')
      .select(`
        id,
        title,
        description,
        content_type,
        published_at,
        ai_summary,
        referenced_titles,
        tags,
        creators (
          name,
          trust_score,
          verified
        )
      `)
      .order('published_at', { ascending: false })
      .limit(8)
    
    if (contentError || !content) {
      return NextResponse.json({ error: 'Failed to fetch content' }, { status: 500 })
    }

    // Score with OLD algorithm (basic)
    const oldScored = content.map(item => ({
      ...item,
      oldScore: calculateOldRelevanceScore(item, mockUserPrefs)
    })).sort((a, b) => b.oldScore - a.oldScore)

    // Score with NEW algorithm (AI-enhanced)
    const newScored = content.map(item => ({
      ...item,
      newScore: calculateNewRelevanceScore(item, mockUserPrefs)
    })).sort((a, b) => b.newScore - a.newScore)

    // Create comparison
    const comparison = content.map(item => {
      const oldRank = oldScored.findIndex(x => x.id === item.id) + 1
      const newRank = newScored.findIndex(x => x.id === item.id) + 1
      const oldScore = oldScored.find(x => x.id === item.id)?.oldScore || 0
      const newScore = newScored.find(x => x.id === item.id)?.newScore || 0
      
      return {
        title: item.title,
        creator: item.creators?.name,
        contentType: item.content_type,
        hasAiSummary: !!item.ai_summary,
        referencedTitles: item.referenced_titles?.length || 0,
        tags: item.tags?.length || 0,
        oldAlgorithm: {
          score: oldScore,
          rank: oldRank
        },
        newAlgorithm: {
          score: newScore,
          rank: newRank
        },
        improvement: {
          scoreDiff: newScore - oldScore,
          rankChange: oldRank - newRank, // Positive = moved up
          percentImprovement: oldScore > 0 ? Math.round(((newScore - oldScore) / oldScore) * 100) : 0
        }
      }
    })

    // Sort by improvement
    const sortedComparison = comparison.sort((a, b) => b.improvement.scoreDiff - a.improvement.scoreDiff)

    // Calculate metrics
    const metrics = {
      totalItems: content.length,
      aiProcessedItems: content.filter(item => item.ai_summary).length,
      averageScoreIncrease: Math.round(
        comparison.reduce((sum, item) => sum + item.improvement.scoreDiff, 0) / comparison.length
      ),
      itemsImproved: comparison.filter(item => item.improvement.scoreDiff > 0).length,
      biggestWinner: sortedComparison[0],
      aiContentAdvantage: {
        aiProcessedAvgScore: Math.round(
          comparison.filter(item => item.hasAiSummary)
            .reduce((sum, item) => sum + item.newAlgorithm.score, 0) / 
          comparison.filter(item => item.hasAiSummary).length
        ),
        nonAiAvgScore: Math.round(
          comparison.filter(item => !item.hasAiSummary)
            .reduce((sum, item) => sum + item.newAlgorithm.score, 0) / 
          comparison.filter(item => !item.hasAiSummary).length
        )
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Algorithm comparison completed',
      metrics,
      comparison: sortedComparison,
      topRankedOld: oldScored.slice(0, 3).map(item => ({ title: item.title, score: item.oldScore })),
      topRankedNew: newScored.slice(0, 3).map(item => ({ title: item.title, score: item.newScore }))
    })

  } catch (error) {
    console.error('Algorithm comparison error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// OLD Algorithm (basic scoring)
function calculateOldRelevanceScore(content: any, userPrefs: any): number {
  let score = 0

  // Basic creator trust score
  if (content.creators?.trust_score) {
    score += content.creators.trust_score * 10
  }

  // Verified creator boost
  if (content.creators?.verified) {
    score += 20
  }

  // Basic recency
  const publishedDate = new Date(content.published_at)
  const daysSincePublished = (Date.now() - publishedDate.getTime()) / (1000 * 60 * 60 * 24)
  score += Math.max(0, 30 - daysSincePublished)

  // Simple content type scoring
  const basicTypeScores: { [key: string]: number } = {
    'review': 10,
    'recommendation': 15,
    'theory': 8,
    'news': 5,
    'breakdown': 12,
    'spoiler-free': 10
  }
  
  if (content.content_type && basicTypeScores[content.content_type]) {
    score += basicTypeScores[content.content_type]
  }

  // Basic AI boost
  if (content.ai_summary) {
    score += 10
  }

  // Basic referenced titles boost
  if (content.referenced_titles && content.referenced_titles.length > 0) {
    score += content.referenced_titles.length * 5
  }

  return Math.round(score)
}

// NEW Algorithm (AI-enhanced)
function calculateNewRelevanceScore(content: any, userPrefs: any): number {
  let score = 0

  // Enhanced creator scoring
  if (content.creators?.trust_score) {
    score += content.creators.trust_score * 10
  }

  if (content.creators?.verified) {
    score += 20
  }

  // Enhanced recency
  const publishedDate = new Date(content.published_at)
  const daysSincePublished = (Date.now() - publishedDate.getTime()) / (1000 * 60 * 60 * 24)
  score += Math.max(0, 30 - daysSincePublished)

  // AI-Enhanced Content Type Scoring
  const aiContentTypeScores: { [key: string]: number } = {
    'review': 12,
    'recommendation': 18,
    'theory': 10,
    'news': 8,
    'breakdown': 15,
    'spoiler-free': 14
  }
  
  if (content.content_type && aiContentTypeScores[content.content_type]) {
    score += aiContentTypeScores[content.content_type]
  }

  // AI Summary Quality Boost
  if (content.ai_summary) {
    const summaryLength = content.ai_summary.length
    let summaryBoost = 15
    if (summaryLength > 100) summaryBoost += 5
    if (summaryLength > 200) summaryBoost += 5
    score += summaryBoost
  }

  // Enhanced Referenced Titles Boost
  if (content.referenced_titles && content.referenced_titles.length > 0) {
    score += Math.min(content.referenced_titles.length * 8, 40)
  }

  // AI Tags Quality Boost
  if (content.tags && content.tags.length > 0) {
    score += Math.min(content.tags.length * 2, 12)
    
    // Genre matching with AI tags
    if (userPrefs?.genres) {
      const matches = userPrefs.genres.filter((genre: string) => 
        content.tags.some((tag: string) => tag.toLowerCase().includes(genre.toLowerCase()))
      )
      score += matches.length * 20
    }
  }

  // Penalty for unprocessed content
  if (!content.ai_summary && !content.referenced_titles) {
    score -= 15
  }

  return Math.max(0, Math.round(score))
}

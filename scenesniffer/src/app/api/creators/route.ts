import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase-server'
import { youtubeAPI } from '@/lib/youtube'

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { searchParams } = new URL(request.url)
    const platform = searchParams.get('platform')
    const search = searchParams.get('search')

    let query = supabase
      .from('creators')
      .select('*')
      .order('trust_score', { ascending: false })

    if (platform) {
      query = query.eq('platform', platform)
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,handle.ilike.%${search}%`)
    }

    const { data: creators, error } = await query.limit(50)

    if (error) {
      console.error('Error fetching creators:', error)
      return NextResponse.json({ error: 'Failed to fetch creators' }, { status: 500 })
    }

    return NextResponse.json({ creators })
  } catch (error) {
    console.error('Error in creators API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { platform, handle } = body

    if (!platform || !handle) {
      return NextResponse.json({ error: 'Platform and handle are required' }, { status: 400 })
    }

    // Check if creator already exists
    const { data: existingCreator } = await supabase
      .from('creators')
      .select('*')
      .eq('platform', platform)
      .eq('handle', handle)
      .single()

    let creator = existingCreator

    if (!creator) {
      // Create new creator by fetching info from platform
      let creatorInfo: any = {}

      if (platform === 'youtube') {
        try {
          const channelInfo = await youtubeAPI.getChannelInfo(handle)
          creatorInfo = {
            name: channelInfo.snippet.title,
            avatar_url: channelInfo.snippet.thumbnails?.default?.url,
            follower_count: parseInt(channelInfo.statistics?.subscriberCount || '0'),
            verified: channelInfo.snippet.customUrl ? true : false
          }
        } catch (error) {
          console.error('Error fetching YouTube channel info:', error)
          // Continue with basic info if API fails
          creatorInfo = {
            name: handle,
            verified: false
          }
        }
      } else {
        // For other platforms, use basic info for now
        creatorInfo = {
          name: handle,
          verified: false
        }
      }

      const { data: newCreator, error: createError } = await supabase
        .from('creators')
        .insert({
          platform,
          handle,
          ...creatorInfo
        })
        .select()
        .single()

      if (createError) {
        console.error('Error creating creator:', createError)
        return NextResponse.json({ error: 'Failed to create creator' }, { status: 500 })
      }

      creator = newCreator
    }

    // Add creator to user's following list
    const { error: followError } = await supabase
      .from('user_creators')
      .insert({
        user_id: user.id,
        creator_id: creator.id
      })

    if (followError && followError.code !== '23505') { // Ignore duplicate key error
      console.error('Error following creator:', followError)
      return NextResponse.json({ error: 'Failed to follow creator' }, { status: 500 })
    }

    return NextResponse.json({ creator })
  } catch (error) {
    console.error('Error in creators POST API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

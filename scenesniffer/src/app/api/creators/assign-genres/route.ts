import { NextRequest, NextResponse } from 'next/server'
import { createServiceRoleClient } from '@/lib/supabase-server'

export async function POST(request: NextRequest) {
  try {
    const supabase = createServiceRoleClient()
    const { searchParams } = new URL(request.url)
    const dryRun = searchParams.get('dryRun') === 'true'
    
    console.log(`🎭 Starting creator genre assignment (dryRun: ${dryRun})`)

    // Get creators without genres
    const { data: creators, error: fetchError } = await supabase
      .from('creators')
      .select(`
        id,
        name,
        platform,
        handle,
        genres
      `)
      .or('genres.is.null,genres.eq.{}')

    if (fetchError) {
      throw new Error(`Failed to fetch creators: ${fetchError.message}`)
    }

    if (!creators || creators.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'All creators already have genres assigned',
        processed: 0
      })
    }

    console.log(`Found ${creators.length} creators needing genre assignment`)

    const results = []
    let processed = 0
    let errors = 0

    // Initialize OpenAI
    const OpenAI = require('openai')
    const client = new OpenAI({
      apiKey: '********************************************************************************************************************************************************************'
    })

    for (const creator of creators) {
      try {
        console.log(`Processing creator: ${creator.name}`)

        // Get sample content from this creator to analyze their focus
        const { data: creatorContent } = await supabase
          .from('content')
          .select('title, description, tags')
          .eq('creator_id', creator.id)
          .order('created_at', { ascending: false })
          .limit(10)

        let contentSample = ''
        if (creatorContent && creatorContent.length > 0) {
          contentSample = creatorContent
            .map(c => `${c.title}: ${c.description}`)
            .join('\n')
            .substring(0, 2000) // Limit length
        }

        // Use AI to determine creator's genre focus
        const genreResponse = await client.chat.completions.create({
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: 'You are an expert at categorizing content creators by their genre focus. Analyze the creator and their content to determine what movie/TV genres they typically cover. Return as a JSON array of genre strings.'
            },
            {
              role: 'user',
              content: `Analyze this ${creator.platform} creator and assign relevant genres based on their content focus:

Creator: ${creator.name} (${creator.handle})
Platform: ${creator.platform}

Sample Content:
${contentSample || 'No content available - use creator name/handle to infer'}

Available genres: Action, Adventure, Animation, Comedy, Crime, Drama, Horror, History, Fantasy, Mystery, Science Fiction, Thriller, Family, War, Anime, Dark Comedy, Psychological Thriller, Superhero, True Crime, Indie, Sports, Documentary

Return 2-5 most relevant genres as JSON array like ["Action", "Thriller", "Superhero"]`
            }
          ],
          max_tokens: 100,
          temperature: 0.3
        })

        let genres = []
        try {
          const genresText = genreResponse.choices[0]?.message?.content?.trim() || '[]'
          genres = JSON.parse(genresText)
          
          // Validate genres are from our allowed list
          const validGenres = ['Action', 'Adventure', 'Animation', 'Comedy', 'Crime', 'Drama', 'Horror', 'History', 'Fantasy', 'Mystery', 'Science Fiction', 'Thriller', 'Family', 'War', 'Anime', 'Dark Comedy', 'Psychological Thriller', 'Superhero', 'True Crime', 'Indie', 'Sports', 'Documentary']
          genres = genres.filter(g => validGenres.includes(g))
          
          // Fallback if no valid genres
          if (genres.length === 0) {
            genres = ['Entertainment'] // Generic fallback
          }
        } catch (parseError) {
          // Fallback based on creator name/platform
          if (creator.name.toLowerCase().includes('game')) {
            genres = ['Action', 'Adventure']
          } else if (creator.name.toLowerCase().includes('horror')) {
            genres = ['Horror', 'Thriller']
          } else if (creator.name.toLowerCase().includes('comedy')) {
            genres = ['Comedy']
          } else {
            genres = ['Drama', 'Entertainment'] // Generic fallback
          }
        }

        results.push({
          id: creator.id,
          name: creator.name,
          platform: creator.platform,
          assignedGenres: genres,
          contentSampleLength: contentSample.length,
          success: true
        })

        // Update database if not dry run
        if (!dryRun) {
          const { error: updateError } = await supabase
            .from('creators')
            .update({ genres })
            .eq('id', creator.id)

          if (updateError) {
            throw new Error(`Update failed: ${updateError.message}`)
          }
        }

        processed++
        console.log(`✅ Assigned genres to ${creator.name}: ${genres.join(', ')}`)

        // Rate limiting
        await new Promise(resolve => setTimeout(resolve, 1000))

      } catch (itemError) {
        console.error(`❌ Error processing ${creator.name}:`, itemError)
        errors++
        results.push({
          id: creator.id,
          name: creator.name,
          error: itemError.message,
          success: false
        })
      }
    }

    return NextResponse.json({
      success: true,
      message: `Genre assignment ${dryRun ? 'simulation' : 'completed'}`,
      processed,
      errors,
      total: creators.length,
      results: results.slice(0, 10), // Show first 10 results
      dryRun
    })

  } catch (error) {
    console.error('Genre assignment error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

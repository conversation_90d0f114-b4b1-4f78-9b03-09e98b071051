import { createServerSupabaseClient } from '@/lib/supabase-server'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createServerSupabaseClient()
    const { id: creatorId } = await params
    const { searchParams } = new URL(request.url)
    
    const limit = parseInt(searchParams.get('limit') || '10')
    const exclude = searchParams.get('exclude') // Content ID to exclude
    const contentType = searchParams.get('type') // Filter by content type

    // Build query
    let query = supabase
      .from('content')
      .select(`
        id,
        title,
        description,
        content_type,
        platform_url,
        thumbnail_url,
        published_at,
        ai_summary,
        referenced_titles,
        tags
      `)
      .eq('creator_id', creatorId)
      .order('published_at', { ascending: false })
      .limit(limit)

    // Exclude specific content if provided
    if (exclude) {
      query = query.neq('id', exclude)
    }

    // Filter by content type if provided
    if (contentType) {
      query = query.eq('content_type', contentType)
    }

    const { data: content, error: contentError } = await query

    if (contentError) {
      console.error('Error fetching creator content:', contentError)
      return NextResponse.json({
        success: false,
        error: 'Failed to fetch creator content'
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      content: content || [],
      total: content?.length || 0
    })

  } catch (error) {
    console.error('Error fetching creator content:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

import { createServiceRoleClient } from '@/lib/supabase-server'
import { streamingService } from '@/lib/streaming-availability'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('🎬 Testing Streaming with AI-Enhanced Content...')
    
    const serviceSupabase = createServiceRoleClient()
    
    // Get AI-enhanced content with referenced titles
    const { data: content, error: contentError } = await serviceSupabase
      .from('content')
      .select(`
        id,
        title,
        referenced_titles,
        ai_summary,
        content_type,
        creators (name)
      `)
      .not('ai_summary', 'is', null)
      .not('referenced_titles', 'is', null)
      .limit(5)
    
    if (contentError || !content || content.length === 0) {
      return NextResponse.json({ 
        success: true,
        message: 'No AI-enhanced content with referenced titles found',
        note: 'Run AI enhancement first to get referenced titles'
      })
    }

    console.log(`Found ${content.length} AI-enhanced content items`)
    
    // Mock user streaming services
    const mockUserServices = ['netflix', 'disney', 'hbo', 'amazon']
    
    const results = []
    
    for (const item of content) {
      try {
        console.log(`\n🎬 Processing: ${item.title}`)
        console.log(`Referenced titles: ${item.referenced_titles?.join(', ')}`)
        
        // Get streaming availability
        const streamingData = await streamingService.getStreamingForContent(
          item.referenced_titles || [],
          [] // Don't filter by user services for testing
        )
        
        console.log(`Found streaming for ${streamingData.length} titles`)
        
        // Also test with user service filtering
        const filteredStreamingData = await streamingService.getStreamingForContent(
          item.referenced_titles || [],
          mockUserServices
        )
        
        results.push({
          contentId: item.id,
          contentTitle: item.title,
          contentType: item.content_type,
          creator: item.creators?.name,
          referencedTitles: item.referenced_titles,
          allStreaming: streamingData.map(s => ({
            title: s.title,
            type: s.content_type,
            tmdbId: s.tmdb_id,
            posterPath: s.poster_path,
            availableOn: {
              subscription: s.providers.flatrate?.map(p => p.provider_name) || [],
              rental: s.providers.rent?.map(p => p.provider_name) || [],
              purchase: s.providers.buy?.map(p => p.provider_name) || []
            }
          })),
          userFilteredStreaming: filteredStreamingData.map(s => ({
            title: s.title,
            type: s.content_type,
            availableOn: {
              subscription: s.providers.flatrate?.map(p => p.provider_name) || [],
              rental: s.providers.rent?.map(p => p.provider_name) || [],
              purchase: s.providers.buy?.map(p => p.provider_name) || []
            }
          }))
        })
        
      } catch (error) {
        console.error(`Error processing ${item.title}:`, error)
        results.push({
          contentTitle: item.title,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // Now test updating the database with streaming data
    console.log('\n🎬 Testing database updates...')
    const updateResults = []
    
    for (const result of results.slice(0, 2)) { // Test with first 2 items
      if (result.allStreaming && result.allStreaming.length > 0) {
        try {
          const { error: updateError } = await serviceSupabase
            .from('content')
            .update({
              streaming_availability: result.allStreaming
            })
            .eq('id', result.contentId)
          
          updateResults.push({
            contentId: result.contentId,
            title: result.contentTitle,
            updated: !updateError,
            error: updateError?.message
          })
          
          if (!updateError) {
            console.log(`✅ Updated streaming data for: ${result.contentTitle}`)
          }
          
        } catch (error) {
          updateResults.push({
            contentId: result.contentId,
            title: result.contentTitle,
            updated: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Enhanced streaming availability test completed',
      mockUserServices,
      contentProcessed: content.length,
      results,
      databaseUpdates: updateResults,
      summary: {
        totalContentTested: content.length,
        contentWithStreaming: results.filter(r => r.allStreaming?.length > 0).length,
        totalTitlesFound: results.reduce((sum, r) => sum + (r.allStreaming?.length || 0), 0),
        userFilteredResults: results.filter(r => r.userFilteredStreaming?.length > 0).length,
        databaseUpdatesSuccessful: updateResults.filter(u => u.updated).length
      }
    })

  } catch (error) {
    console.error('Enhanced streaming test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

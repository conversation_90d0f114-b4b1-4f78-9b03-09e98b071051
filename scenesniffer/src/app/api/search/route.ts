import { NextRequest, NextResponse } from 'next/server'
import { tmdbAPI } from '@/lib/tmdb'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('q')
    const type = searchParams.get('type') // 'movie', 'tv', or 'multi'

    if (!query) {
      return NextResponse.json({ error: 'Query parameter is required' }, { status: 400 })
    }

    let results: any = {}

    switch (type) {
      case 'movie':
        const movies = await tmdbAPI.searchMovies(query)
        results = { movies }
        break
      
      case 'tv':
        const tvShows = await tmdbAPI.searchTVShows(query)
        results = { tvShows }
        break
      
      case 'multi':
      default:
        const multiResults = await tmdbAPI.searchMulti(query)
        results = multiResults
        break
    }

    return NextResponse.json(results)
  } catch (error) {
    console.error('Error in search API:', error)
    return NextResponse.json({ error: 'Search failed' }, { status: 500 })
  }
}

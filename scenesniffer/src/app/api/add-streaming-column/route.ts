import { createServiceRoleClient } from '@/lib/supabase-server'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Adding streaming_availability column to content table...')
    
    const serviceSupabase = createServiceRoleClient()
    
    // Add the streaming_availability column
    const { error: alterError } = await serviceSupabase.rpc('exec_sql', {
      sql: `
        ALTER TABLE public.content 
        ADD COLUMN IF NOT EXISTS streaming_availability JSONB DEFAULT NULL;
        
        CREATE INDEX IF NOT EXISTS idx_content_streaming_availability 
        ON public.content USING GIN (streaming_availability);
      `
    })
    
    if (alterError) {
      console.error('Error adding column:', alterError)
      return NextResponse.json({ 
        success: false, 
        error: 'Failed to add column: ' + alterError.message 
      }, { status: 500 })
    }

    console.log('✅ Column added successfully')
    
    // Test the column by updating a record
    const { data: testContent, error: fetchError } = await serviceSupabase
      .from('content')
      .select('id, title')
      .limit(1)
      .single()
    
    if (fetchError || !testContent) {
      return NextResponse.json({
        success: true,
        message: 'Column added but no content found for testing'
      })
    }

    // Test updating with streaming data
    const testStreamingData = [{
      title: "Test Movie",
      tmdb_id: 12345,
      content_type: "movie",
      providers: {
        flatrate: [{ provider_name: "Netflix", provider_id: 8 }]
      }
    }]

    const { error: updateError } = await serviceSupabase
      .from('content')
      .update({ streaming_availability: testStreamingData })
      .eq('id', testContent.id)
    
    if (updateError) {
      console.error('Error testing column:', updateError)
      return NextResponse.json({
        success: true,
        message: 'Column added but test update failed: ' + updateError.message
      })
    }

    // Clean up test data
    await serviceSupabase
      .from('content')
      .update({ streaming_availability: null })
      .eq('id', testContent.id)

    return NextResponse.json({
      success: true,
      message: 'streaming_availability column added successfully and tested',
      testedWith: testContent.title
    })

  } catch (error) {
    console.error('Add column error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

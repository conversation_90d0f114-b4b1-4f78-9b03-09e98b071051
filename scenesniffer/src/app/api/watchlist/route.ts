import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase-server'

// GET /api/watchlist - Get user's watchlist with filtering and sorting
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const status = searchParams.get('status') // 'want_to_watch', 'watching', 'completed', etc.
    const contentType = searchParams.get('type') // 'movie', 'tv'
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    const sortBy = searchParams.get('sort') || 'added_at' // 'added_at', 'last_watched_at', 'title'
    const sortOrder = searchParams.get('order') || 'desc'

    let query = supabase
      .from('watchlist')
      .select(`
        id,
        content_type,
        tmdb_id,
        title,
        poster_path,
        status,
        current_season,
        current_episode,
        progress_percentage,
        last_watched_at,
        started_watching_at,
        user_rating,
        personal_notes,
        total_runtime,
        added_at,
        updated_at
      `)
      .eq('user_id', user.id)

    // Apply filters
    if (status) {
      query = query.eq('status', status)
    }
    if (contentType) {
      query = query.eq('content_type', contentType)
    }

    // Apply sorting
    query = query.order(sortBy, { ascending: sortOrder === 'asc' })

    // Apply pagination
    query = query.range(offset, offset + limit - 1)

    const { data: watchlistItems, error } = await query

    if (error) {
      console.error('Error fetching watchlist:', error)
      return NextResponse.json({ error: 'Failed to fetch watchlist' }, { status: 500 })
    }

    // Get total count for pagination
    const { count, error: countError } = await supabase
      .from('watchlist')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id)
      .eq(status ? 'status' : 'id', status || watchlistItems[0]?.id || '')

    return NextResponse.json({
      success: true,
      data: watchlistItems || [],
      pagination: {
        total: count || 0,
        limit,
        offset,
        hasMore: (count || 0) > offset + limit
      }
    })

  } catch (error) {
    console.error('Error in watchlist GET API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/watchlist - Add item to watchlist
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { 
      tmdb_id, 
      content_type, 
      title, 
      poster_path,
      status = 'want_to_watch',
      total_runtime 
    } = body

    if (!tmdb_id || !content_type || !title) {
      return NextResponse.json({ 
        error: 'tmdb_id, content_type, and title are required' 
      }, { status: 400 })
    }

    if (!['movie', 'tv'].includes(content_type)) {
      return NextResponse.json({ 
        error: 'content_type must be either "movie" or "tv"' 
      }, { status: 400 })
    }

    // Check if item already exists
    const { data: existingItem } = await supabase
      .from('watchlist')
      .select('id, status')
      .eq('user_id', user.id)
      .eq('tmdb_id', tmdb_id)
      .eq('content_type', content_type)
      .single()

    if (existingItem) {
      return NextResponse.json({ 
        error: 'Item already in watchlist',
        existing: existingItem
      }, { status: 409 })
    }

    // Add to watchlist
    const { data: newItem, error } = await supabase
      .from('watchlist')
      .insert({
        user_id: user.id,
        tmdb_id,
        content_type,
        title,
        poster_path,
        status,
        total_runtime,
        added_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error adding to watchlist:', error)
      return NextResponse.json({ error: 'Failed to add to watchlist' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: 'Added to watchlist successfully',
      data: newItem
    }, { status: 201 })

  } catch (error) {
    console.error('Error in watchlist POST API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase-server'

// GET /api/watchlist/currently-watching - Get user's currently watching items
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    const includeMetadata = searchParams.get('metadata') === 'true'

    // Use the currently_watching view we created
    let query = supabase
      .from('currently_watching')
      .select('*')
      .eq('user_id', user.id)
      .limit(limit)

    const { data: currentlyWatching, error } = await query

    if (error) {
      console.error('Error fetching currently watching:', error)
      return NextResponse.json({ error: 'Failed to fetch currently watching items' }, { status: 500 })
    }

    // If metadata is requested, enrich with additional info
    let enrichedData = currentlyWatching || []
    
    if (includeMetadata && enrichedData.length > 0) {
      // Add progress calculations and time estimates
      enrichedData = enrichedData.map(item => {
        const progressInfo = calculateProgressInfo(item)
        return {
          ...item,
          ...progressInfo
        }
      })
    }

    return NextResponse.json({
      success: true,
      data: enrichedData,
      count: enrichedData.length
    })

  } catch (error) {
    console.error('Error in currently watching API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// Helper function to calculate progress information
function calculateProgressInfo(item: any) {
  const progressInfo: any = {}

  // Calculate time remaining for movies
  if (item.content_type === 'movie' && item.runtime_minutes && item.progress_percentage) {
    const watchedMinutes = (item.progress_percentage / 100) * item.runtime_minutes
    const remainingMinutes = item.runtime_minutes - watchedMinutes
    
    progressInfo.watched_minutes = Math.round(watchedMinutes)
    progressInfo.remaining_minutes = Math.round(remainingMinutes)
    progressInfo.estimated_completion_time = formatMinutes(remainingMinutes)
  }

  // Calculate episode progress for TV shows
  if (item.content_type === 'tv') {
    const currentSeason = item.current_season || 1
    const currentEpisode = item.current_episode || 1
    
    progressInfo.season_episode = `S${currentSeason.toString().padStart(2, '0')}E${currentEpisode.toString().padStart(2, '0')}`
    
    // If we have total episodes, calculate overall progress
    if (item.total_episodes && currentEpisode && currentSeason) {
      // This is a simplified calculation - in reality you'd need episode counts per season
      const estimatedProgress = ((currentSeason - 1) * 10 + currentEpisode) / item.total_episodes * 100
      progressInfo.estimated_overall_progress = Math.min(estimatedProgress, 100)
    }
  }

  // Calculate days since last watched
  if (item.last_watched_at) {
    const daysSinceWatched = Math.floor(
      (new Date().getTime() - new Date(item.last_watched_at).getTime()) / (1000 * 60 * 60 * 24)
    )
    progressInfo.days_since_watched = daysSinceWatched
    
    // Add status indicators
    if (daysSinceWatched > 30) {
      progressInfo.status_indicator = 'stale'
    } else if (daysSinceWatched > 7) {
      progressInfo.status_indicator = 'inactive'
    } else {
      progressInfo.status_indicator = 'active'
    }
  }

  // Calculate watching streak
  if (item.started_watching_at) {
    const daysSinceStarted = Math.floor(
      (new Date().getTime() - new Date(item.started_watching_at).getTime()) / (1000 * 60 * 60 * 24)
    )
    progressInfo.days_watching = daysSinceStarted
  }

  return progressInfo
}

// Helper function to format minutes into readable time
function formatMinutes(minutes: number): string {
  if (minutes < 60) {
    return `${Math.round(minutes)}m`
  } else if (minutes < 1440) { // Less than 24 hours
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = Math.round(minutes % 60)
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
  } else {
    const days = Math.floor(minutes / 1440)
    const remainingHours = Math.floor((minutes % 1440) / 60)
    return remainingHours > 0 ? `${days}d ${remainingHours}h` : `${days}d`
  }
}

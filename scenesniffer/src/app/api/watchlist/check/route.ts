import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase-server'

// GET /api/watchlist/check - Check if items are in user's watchlist
export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const tmdbIds = searchParams.get('tmdb_ids')?.split(',') || []
    const contentType = searchParams.get('content_type')

    if (tmdbIds.length === 0) {
      return NextResponse.json({ 
        error: 'tmdb_ids parameter is required (comma-separated list)' 
      }, { status: 400 })
    }

    let query = supabase
      .from('watchlist')
      .select('tmdb_id, content_type, status, id')
      .eq('user_id', user.id)
      .in('tmdb_id', tmdbIds.map(id => parseInt(id)))

    if (contentType) {
      query = query.eq('content_type', contentType)
    }

    const { data: watchlistItems, error } = await query

    if (error) {
      console.error('Error checking watchlist:', error)
      return NextResponse.json({ error: 'Failed to check watchlist' }, { status: 500 })
    }

    // Create a map of tmdb_id -> watchlist status
    const watchlistMap: Record<string, any> = {}
    
    tmdbIds.forEach(tmdbId => {
      const item = watchlistItems?.find(w => w.tmdb_id === parseInt(tmdbId))
      watchlistMap[tmdbId] = item ? {
        in_watchlist: true,
        status: item.status,
        watchlist_id: item.id,
        content_type: item.content_type
      } : {
        in_watchlist: false,
        status: null,
        watchlist_id: null,
        content_type: null
      }
    })

    return NextResponse.json({
      success: true,
      data: watchlistMap
    })

  } catch (error) {
    console.error('Error in watchlist check API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/watchlist/check - Batch check multiple items with details
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { items } = body // Array of { tmdb_id, content_type }

    if (!Array.isArray(items) || items.length === 0) {
      return NextResponse.json({ 
        error: 'items array is required with format [{ tmdb_id, content_type }]' 
      }, { status: 400 })
    }

    // Validate items format
    for (const item of items) {
      if (!item.tmdb_id || !item.content_type) {
        return NextResponse.json({ 
          error: 'Each item must have tmdb_id and content_type' 
        }, { status: 400 })
      }
    }

    // Get all watchlist items for these TMDb IDs
    const tmdbIds = items.map(item => item.tmdb_id)
    
    const { data: watchlistItems, error } = await supabase
      .from('watchlist')
      .select('*')
      .eq('user_id', user.id)
      .in('tmdb_id', tmdbIds)

    if (error) {
      console.error('Error checking watchlist items:', error)
      return NextResponse.json({ error: 'Failed to check watchlist' }, { status: 500 })
    }

    // Create detailed response for each requested item
    const results = items.map(requestedItem => {
      const watchlistItem = watchlistItems?.find(w => 
        w.tmdb_id === requestedItem.tmdb_id && 
        w.content_type === requestedItem.content_type
      )

      return {
        tmdb_id: requestedItem.tmdb_id,
        content_type: requestedItem.content_type,
        in_watchlist: !!watchlistItem,
        watchlist_data: watchlistItem || null
      }
    })

    return NextResponse.json({
      success: true,
      data: results
    })

  } catch (error) {
    console.error('Error in watchlist batch check API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase-server'

// GET /api/watchlist/[id] - Get specific watchlist item
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const { data: item, error } = await supabase
      .from('watchlist')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single()

    if (error || !item) {
      return NextResponse.json({ error: 'Watchlist item not found' }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: item
    })

  } catch (error) {
    console.error('Error in watchlist item GET API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// PATCH /api/watchlist/[id] - Update watchlist item (status, progress, etc.)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    
    const {
      status,
      current_season,
      current_episode,
      progress_percentage,
      user_rating,
      personal_notes,
      total_runtime
    } = body

    // Validate status if provided
    if (status && !['want_to_watch', 'watching', 'completed', 'on_hold', 'dropped'].includes(status)) {
      return NextResponse.json({ 
        error: 'Invalid status. Must be one of: want_to_watch, watching, completed, on_hold, dropped' 
      }, { status: 400 })
    }

    // Validate rating if provided
    if (user_rating !== undefined && (user_rating < 1 || user_rating > 10)) {
      return NextResponse.json({ 
        error: 'Rating must be between 1 and 10' 
      }, { status: 400 })
    }

    // Validate progress percentage if provided
    if (progress_percentage !== undefined && (progress_percentage < 0 || progress_percentage > 100)) {
      return NextResponse.json({ 
        error: 'Progress percentage must be between 0 and 100' 
      }, { status: 400 })
    }

    // Build update object with only provided fields
    const updateData: any = {}
    if (status !== undefined) updateData.status = status
    if (current_season !== undefined) updateData.current_season = current_season
    if (current_episode !== undefined) updateData.current_episode = current_episode
    if (progress_percentage !== undefined) updateData.progress_percentage = progress_percentage
    if (user_rating !== undefined) updateData.user_rating = user_rating
    if (personal_notes !== undefined) updateData.personal_notes = personal_notes
    if (total_runtime !== undefined) updateData.total_runtime = total_runtime

    if (Object.keys(updateData).length === 0) {
      return NextResponse.json({ 
        error: 'No valid fields provided for update' 
      }, { status: 400 })
    }

    const { data: updatedItem, error } = await supabase
      .from('watchlist')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error updating watchlist item:', error)
      return NextResponse.json({ error: 'Failed to update watchlist item' }, { status: 500 })
    }

    if (!updatedItem) {
      return NextResponse.json({ error: 'Watchlist item not found' }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      message: 'Watchlist item updated successfully',
      data: updatedItem
    })

  } catch (error) {
    console.error('Error in watchlist item PATCH API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// DELETE /api/watchlist/[id] - Remove item from watchlist
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params

    const { error } = await supabase
      .from('watchlist')
      .delete()
      .eq('id', id)
      .eq('user_id', user.id)

    if (error) {
      console.error('Error deleting watchlist item:', error)
      return NextResponse.json({ error: 'Failed to delete watchlist item' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: 'Watchlist item deleted successfully'
    })

  } catch (error) {
    console.error('Error in watchlist item DELETE API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

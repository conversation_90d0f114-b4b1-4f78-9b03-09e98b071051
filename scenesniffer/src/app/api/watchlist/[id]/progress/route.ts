import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase-server'

// PATCH /api/watchlist/[id]/progress - Update viewing progress
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    
    const {
      progress_percentage,
      current_season,
      current_episode,
      minutes_watched,
      episode_completed = false
    } = body

    // Get current item to determine content type and calculate progress
    const { data: currentItem, error: fetchError } = await supabase
      .from('watchlist')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single()

    if (fetchError || !currentItem) {
      return NextResponse.json({ error: 'Watchlist item not found' }, { status: 404 })
    }

    let updateData: any = {}

    // Handle different progress update types
    if (progress_percentage !== undefined) {
      // Direct percentage update (for movies or manual updates)
      if (progress_percentage < 0 || progress_percentage > 100) {
        return NextResponse.json({ 
          error: 'Progress percentage must be between 0 and 100' 
        }, { status: 400 })
      }
      updateData.progress_percentage = progress_percentage
    }

    if (minutes_watched !== undefined && currentItem.total_runtime) {
      // Calculate percentage from minutes watched
      const percentage = Math.min((minutes_watched / currentItem.total_runtime) * 100, 100)
      updateData.progress_percentage = percentage
    }

    if (current_season !== undefined) {
      updateData.current_season = current_season
    }

    if (current_episode !== undefined) {
      updateData.current_episode = current_episode
    }

    // If episode completed for TV shows, increment episode
    if (episode_completed && currentItem.content_type === 'tv') {
      updateData.current_episode = (currentItem.current_episode || 0) + 1
      
      // For TV shows, we could calculate overall progress if we have total episodes
      // This would require TMDb integration to get total episode count
    }

    // Auto-set status to watching if not already
    if (currentItem.status === 'want_to_watch') {
      updateData.status = 'watching'
    }

    // Auto-complete if progress reaches 100%
    if (updateData.progress_percentage >= 100) {
      updateData.status = 'completed'
    }

    const { data: updatedItem, error } = await supabase
      .from('watchlist')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error updating progress:', error)
      return NextResponse.json({ error: 'Failed to update progress' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: 'Progress updated successfully',
      data: updatedItem
    })

  } catch (error) {
    console.error('Error in progress update API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

// POST /api/watchlist/[id]/progress - Quick progress actions
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createServerSupabaseClient()
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id } = await params
    const body = await request.json()
    const { action } = body

    // Get current item
    const { data: currentItem, error: fetchError } = await supabase
      .from('watchlist')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single()

    if (fetchError || !currentItem) {
      return NextResponse.json({ error: 'Watchlist item not found' }, { status: 404 })
    }

    let updateData: any = {}

    switch (action) {
      case 'start_watching':
        updateData = {
          status: 'watching',
          started_watching_at: new Date().toISOString()
        }
        break

      case 'finish_watching':
        updateData = {
          status: 'completed',
          progress_percentage: 100,
          last_watched_at: new Date().toISOString()
        }
        break

      case 'next_episode':
        if (currentItem.content_type === 'tv') {
          updateData = {
            current_episode: (currentItem.current_episode || 0) + 1,
            status: currentItem.status === 'want_to_watch' ? 'watching' : currentItem.status
          }
        } else {
          return NextResponse.json({ 
            error: 'Next episode action only available for TV shows' 
          }, { status: 400 })
        }
        break

      case 'next_season':
        if (currentItem.content_type === 'tv') {
          updateData = {
            current_season: (currentItem.current_season || 0) + 1,
            current_episode: 1,
            status: currentItem.status === 'want_to_watch' ? 'watching' : currentItem.status
          }
        } else {
          return NextResponse.json({ 
            error: 'Next season action only available for TV shows' 
          }, { status: 400 })
        }
        break

      case 'mark_current_episode_watched':
        if (currentItem.content_type === 'tv') {
          updateData = {
            current_episode: (currentItem.current_episode || 0) + 1,
            last_watched_at: new Date().toISOString(),
            status: currentItem.status === 'want_to_watch' ? 'watching' : currentItem.status
          }
        } else {
          return NextResponse.json({ 
            error: 'Episode actions only available for TV shows' 
          }, { status: 400 })
        }
        break

      default:
        return NextResponse.json({ 
          error: 'Invalid action. Available actions: start_watching, finish_watching, next_episode, next_season, mark_current_episode_watched' 
        }, { status: 400 })
    }

    const { data: updatedItem, error } = await supabase
      .from('watchlist')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', user.id)
      .select()
      .single()

    if (error) {
      console.error('Error performing progress action:', error)
      return NextResponse.json({ error: 'Failed to perform action' }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: `Action "${action}" completed successfully`,
      data: updatedItem
    })

  } catch (error) {
    console.error('Error in progress action API:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

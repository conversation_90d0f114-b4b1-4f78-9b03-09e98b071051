import { createServiceRoleClient } from '@/lib/supabase-server'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('🧪 Testing Creator Management APIs...')
    
    const serviceSupabase = createServiceRoleClient()
    
    // Get a test user
    const { data: testUser, error: userError } = await serviceSupabase
      .from('users')
      .select('id, username')
      .limit(1)
      .single()
    
    if (userError || !testUser) {
      return NextResponse.json({
        success: false,
        error: 'No test user found. Please ensure you have users in the database.'
      })
    }

    console.log('Using test user:', testUser.username)

    // Test 1: Get current creators
    const { data: currentCreators } = await serviceSupabase
      .from('user_creators')
      .select(`
        creator_id,
        creators (
          platform,
          handle,
          name
        )
      `)
      .eq('user_id', testUser.id)

    console.log('Current creators:', currentCreators?.length || 0)

    // Test 2: Add a test creator
    const testCreatorData = {
      platform: 'youtube',
      handle: 'TestCreator' + Date.now(),
      name: 'Test Creator for API Demo'
    }

    // Check if creator exists
    const { data: existingCreator } = await serviceSupabase
      .from('creators')
      .select('id')
      .eq('platform', testCreatorData.platform)
      .eq('handle', testCreatorData.handle)
      .single()

    let creatorId: string

    if (existingCreator) {
      creatorId = existingCreator.id
    } else {
      // Create new creator
      const { data: newCreator, error: createError } = await serviceSupabase
        .from('creators')
        .insert({
          name: testCreatorData.name,
          platform: testCreatorData.platform,
          handle: testCreatorData.handle,
          verified: false,
          trust_score: 7.5
        })
        .select('id')
        .single()

      if (createError) {
        throw new Error('Failed to create test creator: ' + createError.message)
      }

      creatorId = newCreator.id
    }

    // Test 3: Link creator to user
    const { error: linkError } = await serviceSupabase
      .from('user_creators')
      .upsert({
        user_id: testUser.id,
        creator_id: creatorId
      })

    if (linkError) {
      throw new Error('Failed to link creator to user: ' + linkError.message)
    }

    // Test 4: Verify the link was created
    const { data: verifyLink } = await serviceSupabase
      .from('user_creators')
      .select(`
        creator_id,
        created_at,
        creators (
          platform,
          handle,
          name,
          trust_score
        )
      `)
      .eq('user_id', testUser.id)
      .eq('creator_id', creatorId)
      .single()

    // Test 5: Get updated creators list
    const { data: updatedCreators } = await serviceSupabase
      .from('user_creators')
      .select(`
        creator_id,
        created_at,
        creators (
          platform,
          handle,
          name,
          verified,
          trust_score
        )
      `)
      .eq('user_id', testUser.id)

    // Test 6: Remove the test creator (cleanup)
    const { error: removeError } = await serviceSupabase
      .from('user_creators')
      .delete()
      .eq('user_id', testUser.id)
      .eq('creator_id', creatorId)

    if (removeError) {
      console.warn('Failed to cleanup test creator:', removeError.message)
    }

    return NextResponse.json({
      success: true,
      message: 'Creator management API test completed successfully',
      testResults: {
        testUser: {
          id: testUser.id,
          username: testUser.username
        },
        initialCreatorCount: currentCreators?.length || 0,
        testCreator: {
          id: creatorId,
          platform: testCreatorData.platform,
          handle: testCreatorData.handle,
          name: testCreatorData.name
        },
        linkCreated: !!verifyLink,
        linkDetails: verifyLink ? {
          followed_at: verifyLink.created_at,
          creator: verifyLink.creators
        } : null,
        finalCreatorCount: updatedCreators?.length || 0,
        cleanupSuccessful: !removeError
      },
      apiEndpoints: {
        get: 'GET /api/user/creators - Fetch user\'s followed creators',
        post: 'POST /api/user/creators - Follow a new creator',
        delete: 'DELETE /api/user/creators?creator_id=X - Unfollow a creator'
      },
      summary: {
        creatorCreation: '✅ Can create new creators',
        userLinking: '✅ Can link creators to users',
        dataRetrieval: '✅ Can fetch user\'s creators',
        cleanup: removeError ? '⚠️ Cleanup had issues' : '✅ Cleanup successful'
      }
    })

  } catch (error) {
    console.error('Creator management test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      note: 'This test requires a user to exist in the database'
    }, { status: 500 })
  }
}

import { createServiceRoleClient } from '@/lib/supabase-server'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const serviceSupabase = createServiceRoleClient()
    
    console.log('🎯 Testing Enhanced Feed Algorithm...')
    
    // Get sample user preferences (simulated)
    const mockUserPrefs = {
      genres: ['action', 'sci-fi', 'horror'],
      streaming_services: ['netflix', 'disney']
    }
    
    // Get all content with AI processing
    const { data: content, error: contentError } = await serviceSupabase
      .from('content')
      .select(`
        id,
        title,
        description,
        content_type,
        platform_url,
        thumbnail_url,
        published_at,
        ai_summary,
        referenced_titles,
        tags,
        creators (
          id,
          name,
          platform,
          handle,
          avatar_url,
          trust_score,
          verified
        )
      `)
      .order('published_at', { ascending: false })
      .limit(10)
    
    if (contentError || !content) {
      return NextResponse.json({ error: 'Failed to fetch content' }, { status: 500 })
    }

    console.log(`Found ${content.length} content items to score`)
    
    // Calculate relevance scores for each item
    const scoredContent = content.map(item => {
      const relevanceScore = calculateRelevanceScore(item, mockUserPrefs, 'test-user')
      return {
        ...item,
        relevanceScore,
        scoringDetails: getScoreBreakdown(item, mockUserPrefs)
      }
    })
    
    // Sort by relevance score
    const rankedContent = scoredContent.sort((a, b) => b.relevanceScore - a.relevanceScore)
    
    // Prepare response with detailed scoring info
    const results = rankedContent.map((item, index) => ({
      rank: index + 1,
      title: item.title,
      creator: item.creators?.name,
      contentType: item.content_type,
      relevanceScore: item.relevanceScore,
      hasAiSummary: !!item.ai_summary,
      referencedTitles: item.referenced_titles || [],
      tags: item.tags || [],
      publishedDaysAgo: Math.round((Date.now() - new Date(item.published_at).getTime()) / (1000 * 60 * 60 * 24)),
      scoringDetails: item.scoringDetails
    }))
    
    // Calculate algorithm performance metrics
    const metrics = {
      totalItems: content.length,
      aiProcessedItems: content.filter(item => item.ai_summary).length,
      averageScore: Math.round(rankedContent.reduce((sum, item) => sum + item.relevanceScore, 0) / rankedContent.length),
      scoreRange: {
        highest: rankedContent[0]?.relevanceScore || 0,
        lowest: rankedContent[rankedContent.length - 1]?.relevanceScore || 0
      },
      contentTypeDistribution: rankedContent.reduce((acc: any, item) => {
        acc[item.content_type] = (acc[item.content_type] || 0) + 1
        return acc
      }, {}),
      userPreferences: mockUserPrefs
    }

    return NextResponse.json({
      success: true,
      message: 'Enhanced feed algorithm test completed',
      metrics,
      rankedContent: results.slice(0, 5), // Top 5 results
      fullResults: results // All results for analysis
    })

  } catch (error) {
    console.error('Enhanced feed test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

function calculateRelevanceScore(content: any, userPrefs: any, userId: string): number {
  let score = 0

  // Base score from creator trust score
  if (content.creators?.trust_score) {
    score += content.creators.trust_score * 10
  }

  // Boost for verified creators
  if (content.creators?.verified) {
    score += 20
  }

  // Recency boost
  const publishedDate = new Date(content.published_at)
  const daysSincePublished = (Date.now() - publishedDate.getTime()) / (1000 * 60 * 60 * 24)
  const recencyScore = Math.max(0, 30 - daysSincePublished)
  score += recencyScore

  // AI-Enhanced Content Type Scoring
  const aiContentTypeScores: { [key: string]: number } = {
    'review': 12,
    'recommendation': 18,
    'theory': 10,
    'news': 8,
    'breakdown': 15,
    'spoiler-free': 14
  }
  
  if (content.content_type && aiContentTypeScores[content.content_type]) {
    score += aiContentTypeScores[content.content_type]
  }

  // AI Summary Quality Boost
  if (content.ai_summary) {
    const summaryLength = content.ai_summary.length
    let summaryBoost = 15
    if (summaryLength > 100) summaryBoost += 5
    if (summaryLength > 200) summaryBoost += 5
    score += summaryBoost
  }

  // Referenced Titles Boost
  if (content.referenced_titles && content.referenced_titles.length > 0) {
    score += Math.min(content.referenced_titles.length * 8, 40)
  }

  // AI Tags Matching
  if (userPrefs?.genres && content.tags && content.tags.length > 0) {
    const directMatches = userPrefs.genres.filter((genre: string) => 
      content.tags.some((tag: string) => tag.toLowerCase().includes(genre.toLowerCase()))
    )
    score += directMatches.length * 20
  }

  // Tag Quality Boost
  if (content.tags && content.tags.length > 0) {
    score += Math.min(content.tags.length * 2, 12)
  }

  // Penalty for unprocessed content
  if (!content.ai_summary && !content.referenced_titles) {
    score -= 15
  }

  return Math.max(0, Math.round(score))
}

function getScoreBreakdown(content: any, userPrefs: any): any {
  const breakdown: any = {}
  
  if (content.creators?.trust_score) {
    breakdown.creatorTrust = content.creators.trust_score * 10
  }
  
  if (content.creators?.verified) {
    breakdown.verified = 20
  }
  
  const daysSincePublished = (Date.now() - new Date(content.published_at).getTime()) / (1000 * 60 * 60 * 24)
  breakdown.recency = Math.max(0, 30 - daysSincePublished)
  
  if (content.ai_summary) {
    breakdown.aiSummary = 15 + (content.ai_summary.length > 100 ? 5 : 0) + (content.ai_summary.length > 200 ? 5 : 0)
  }
  
  if (content.referenced_titles?.length > 0) {
    breakdown.referencedTitles = Math.min(content.referenced_titles.length * 8, 40)
  }
  
  if (content.tags?.length > 0) {
    breakdown.tagQuality = Math.min(content.tags.length * 2, 12)
  }
  
  return breakdown
}

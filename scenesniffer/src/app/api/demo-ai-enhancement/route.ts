import { createServiceRoleClient } from '@/lib/supabase-server'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const serviceSupabase = createServiceRoleClient()
    
    console.log('🤖 Demo: AI Enhancement of Existing Content...')
    
    // Get existing content that doesn't have AI summaries
    const { data: existingContent, error: contentError } = await serviceSupabase
      .from('content')
      .select(`
        id,
        title,
        description,
        content_type,
        ai_summary,
        referenced_titles,
        tags,
        creators (name, handle)
      `)
      .is('ai_summary', null)
      .limit(3)
    
    if (contentError || !existingContent || existingContent.length === 0) {
      return NextResponse.json({ 
        success: true,
        message: 'No content found that needs AI enhancement',
        note: 'All content may already be AI-processed'
      })
    }

    console.log(`Found ${existingContent.length} content items to enhance with AI`)
    
    const results: any[] = []
    
    for (const content of existingContent) {
      try {
        console.log(`🤖 AI Processing: ${content.title}`)
        
        // AI Processing
        let aiSummary = ''
        let referencedTitles: string[] = []
        let aiContentType = ''
        let aiTags: string[] = []

        const OpenAI = require('openai')
        const client = new OpenAI({
          apiKey: '********************************************************************************************************************************************************************'
        })

        // Categorize content type
        const categoryResponse = await client.chat.completions.create({
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: 'You are an expert at categorizing movie and TV content. Return only the category name.'
            },
            {
              role: 'user',
              content: `Categorize this content: review, theory, news, spoiler-free, breakdown, or recommendation.\n\nTitle: ${content.title}\nDescription: ${content.description}\n\nReturn only the category name (lowercase).`
            }
          ],
          max_tokens: 10,
          temperature: 0.1
        })
        aiContentType = categoryResponse.choices[0]?.message?.content?.trim().toLowerCase() || 'review'

        // Generate summary
        const summaryResponse = await client.chat.completions.create({
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: 'You are an expert at summarizing movie and TV content. Provide concise, engaging summaries.'
            },
            {
              role: 'user',
              content: `Summarize this ${aiContentType} content in 2-3 sentences.\n\nTitle: ${content.title}\nDescription: ${content.description}\n\nFocus on the main points and what makes this content valuable to viewers.`
            }
          ],
          max_tokens: 200,
          temperature: 0.7
        })
        aiSummary = summaryResponse.choices[0]?.message?.content?.trim() || 'Summary not available'

        // Extract referenced titles using AI
        const titleResponse = await client.chat.completions.create({
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: 'Extract movie and TV show titles mentioned in content. Return as JSON array.'
            },
            {
              role: 'user',
              content: `Extract all movie and TV show titles mentioned in this content:\n\nTitle: ${content.title}\nDescription: ${content.description}\n\nReturn format: ["Title 1", "Title 2"]`
            }
          ],
          max_tokens: 100,
          temperature: 0.3
        })
        
        try {
          const titleContent = titleResponse.choices[0]?.message?.content?.trim()
          referencedTitles = titleContent ? JSON.parse(titleContent) : []
        } catch {
          referencedTitles = []
        }

        // Generate AI tags
        const tagResponse = await client.chat.completions.create({
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: 'Generate relevant tags for movie/TV content. Return as JSON array.'
            },
            {
              role: 'user',
              content: `Generate 3-6 relevant tags for this content:\n\nTitle: ${content.title}\nDescription: ${content.description}\n\nFocus on genres, themes, and content attributes. Return format: ["tag1", "tag2", "tag3"]`
            }
          ],
          max_tokens: 80,
          temperature: 0.5
        })
        
        try {
          const tagContent = tagResponse.choices[0]?.message?.content?.trim()
          aiTags = tagContent ? JSON.parse(tagContent) : ['general']
        } catch {
          aiTags = ['general']
        }

        // Update the content with AI enhancements
        const { error: updateError } = await serviceSupabase
          .from('content')
          .update({
            ai_summary: aiSummary,
            referenced_titles: referencedTitles,
            tags: aiTags,
            content_type: aiContentType // Update with AI-determined type
          })
          .eq('id', content.id)

        results.push({
          id: content.id,
          title: content.title,
          creator: content.creators?.name,
          originalType: content.content_type,
          originalTags: content.tags,
          aiEnhancements: {
            contentType: aiContentType,
            summary: aiSummary,
            referencedTitles,
            tags: aiTags,
            updated: !updateError
          },
          updateError: updateError?.message
        })

        console.log(`✅ Enhanced: ${content.title}`)
        console.log(`   Type: ${content.content_type} → ${aiContentType}`)
        console.log(`   Summary: ${aiSummary.substring(0, 50)}...`)
        console.log(`   Referenced: ${referencedTitles.join(', ')}`)
        console.log(`   Tags: ${aiTags.join(', ')}`)

      } catch (error) {
        console.error(`❌ Failed to enhance ${content.title}:`, error)
        results.push({
          id: content.id,
          title: content.title,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return NextResponse.json({
      success: true,
      message: 'AI enhancement demo completed',
      contentProcessed: existingContent.length,
      results
    })

  } catch (error) {
    console.error('AI enhancement demo error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

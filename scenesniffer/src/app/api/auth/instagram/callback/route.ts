import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase-server'
import { instagramAPI } from '@/lib/instagram-api'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const code = searchParams.get('code')
    const state = searchParams.get('state')
    const error = searchParams.get('error')

    if (error) {
      console.error('Instagram auth error:', error)
      return NextResponse.redirect(new URL('/feed?error=instagram_auth_failed', request.url))
    }

    if (!code) {
      return NextResponse.redirect(new URL('/feed?error=no_auth_code', request.url))
    }

    const supabase = await createServerSupabaseClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.redirect(new URL('/auth?error=not_authenticated', request.url))
    }

    try {
      // Exchange code for access token
      const tokenData = await instagramAPI.getAccessToken(code)
      
      // Get long-lived token
      const longLivedToken = await instagramAPI.getLongLivedToken(tokenData.access_token)
      
      // Store Instagram credentials for the user
      const { error: updateError } = await supabase
        .from('user_preferences')
        .upsert({
          user_id: user.id,
          instagram_access_token: longLivedToken.access_token,
          instagram_user_id: tokenData.user_id,
          instagram_token_expires_at: new Date(Date.now() + longLivedToken.expires_in * 1000).toISOString(),
          updated_at: new Date().toISOString()
        })

      if (updateError) {
        console.error('Error storing Instagram credentials:', updateError)
        return NextResponse.redirect(new URL('/feed?error=storage_failed', request.url))
      }

      // Redirect back to feed with success
      return NextResponse.redirect(new URL('/feed?instagram_connected=true', request.url))

    } catch (apiError) {
      console.error('Instagram API error:', apiError)
      return NextResponse.redirect(new URL('/feed?error=instagram_api_failed', request.url))
    }

  } catch (error) {
    console.error('Instagram callback error:', error)
    return NextResponse.redirect(new URL('/feed?error=callback_failed', request.url))
  }
}

// Handle Instagram deauthorization
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const userId = body.user_id

    if (!userId) {
      return NextResponse.json({ error: 'Missing user_id' }, { status: 400 })
    }

    const supabase = await createServerSupabaseClient()

    // Remove Instagram credentials
    const { error } = await supabase
      .from('user_preferences')
      .update({
        instagram_access_token: null,
        instagram_user_id: null,
        instagram_token_expires_at: null,
        updated_at: new Date().toISOString()
      })
      .eq('instagram_user_id', userId)

    if (error) {
      console.error('Error removing Instagram credentials:', error)
      return NextResponse.json({ error: 'Failed to deauthorize' }, { status: 500 })
    }

    return NextResponse.json({ success: true })

  } catch (error) {
    console.error('Instagram deauth error:', error)
    return NextResponse.json({ error: 'Deauthorization failed' }, { status: 500 })
  }
}

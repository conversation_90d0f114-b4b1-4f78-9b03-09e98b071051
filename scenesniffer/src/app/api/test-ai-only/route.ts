import { createOpenAIService } from '@/lib/openai'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('🤖 Testing AI Processing Only...')
    
    // Test data - sample video content
    const testVideos = [
      {
        title: "The Batman (2022) - Movie Review",
        description: "My thoughts on <PERSON>' The Batman starring <PERSON>. This dark and gritty take on the Dark Knight brings a noir detective story to Gotham City. Spoiler-free review covering the cinematography, performances, and overall direction."
      },
      {
        title: "Marvel's Secret Wars Theory - Everything We Know",
        description: "Breaking down all the clues and hints about <PERSON>'s upcoming Secret Wars event. From the multiverse setup in <PERSON> Strange to the X-Men introduction, here's how it could all connect. This theory video explores the comic book origins and potential MCU adaptation."
      },
      {
        title: "Netflix December 2024 - What's New This Month",
        description: "All the new movies and TV shows coming to Netflix this December. From holiday specials to action blockbusters, here's your complete guide to what's worth watching this month on the streaming platform."
      }
    ]
    
    const openaiService = createOpenAIService()
    const results: any[] = []
    
    for (const video of testVideos) {
      try {
        console.log(`🤖 Processing: ${video.title}`)
        const startTime = Date.now()
        
        // Test each AI function individually
        let aiContentType = 'review'
        let aiSummary = 'Summary not available'
        let referencedTitles: string[] = []
        let aiTags: string[] = []
        
        try {
          aiContentType = await openaiService.categorizeContent(video.title, video.description)
          console.log(`✅ Content type: ${aiContentType}`)
        } catch (error) {
          console.error('❌ Content categorization failed:', error)
        }
        
        try {
          aiSummary = await openaiService.summarizeContent(video.title, video.description, aiContentType)
          console.log(`✅ Summary: ${aiSummary.substring(0, 50)}...`)
        } catch (error) {
          console.error('❌ Summary generation failed:', error)
        }
        
        try {
          referencedTitles = await openaiService.extractTitles(video.title, video.description)
          console.log(`✅ Titles: ${referencedTitles.join(', ')}`)
        } catch (error) {
          console.error('❌ Title extraction failed:', error)
        }
        
        try {
          aiTags = await openaiService.generateTags(video.title, video.description, referencedTitles)
          console.log(`✅ Tags: ${aiTags.join(', ')}`)
        } catch (error) {
          console.error('❌ Tag generation failed:', error)
        }
        
        const processingTime = Date.now() - startTime
        
        results.push({
          title: video.title,
          originalDescription: video.description.substring(0, 100) + '...',
          aiProcessing: {
            contentType: aiContentType,
            summary: aiSummary,
            referencedTitles,
            tags: aiTags,
            processingTimeMs: processingTime
          }
        })
        
        console.log(`✅ Completed "${video.title}" in ${processingTime}ms`)
        
      } catch (error) {
        console.error(`❌ Failed to process ${video.title}:`, error)
        results.push({
          title: video.title,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }
    
    return NextResponse.json({
      success: true,
      message: 'AI processing test completed',
      videosProcessed: testVideos.length,
      results
    })

  } catch (error) {
    console.error('AI test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

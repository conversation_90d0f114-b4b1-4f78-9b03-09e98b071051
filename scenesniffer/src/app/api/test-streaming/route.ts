import { createServiceRoleClient } from '@/lib/supabase-server'
import { streamingService } from '@/lib/streaming-availability'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    console.log('🎬 Testing Streaming Availability Integration...')
    
    const serviceSupabase = createServiceRoleClient()
    
    // Get content with AI-referenced titles
    const { data: content, error: contentError } = await serviceSupabase
      .from('content')
      .select(`
        id,
        title,
        referenced_titles,
        ai_summary,
        creators (name)
      `)
      .not('referenced_titles', 'is', null)
      .limit(3)
    
    if (contentError || !content || content.length === 0) {
      return NextResponse.json({ 
        success: true,
        message: 'No content with referenced titles found for testing',
        note: 'Run AI enhancement first to get referenced titles'
      })
    }

    console.log(`Found ${content.length} content items with referenced titles`)
    
    // Mock user streaming services for testing
    const mockUserServices = ['netflix', 'disney', 'hbo']
    
    const results = []
    
    for (const item of content) {
      try {
        console.log(`\n🎬 Testing: ${item.title}`)
        console.log(`Referenced titles: ${item.referenced_titles?.join(', ')}`)
        
        // Test streaming lookup
        const streamingData = await streamingService.getStreamingForContent(
          item.referenced_titles || [],
          mockUserServices
        )
        
        console.log(`Found streaming for ${streamingData.length} titles`)
        
        results.push({
          contentTitle: item.title,
          creator: item.creators?.name,
          referencedTitles: item.referenced_titles,
          streamingFound: streamingData.length,
          streamingDetails: streamingData.map(streaming => ({
            title: streaming.title,
            type: streaming.content_type,
            tmdbId: streaming.tmdb_id,
            availableOn: {
              subscription: streaming.providers.flatrate?.map(p => p.provider_name) || [],
              rental: streaming.providers.rent?.map(p => p.provider_name) || [],
              purchase: streaming.providers.buy?.map(p => p.provider_name) || []
            }
          }))
        })
        
      } catch (error) {
        console.error(`Error testing ${item.title}:`, error)
        results.push({
          contentTitle: item.title,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    // Test individual title lookup
    console.log('\n🎬 Testing individual title lookups...')
    const testTitles = ['The Batman', 'Dune', 'Stranger Things']
    const individualTests = []
    
    for (const title of testTitles) {
      try {
        const streaming = await streamingService.getStreamingForContent([title], mockUserServices)
        individualTests.push({
          title,
          found: streaming.length > 0,
          details: streaming[0] || null
        })
      } catch (error) {
        individualTests.push({
          title,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Streaming availability test completed',
      mockUserServices,
      contentTests: {
        processed: content.length,
        results
      },
      individualTests,
      summary: {
        totalContentTested: content.length,
        contentWithStreaming: results.filter(r => r.streamingFound > 0).length,
        totalTitlesFound: results.reduce((sum, r) => sum + (r.streamingFound || 0), 0),
        individualTestsSuccessful: individualTests.filter(t => t.found).length
      }
    })

  } catch (error) {
    console.error('Streaming test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

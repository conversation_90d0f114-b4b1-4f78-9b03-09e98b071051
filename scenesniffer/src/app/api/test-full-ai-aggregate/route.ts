import { createServiceRoleClient } from '@/lib/supabase-server'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const serviceSupabase = createServiceRoleClient()
    
    console.log('🤖 Testing Full AI-Enhanced Content Aggregation...')
    
    // Get one YouTube creator for testing
    const { data: creators, error: creatorsError } = await serviceSupabase
      .from('creators')
      .select('*')
      .eq('platform', 'youtube')
      .limit(1)
    
    if (creatorsError || !creators || creators.length === 0) {
      return NextResponse.json({ error: 'No YouTube creators found' }, { status: 500 })
    }

    const creator = creators[0]
    console.log(`Testing with creator: ${creator.name} (${creator.handle})`)
    
    // Get channel ID
    let actualChannelId: string | null = null
    
    if (creator.handle.startsWith('@')) {
      const handle = creator.handle.substring(1)
      const channelInfoUrl = `https://www.googleapis.com/youtube/v3/channels?` +
        `part=snippet&` +
        `forHandle=${handle}&` +
        `key=${process.env.YOUTUBE_API_KEY}`
      
      const channelInfoResponse = await fetch(channelInfoUrl)
      if (channelInfoResponse.ok) {
        const channelInfoData = await channelInfoResponse.json()
        if (channelInfoData.items && channelInfoData.items.length > 0) {
          actualChannelId = channelInfoData.items[0].id
        }
      }
    }
    
    if (!actualChannelId) {
      return NextResponse.json({ error: 'Could not resolve channel ID' }, { status: 500 })
    }
    
    // Get recent videos (expand time range to get content)
    const videoSearchUrl = `https://www.googleapis.com/youtube/v3/search?` +
      `part=snippet&` +
      `channelId=${actualChannelId}&` +
      `order=date&` +
      `type=video&` +
      `maxResults=2&` +
      `publishedAfter=${new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString()}&` + // 60 days
      `key=${process.env.YOUTUBE_API_KEY}`
    
    console.log(`Fetching videos for AI processing...`)
    const videoResponse = await fetch(videoSearchUrl)
    
    if (!videoResponse.ok) {
      return NextResponse.json({ error: 'Failed to fetch videos' }, { status: 500 })
    }
    
    const videoData = await videoResponse.json()
    console.log(`Found ${videoData.items?.length || 0} videos`)
    
    if (!videoData.items || videoData.items.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No videos found for processing',
        creator: creator.name
      })
    }
    
    // Process one video with full AI pipeline
    const video = videoData.items[0].snippet
    const videoId = videoData.items[0].id.videoId
    
    console.log(`🤖 Processing: ${video.title}`)
    
    // Check if content already exists
    const { data: existingContent } = await serviceSupabase
      .from('content')
      .select('id')
      .eq('creator_id', creator.id)
      .eq('platform_id', videoId)
      .single()

    let isNewContent = !existingContent
    
    // AI Processing
    let aiSummary = ''
    let referencedTitles: string[] = []
    let aiContentType = ''
    let aiTags: string[] = []

    try {
      const OpenAI = require('openai')
      const client = new OpenAI({
        apiKey: '********************************************************************************************************************************************************************'
      })

      // Categorize content type
      const categoryResponse = await client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert at categorizing movie and TV content. Return only the category name.'
          },
          {
            role: 'user',
            content: `Categorize this content: review, theory, news, spoiler-free, breakdown, or recommendation.\n\nTitle: ${video.title}\nDescription: ${video.description}\n\nReturn only the category name (lowercase).`
          }
        ],
        max_tokens: 10,
        temperature: 0.1
      })
      aiContentType = categoryResponse.choices[0]?.message?.content?.trim().toLowerCase() || 'review'

      // Generate summary
      const summaryResponse = await client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert at summarizing movie and TV content. Provide concise, engaging summaries.'
          },
          {
            role: 'user',
            content: `Summarize this ${aiContentType} content in 2-3 sentences.\n\nTitle: ${video.title}\nDescription: ${video.description}\n\nFocus on the main points and what makes this content valuable to viewers.`
          }
        ],
        max_tokens: 200,
        temperature: 0.7
      })
      aiSummary = summaryResponse.choices[0]?.message?.content?.trim() || 'Summary not available'

      // Extract referenced titles (simplified)
      const titleMatches = (video.title + ' ' + video.description).match(/["']([^"']{3,50})["']/g)
      referencedTitles = titleMatches ? titleMatches.map(match => match.replace(/["']/g, '')) : []

      // Generate basic tags
      const text = `${video.title} ${video.description}`.toLowerCase()
      aiTags = []
      const keywords = ['movie', 'film', 'review', 'theory', 'news', 'spoiler', 'breakdown', 'recommendation']
      keywords.forEach(keyword => {
        if (text.includes(keyword)) {
          aiTags.push(keyword)
        }
      })

    } catch (aiError) {
      console.error(`AI processing failed:`, aiError)
      aiContentType = 'review'
      aiSummary = video.description?.substring(0, 200) + '...' || 'No summary available'
      aiTags = ['general']
    }

    // Insert or update content with AI data (for testing, we'll insert with a test suffix)
    let insertResult = null
    if (!isNewContent) {
      // For testing, create a new entry with modified platform_id
      const testPlatformId = videoId + '_ai_test'
      const { data, error: insertError } = await serviceSupabase
        .from('content')
        .insert({
          creator_id: creator.id,
          title: video.title + ' [AI Test]',
          description: video.description,
          content_type: aiContentType,
          platform_url: `https://www.youtube.com/watch?v=${videoId}`,
          platform_id: testPlatformId,
          thumbnail_url: video.thumbnails.high?.url || video.thumbnails.medium?.url || video.thumbnails.default?.url,
          published_at: video.publishedAt,
          ai_summary: aiSummary,
          referenced_titles: referencedTitles,
          tags: aiTags
        })
        .select()
        .single()
      
      insertResult = { data, error: insertError }
    }

    return NextResponse.json({
      success: true,
      message: 'Full AI processing test completed',
      creator: creator.name,
      video: {
        title: video.title,
        originalDescription: video.description?.substring(0, 100) + '...',
        isNewContent,
        aiProcessing: {
          contentType: aiContentType,
          summary: aiSummary,
          referencedTitles,
          tags: aiTags
        }
      },
      insertResult: insertResult ? {
        success: !insertResult.error,
        error: insertResult.error?.message
      } : null
    })

  } catch (error) {
    console.error('Full AI test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

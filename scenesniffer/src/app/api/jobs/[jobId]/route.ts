import { NextRequest, NextResponse } from 'next/server'
import { BackgroundJobService } from '@/lib/background-jobs'

// GET /api/jobs/[jobId] - Get job status
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ jobId: string }> }
) {
  try {
    const { jobId } = await params
    const jobService = new BackgroundJobService()

    const job = await jobService.getJob(jobId)

    if (!job) {
      return NextResponse.json({
        success: false,
        error: 'Job not found'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      job: {
        id: job.id,
        type: job.job_type,
        status: job.status,
        progress: job.progress,
        total_items: job.total_items,
        processed_items: job.processed_items,
        result: job.result,
        error_message: job.error_message,
        created_at: job.created_at,
        updated_at: job.updated_at,
        completed_at: job.completed_at
      }
    })

  } catch (error) {
    console.error('Error fetching job status:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch job status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

import { createServerSupabaseClient, createServiceRoleClient } from '@/lib/supabase-server'
import { NextRequest, NextResponse } from 'next/server'

interface YouTubeVideo {
  id: string
  title: string
  description: string
  publishedAt: string
  thumbnails: {
    default: { url: string }
    medium: { url: string }
    high: { url: string }
  }
  channelId: string
  channelTitle: string
}

interface YouTubeSearchResponse {
  items: Array<{
    id: { videoId: string }
    snippet: YouTubeVideo
  }>
}

export async function POST(request: NextRequest) {
  try {
    const serviceSupabase = createServiceRoleClient()
    
    console.log('🎬 Testing Content Aggregation (bypassing auth)...')
    
    // Get all YouTube creators from the database
    const { data: creators, error: creatorsError } = await serviceSupabase
      .from('creators')
      .select('*')
      .eq('platform', 'youtube')
      .limit(3) // Test with just 3 creators
    
    if (creatorsError) {
      console.error('Error fetching creators:', creatorsError)
      return NextResponse.json({ error: 'Failed to fetch creators' }, { status: 500 })
    }

    if (!creators || creators.length === 0) {
      return NextResponse.json({ 
        success: true, 
        message: 'No YouTube creators found in database',
        contentAdded: 0 
      })
    }

    console.log(`Found ${creators.length} YouTube creators to test`)
    
    let totalContentAdded = 0
    const errors: string[] = []
    const results: any[] = []

    // Process each YouTube creator
    for (const creator of creators) {
      try {
        console.log(`Processing creator: ${creator.name} (${creator.handle})`)
        
        // First, get the actual channel ID from the handle
        let actualChannelId: string | null = null
        
        // Try to get channel info by handle first
        if (creator.handle.startsWith('@')) {
          const handle = creator.handle.substring(1)
          const channelInfoUrl = `https://www.googleapis.com/youtube/v3/channels?` +
            `part=snippet&` +
            `forHandle=${handle}&` +
            `key=${process.env.YOUTUBE_API_KEY}`
          
          const channelInfoResponse = await fetch(channelInfoUrl)
          if (channelInfoResponse.ok) {
            const channelInfoData = await channelInfoResponse.json()
            if (channelInfoData.items && channelInfoData.items.length > 0) {
              actualChannelId = channelInfoData.items[0].id
              console.log(`Found channel ID for ${creator.handle}: ${actualChannelId}`)
            }
          } else {
            console.log(`Channel info request failed for ${creator.handle}:`, channelInfoResponse.status)
          }
        }
        
        // If we couldn't get channel ID by handle, try searching by name
        if (!actualChannelId) {
          console.log(`Searching for channel by name: ${creator.name}`)
          const channelSearchUrl = `https://www.googleapis.com/youtube/v3/search?` +
            `part=snippet&` +
            `q=${encodeURIComponent(creator.name)}&` +
            `type=channel&` +
            `maxResults=1&` +
            `key=${process.env.YOUTUBE_API_KEY}`
          
          const channelResponse = await fetch(channelSearchUrl)
          if (!channelResponse.ok) {
            errors.push(`Failed to find channel for ${creator.name}`)
            continue
          }
          
          const channelData = await channelResponse.json()
          if (!channelData.items || channelData.items.length === 0) {
            errors.push(`No channel found for ${creator.name}`)
            continue
          }
          
          actualChannelId = channelData.items[0].snippet.channelId
          console.log(`Found channel ID by search for ${creator.name}: ${actualChannelId}`)
        }
        
        if (!actualChannelId) {
          errors.push(`Could not resolve channel ID for ${creator.name}`)
          continue
        }
        
        // Now search for videos with the actual channel ID
        const videoSearchUrl = `https://www.googleapis.com/youtube/v3/search?` +
          `part=snippet&` +
          `channelId=${actualChannelId}&` +
          `order=date&` +
          `type=video&` +
          `maxResults=5&` +
          `publishedAfter=${new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()}&` +
          `key=${process.env.YOUTUBE_API_KEY}`
        
        console.log(`Fetching videos for ${creator.name} from channel ${actualChannelId}`)
        const videoResponse = await fetch(videoSearchUrl)
        
        if (!videoResponse.ok) {
          const errorText = await videoResponse.text()
          console.error(`YouTube API error for ${creator.name}:`, videoResponse.status, errorText)
          errors.push(`Failed to fetch videos for ${creator.name}: ${videoResponse.status}`)
          continue
        }
        
        const videoData: YouTubeSearchResponse = await videoResponse.json()
        console.log(`Found ${videoData.items?.length || 0} videos for ${creator.name}`)
        
        // Process and store videos
        const contentAdded = await processVideos(videoData, creator, serviceSupabase)
        console.log(`Added ${contentAdded} new videos for ${creator.name}`)
        totalContentAdded += contentAdded
        
        results.push({
          creator: creator.name,
          handle: creator.handle,
          channelId: actualChannelId,
          videosFound: videoData.items?.length || 0,
          contentAdded
        })
        
      } catch (error) {
        console.error(`Error processing creator ${creator.name}:`, error)
        errors.push(`Error processing ${creator.name}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    return NextResponse.json({
      success: true,
      message: `Test aggregation completed`,
      contentAdded: totalContentAdded,
      creatorsProcessed: creators.length,
      results,
      errors: errors.length > 0 ? errors : undefined
    })

  } catch (error) {
    console.error('Test aggregation error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

async function processVideos(
  data: YouTubeSearchResponse, 
  creator: any, 
  supabase: any
): Promise<number> {
  if (!data.items || data.items.length === 0) {
    console.log(`No videos found for ${creator.name}`)
    return 0
  }

  console.log(`Processing ${data.items.length} videos for ${creator.name}`)
  let contentAdded = 0

  for (const item of data.items) {
    const video = item.snippet
    const videoId = item.id.videoId

    try {
      console.log(`Checking video: ${video.title} (${videoId})`)
      
      // Check if content already exists
      const { data: existingContent } = await supabase
        .from('content')
        .select('id')
        .eq('creator_id', creator.id)
        .eq('platform_id', videoId)
        .single()

      if (existingContent) {
        console.log(`Video already exists: ${video.title}`)
        continue // Skip if already exists
      }

      // Determine content type based on title/description
      const contentType = classifyContent(video.title, video.description)

      console.log(`Adding new video: ${video.title} (type: ${contentType})`)
      
      // Insert new content
      const { error: insertError } = await supabase
        .from('content')
        .insert({
          creator_id: creator.id,
          title: video.title,
          description: video.description,
          content_type: contentType,
          platform_url: `https://www.youtube.com/watch?v=${videoId}`,
          platform_id: videoId,
          thumbnail_url: video.thumbnails.high?.url || video.thumbnails.medium?.url || video.thumbnails.default?.url,
          published_at: video.publishedAt,
          tags: extractTags(video.title, video.description)
        })

      if (insertError) {
        console.error('Error inserting content:', insertError)
      } else {
        console.log(`Successfully added: ${video.title}`)
        contentAdded++
      }
    } catch (error) {
      console.error('Error processing video:', error)
    }
  }

  console.log(`Added ${contentAdded} new videos for ${creator.name}`)
  return contentAdded
}

function classifyContent(title: string, description: string): string {
  const text = `${title} ${description}`.toLowerCase()
  
  if (text.includes('review') || text.includes('rating')) return 'review'
  if (text.includes('theory') || text.includes('explained')) return 'theory'
  if (text.includes('news') || text.includes('update')) return 'news'
  if (text.includes('spoiler')) return 'spoiler-free' // Will be corrected by AI later
  if (text.includes('breakdown') || text.includes('analysis')) return 'breakdown'
  if (text.includes('recommend') || text.includes('must watch')) return 'recommendation'
  
  return 'review' // Default
}

function extractTags(title: string, description: string): string[] {
  const text = `${title} ${description}`.toLowerCase()
  const tags: string[] = []
  
  // Common movie/TV related keywords
  const keywords = [
    'movie', 'film', 'cinema', 'tv show', 'series', 'episode',
    'marvel', 'dc', 'disney', 'netflix', 'hbo', 'amazon prime',
    'horror', 'comedy', 'drama', 'action', 'sci-fi', 'fantasy',
    'thriller', 'romance', 'documentary', 'animation'
  ]
  
  keywords.forEach(keyword => {
    if (text.includes(keyword)) {
      tags.push(keyword)
    }
  })
  
  return tags.slice(0, 10) // Limit to 10 tags
}

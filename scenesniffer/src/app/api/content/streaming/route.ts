import { createServiceRoleClient } from '@/lib/supabase-server'
import { streamingService } from '@/lib/streaming-availability'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const { contentIds, userStreamingServices } = await request.json()
    
    console.log('🎬 Enhancing content with streaming availability...')
    console.log(`Content IDs: ${contentIds?.join(', ')}`)
    console.log(`User services: ${userStreamingServices?.join(', ')}`)
    
    const serviceSupabase = createServiceRoleClient()
    
    // Get content with AI-referenced titles
    const { data: content, error: contentError } = await serviceSupabase
      .from('content')
      .select(`
        id,
        title,
        referenced_titles,
        streaming_availability
      `)
      .in('id', contentIds || [])
      .not('referenced_titles', 'is', null)
    
    if (contentError) {
      return NextResponse.json({ error: 'Failed to fetch content' }, { status: 500 })
    }

    if (!content || content.length === 0) {
      return NextResponse.json({ 
        success: true, 
        message: 'No content with referenced titles found',
        results: []
      })
    }

    console.log(`Found ${content.length} content items with referenced titles`)
    
    const results = []
    
    for (const item of content) {
      try {
        // Skip if already has streaming data and it's recent
        if (item.streaming_availability && Array.isArray(item.streaming_availability)) {
          console.log(`Skipping ${item.title} - already has streaming data`)
          results.push({
            contentId: item.id,
            title: item.title,
            streamingAvailability: item.streaming_availability,
            cached: true
          })
          continue
        }

        console.log(`Processing: ${item.title}`)
        console.log(`Referenced titles: ${item.referenced_titles?.join(', ')}`)
        
        // Get streaming availability for referenced titles
        const streamingData = await streamingService.getStreamingForContent(
          item.referenced_titles || [],
          userStreamingServices || []
        )
        
        // Update content with streaming data
        if (streamingData.length > 0) {
          const { error: updateError } = await serviceSupabase
            .from('content')
            .update({
              streaming_availability: streamingData
            })
            .eq('id', item.id)
          
          if (updateError) {
            console.error(`Failed to update streaming data for ${item.title}:`, updateError)
          } else {
            console.log(`✅ Updated streaming data for ${item.title}`)
          }
        }
        
        results.push({
          contentId: item.id,
          title: item.title,
          referencedTitles: item.referenced_titles,
          streamingAvailability: streamingData,
          cached: false
        })
        
      } catch (error) {
        console.error(`Error processing ${item.title}:`, error)
        results.push({
          contentId: item.id,
          title: item.title,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Streaming availability processing completed',
      processed: content.length,
      results
    })

  } catch (error) {
    console.error('Streaming availability error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET endpoint to retrieve content with streaming data
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    const userServices = searchParams.get('services')?.split(',') || []
    
    const serviceSupabase = createServiceRoleClient()
    
    // Get content with streaming availability
    const { data: content, error: contentError } = await serviceSupabase
      .from('content')
      .select(`
        id,
        title,
        description,
        content_type,
        ai_summary,
        referenced_titles,
        streaming_availability,
        published_at,
        creators (
          name,
          handle,
          verified
        )
      `)
      .not('streaming_availability', 'is', null)
      .order('published_at', { ascending: false })
      .limit(limit)
    
    if (contentError) {
      return NextResponse.json({ error: 'Failed to fetch content' }, { status: 500 })
    }

    // Filter by user's streaming services if provided
    const filteredContent = content?.map(item => {
      if (!userServices.length || !item.streaming_availability) {
        return item
      }
      
      // Filter streaming availability by user services
      const filteredStreaming = item.streaming_availability.filter((streaming: any) => {
        const hasUserService = Object.values(streaming.providers || {}).some((providers: any) =>
          providers?.some((provider: any) => {
            const providerName = provider.provider_name?.toLowerCase()
            return userServices.some(service => 
              providerName?.includes(service.toLowerCase())
            )
          })
        )
        return hasUserService
      })
      
      return {
        ...item,
        streaming_availability: filteredStreaming
      }
    }).filter(item => 
      !userServices.length || 
      (item.streaming_availability && item.streaming_availability.length > 0)
    ) || []

    return NextResponse.json({
      success: true,
      content: filteredContent,
      total: filteredContent.length
    })

  } catch (error) {
    console.error('Get streaming content error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

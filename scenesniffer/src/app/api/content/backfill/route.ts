import { NextRequest, NextResponse } from 'next/server'
import { createServiceRoleClient } from '@/lib/supabase-server'
import { YoutubeTranscript } from 'youtube-transcript'

export async function POST(request: NextRequest) {
  try {
    const supabase = createServiceRoleClient()
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '10')
    const dryRun = searchParams.get('dryRun') === 'true'
    
    console.log(`🔄 Starting content backfill (limit: ${limit}, dryRun: ${dryRun})`)

    // Find content that needs AI processing
    const { data: contentToProcess, error: fetchError } = await supabase
      .from('content')
      .select(`
        id,
        title,
        description,
        platform_id,
        ai_summary,
        tags,
        referenced_titles,
        creators (name, platform)
      `)
      .or('ai_summary.is.null,tags.is.null,referenced_titles.is.null,ai_summary.eq."",tags.eq.{},referenced_titles.eq.{}')
      .order('created_at', { ascending: false })
      .limit(limit)

    if (fetchError) {
      throw new Error(`Failed to fetch content: ${fetchError.message}`)
    }

    if (!contentToProcess || contentToProcess.length === 0) {
      return NextResponse.json({
        success: true,
        message: 'No content needs processing',
        processed: 0
      })
    }

    console.log(`Found ${contentToProcess.length} items needing AI processing`)

    const results = []
    let processed = 0
    let errors = 0

    // Initialize OpenAI
    const OpenAI = require('openai')
    const client = new OpenAI({
      apiKey: '********************************************************************************************************************************************************************'
    })

    for (const item of contentToProcess) {
      try {
        console.log(`Processing: ${item.title}`)

        // Try to get transcript if we have platform_id
        let transcript = ''
        let inputSource = 'metadata'
        
        if (item.platform_id) {
          try {
            const transcriptData = await YoutubeTranscript.fetchTranscript(item.platform_id)
            if (transcriptData && transcriptData.length > 0) {
              const fullTranscript = transcriptData.map(t => t.text).join(' ')
              transcript = fullTranscript.length > 4000 
                ? fullTranscript.substring(0, 4000) + '...'
                : fullTranscript
              inputSource = 'transcript'
            }
          } catch (transcriptError) {
            // Transcript not available, use metadata
          }
        }

        const inputContent = transcript 
          ? `Title: ${item.title}\nDescription: ${item.description}\n\nTranscript: ${transcript}`
          : `Title: ${item.title}\nDescription: ${item.description}`

        // Generate AI summary if missing
        let aiSummary = item.ai_summary
        if (!aiSummary || aiSummary.trim() === '') {
          const summaryResponse = await client.chat.completions.create({
            model: 'gpt-3.5-turbo',
            messages: [
              {
                role: 'system',
                content: 'You are an expert at summarizing movie and TV content. Provide concise, engaging summaries.'
              },
              {
                role: 'user',
                content: `Summarize this content in 2-3 sentences:\n\n${inputContent}\n\nFocus on key insights and value for viewers.`
              }
            ],
            max_tokens: 200,
            temperature: 0.7
          })
          aiSummary = summaryResponse.choices[0]?.message?.content?.trim() || 'Summary not available'
        }

        // Generate tags if missing
        let tags = item.tags
        if (!tags || tags.length === 0) {
          const tagsResponse = await client.chat.completions.create({
            model: 'gpt-3.5-turbo',
            messages: [
              {
                role: 'system',
                content: 'Generate relevant tags for movie/TV content. Return as JSON array.'
              },
              {
                role: 'user',
                content: `Generate 3-8 tags for:\n\n${inputContent}\n\nReturn JSON array like ["tag1", "tag2"]`
              }
            ],
            max_tokens: 100,
            temperature: 0.3
          })
          
          try {
            const tagsText = tagsResponse.choices[0]?.message?.content?.trim() || '[]'
            tags = JSON.parse(tagsText)
          } catch (parseError) {
            tags = ['movie', 'entertainment'] // Fallback tags
          }
        }

        // Generate referenced titles if missing
        let referencedTitles = item.referenced_titles
        if (!referencedTitles || referencedTitles.length === 0) {
          const titlesResponse = await client.chat.completions.create({
            model: 'gpt-3.5-turbo',
            messages: [
              {
                role: 'system',
                content: 'Extract movie/TV titles mentioned. Return as JSON array.'
              },
              {
                role: 'user',
                content: `Extract titles from:\n\n${inputContent}\n\nReturn JSON array like ["Title 1", "Title 2"]`
              }
            ],
            max_tokens: 150,
            temperature: 0.1
          })
          
          try {
            const titlesText = titlesResponse.choices[0]?.message?.content?.trim() || '[]'
            referencedTitles = JSON.parse(titlesText)
          } catch (parseError) {
            referencedTitles = []
          }
        }

        const updateData = {
          ai_summary: aiSummary,
          tags: tags,
          referenced_titles: referencedTitles
        }

        results.push({
          id: item.id,
          title: item.title,
          inputSource,
          updateData,
          success: true
        })

        // Update database if not dry run
        if (!dryRun) {
          const { error: updateError } = await supabase
            .from('content')
            .update(updateData)
            .eq('id', item.id)

          if (updateError) {
            throw new Error(`Update failed: ${updateError.message}`)
          }
        }

        processed++
        console.log(`✅ Processed: ${item.title} (${inputSource})`)

        // Rate limiting - wait between requests
        await new Promise(resolve => setTimeout(resolve, 1000))

      } catch (itemError) {
        console.error(`❌ Error processing ${item.title}:`, itemError)
        errors++
        results.push({
          id: item.id,
          title: item.title,
          error: itemError.message,
          success: false
        })
      }
    }

    return NextResponse.json({
      success: true,
      message: `Backfill ${dryRun ? 'simulation' : 'completed'}`,
      processed,
      errors,
      total: contentToProcess.length,
      results: results.slice(0, 5), // Show first 5 results
      dryRun
    })

  } catch (error) {
    console.error('Backfill error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

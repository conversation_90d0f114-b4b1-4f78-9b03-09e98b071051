import { createServerSupabaseClient, createServiceRoleClient } from '@/lib/supabase-server'
import { createOpenAIService } from '@/lib/openai'
import { NextRequest, NextResponse } from 'next/server'
import { YoutubeTranscript } from 'youtube-transcript'
import { instagramAPI } from '@/lib/instagram-api'
import { twitterAPI } from '@/lib/twitter-api'
import { multiPlatformAggregator } from '@/lib/multiplatform-aggregator'

// Utility functions for reliability
async function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> {
  let lastError: Error

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error

      if (attempt === maxRetries) {
        throw lastError
      }

      const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000
      console.log(`Attempt ${attempt + 1} failed, retrying in ${Math.round(delay)}ms...`)
      await sleep(delay)
    }
  }

  throw lastError!
}

// Rate limiting helper
class RateLimiter {
  private lastCall: number = 0
  private minInterval: number

  constructor(callsPerSecond: number) {
    this.minInterval = 1000 / callsPerSecond
  }

  async waitIfNeeded(): Promise<void> {
    const now = Date.now()
    const timeSinceLastCall = now - this.lastCall

    if (timeSinceLastCall < this.minInterval) {
      const waitTime = this.minInterval - timeSinceLastCall
      await sleep(waitTime)
    }

    this.lastCall = Date.now()
  }
}

// Create rate limiters for different APIs
const youtubeRateLimiter = new RateLimiter(10) // 10 calls per second
const openaiRateLimiter = new RateLimiter(3)   // 3 calls per second

interface YouTubeVideo {
  id: string
  title: string
  description: string
  publishedAt: string
  thumbnails: {
    default: { url: string }
    medium: { url: string }
    high: { url: string }
  }
  channelId: string
  channelTitle: string
}

interface YouTubeSearchResponse {
  items: Array<{
    id: { videoId: string }
    snippet: YouTubeVideo
  }>
}

// Content deduplication helper
async function checkContentExists(supabase: any, platformId: string, creatorId: string): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('content')
      .select('id')
      .eq('platform_id', platformId)
      .eq('creator_id', creatorId)
      .single()

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      console.error('Error checking content existence:', error)
      return false
    }

    return !!data
  } catch (error) {
    console.error('Error in checkContentExists:', error)
    return false
  }
}

// Improved YouTube API call with retry logic
async function fetchYouTubeData(url: string, description: string): Promise<any> {
  return retryWithBackoff(async () => {
    await youtubeRateLimiter.waitIfNeeded()

    const response = await fetch(url)

    if (!response.ok) {
      if (response.status === 429) {
        throw new Error(`YouTube API rate limit exceeded for ${description}`)
      } else if (response.status >= 500) {
        throw new Error(`YouTube API server error (${response.status}) for ${description}`)
      } else {
        throw new Error(`YouTube API error (${response.status}) for ${description}`)
      }
    }

    return response.json()
  }, 3, 2000) // 3 retries, starting with 2 second delay
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    const serviceSupabase = createServiceRoleClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's followed creators (YouTube only for now)
    const { data: userCreators, error: creatorsError } = await supabase
      .from('user_creators')
      .select(`
        creator_id,
        creators (
          id,
          name,
          platform,
          handle,
          trust_score
        )
      `)
      .eq('user_id', user.id)

    if (creatorsError) {
      console.error('Error fetching user creators:', creatorsError)
      return NextResponse.json({ error: 'Failed to fetch creators' }, { status: 500 })
    }

    if (!userCreators || userCreators.length === 0) {
      return NextResponse.json({ 
        success: true, 
        message: 'No creators to aggregate content from',
        contentAdded: 0 
      })
    }

    const youtubeCreators = userCreators.filter(uc => 
      uc.creators?.platform === 'youtube'
    )

    if (youtubeCreators.length === 0) {
      return NextResponse.json({ 
        success: true, 
        message: 'No YouTube creators to aggregate from',
        contentAdded: 0 
      })
    }

    let totalContentAdded = 0
    const errors: string[] = []
    const processedCreators: string[] = []

    // Process each YouTube creator with improved error handling
    for (const userCreator of youtubeCreators) {
      const creator = userCreator.creators
      if (!creator) continue

      try {
        console.log(`🎬 Processing creator: ${creator.name} (${creator.handle})`)

        // First, get the actual channel ID from the handle
        let actualChannelId: string | null = null

        // Try to get channel info by handle first
        if (creator.handle.startsWith('@')) {
          const handle = creator.handle.substring(1)
          const channelInfoUrl = `https://www.googleapis.com/youtube/v3/channels?` +
            `part=snippet&` +
            `forHandle=${handle}&` +
            `key=${process.env.YOUTUBE_API_KEY}`

          try {
            const channelInfoData = await fetchYouTubeData(channelInfoUrl, `channel info for ${creator.handle}`)
            if (channelInfoData.items && channelInfoData.items.length > 0) {
              actualChannelId = channelInfoData.items[0].id
              console.log(`✅ Found channel ID for ${creator.handle}: ${actualChannelId}`)
            }
          } catch (error) {
            console.warn(`⚠️ Failed to get channel info by handle for ${creator.handle}:`, error)
          }
        }

        // If we couldn't get channel ID by handle, try searching by name
        if (!actualChannelId) {
          console.log(`🔍 Searching for channel by name: ${creator.name}`)
          const channelSearchUrl = `https://www.googleapis.com/youtube/v3/search?` +
            `part=snippet&` +
            `q=${encodeURIComponent(creator.name)}&` +
            `type=channel&` +
            `maxResults=1&` +
            `key=${process.env.YOUTUBE_API_KEY}`

          try {
            const channelSearchData = await fetchYouTubeData(channelSearchUrl, `channel search for ${creator.name}`)
            if (channelSearchData.items && channelSearchData.items.length > 0) {
              actualChannelId = channelSearchData.items[0].id.channelId
              console.log(`✅ Found channel ID by search for ${creator.name}: ${actualChannelId}`)
            }
          } catch (error) {
            console.error(`❌ Failed to search for channel ${creator.name}:`, error)
            errors.push(`Failed to find channel for ${creator.name}: ${error}`)
            continue
          }
        }

        if (!actualChannelId) {
          errors.push(`Could not resolve channel ID for ${creator.name}`)
          continue
        }

        // Now search for videos with the actual channel ID using improved error handling
        const videoSearchUrl = `https://www.googleapis.com/youtube/v3/search?` +
          `part=snippet&` +
          `channelId=${actualChannelId}&` +
          `order=date&` +
          `type=video&` +
          `maxResults=10&` +
          `publishedAfter=${new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()}&` +
          `key=${process.env.YOUTUBE_API_KEY}`

        console.log(`📹 Fetching videos for ${creator.name} from channel ${actualChannelId}`)

        try {
          const videoData: YouTubeSearchResponse = await fetchYouTubeData(videoSearchUrl, `videos for ${creator.name}`)
          console.log(`📊 Found ${videoData.items?.length || 0} videos for ${creator.name}`)

          // Process and store videos with deduplication
          const contentAdded = await processVideos(videoData, creator, serviceSupabase)
          console.log(`✅ Added ${contentAdded} new videos for ${creator.name}`)
          totalContentAdded += contentAdded
          processedCreators.push(creator.name)
        } catch (error) {
          console.error(`❌ Failed to fetch videos for ${creator.name}:`, error)
          errors.push(`Failed to fetch videos for ${creator.name}: ${error}`)
        }
      } catch (error) {
        console.error(`Error processing creator ${creator.name}:`, error)
        errors.push(`Error processing ${creator.name}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    return NextResponse.json({
      success: true,
      message: `Content aggregation completed`,
      contentAdded: totalContentAdded,
      creatorsProcessed: processedCreators.length,
      totalCreators: youtubeCreators.length,
      processedCreators,
      errors: errors.length > 0 ? errors : undefined,
      summary: {
        successful: processedCreators.length,
        failed: errors.length,
        contentAdded: totalContentAdded
      }
    })

  } catch (error) {
    console.error('Content aggregation error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

async function processVideos(
  data: YouTubeSearchResponse,
  creator: any,
  supabase: any
): Promise<number> {
  if (!data.items || data.items.length === 0) {
    console.log(`No videos found for ${creator.name}`)
    return 0
  }

  console.log(`Processing ${data.items.length} videos for ${creator.name}`)
  let contentAdded = 0

  // Initialize OpenAI service for AI processing
  const openaiService = createOpenAIService()

  for (const item of data.items) {
    const video = item.snippet
    const videoId = item.id.videoId

    try {
      console.log(`🔍 Checking video: ${video.title} (${videoId})`)

      // Check if content already exists using improved deduplication
      const contentExists = await checkContentExists(supabase, videoId, creator.id)
      if (contentExists) {
        console.log(`⏭️ Video already exists: ${video.title}`)
        continue // Skip if already exists
      }

      console.log(`🆕 Processing new video with AI: ${video.title}`)

      // Step 1: Try to fetch YouTube transcript for better input quality
      let transcript = ''
      let inputSource = 'metadata' // Track what we're using as input

      try {
        console.log(`Attempting to fetch transcript for video: ${videoId}`)
        const transcriptData = await YoutubeTranscript.fetchTranscript(videoId)

        if (transcriptData && transcriptData.length > 0) {
          // Combine transcript segments into full text
          const fullTranscript = transcriptData.map(item => item.text).join(' ')

          // Truncate if too long (keep first 4000 chars for better context)
          transcript = fullTranscript.length > 4000
            ? fullTranscript.substring(0, 4000) + '...'
            : fullTranscript

          inputSource = 'transcript'
          console.log(`✅ Transcript fetched successfully (${transcript.length} chars)`)
        }
      } catch (transcriptError) {
        console.log(`⚠️ Transcript fetch failed for ${videoId}:`, transcriptError.message)
        // Will fallback to title + description
      }

      // AI Processing - Generate enhanced content data using improved input
      let aiSummary = ''
      let referencedTitles: string[] = []
      let aiContentType = ''
      let aiTags: string[] = []

      try {
        // Rate limit OpenAI calls
        await openaiRateLimiter.waitIfNeeded()

        // Direct OpenAI integration for reliable processing
        const OpenAI = require('openai')
        const client = new OpenAI({
          apiKey: process.env.OPENAI_API_KEY || '********************************************************************************************************************************************************************'
        })

        // Prepare input content based on what's available
        const inputContent = transcript
          ? `Title: ${video.title}\nDescription: ${video.description}\n\nTranscript: ${transcript}`
          : `Title: ${video.title}\nDescription: ${video.description}`

        console.log(`🤖 Using ${inputSource} for AI processing`)

        // Categorize content type with improved input and retry logic
        const categoryResponse = await retryWithBackoff(async () => {
          await openaiRateLimiter.waitIfNeeded()
          return client.chat.completions.create({
            model: 'gpt-3.5-turbo',
            messages: [
              {
                role: 'system',
                content: 'You are an expert at categorizing movie and TV content. Analyze the content and return only the category name.'
              },
              {
                role: 'user',
                content: `Categorize this content: review, theory, news, spoiler-free, breakdown, or recommendation.\n\n${inputContent}\n\nReturn only the category name (lowercase).`
              }
            ],
            max_tokens: 10,
            temperature: 0.1
          })
        }, 2, 1000) // 2 retries for AI calls

        aiContentType = categoryResponse.choices[0]?.message?.content?.trim().toLowerCase() || 'review'

        // Map invalid content types to valid ones
        const validContentTypes = ['review', 'theory', 'news', 'spoiler-free', 'breakdown', 'recommendation']
        if (!validContentTypes.includes(aiContentType)) {
          // Map common invalid types to valid ones
          const typeMapping: { [key: string]: string } = {
            'trailer': 'news',
            'teaser': 'news',
            'announcement': 'news',
            'preview': 'news',
            'interview': 'news',
            'analysis': 'breakdown',
            'discussion': 'theory',
            'reaction': 'review'
          }
          aiContentType = typeMapping[aiContentType] || 'news' // Default to 'news' for unknown types
        }

        console.log(`AI categorized as: ${aiContentType}`)

        // Generate enhanced summary using transcript data with retry logic
        const summaryResponse = await retryWithBackoff(async () => {
          await openaiRateLimiter.waitIfNeeded()
          return client.chat.completions.create({
            model: 'gpt-3.5-turbo',
            messages: [
              {
                role: 'system',
                content: 'You are an expert at summarizing movie and TV content. Provide concise, engaging summaries that highlight the key insights and value for viewers.'
              },
              {
                role: 'user',
                content: `Summarize this ${aiContentType} content in 2-3 sentences.\n\n${inputContent}\n\nFocus on the main points, insights, and what makes this content valuable to viewers.`
              }
            ],
            max_tokens: 200,
            temperature: 0.7
          })
        }, 2, 1000)

        aiSummary = summaryResponse.choices[0]?.message?.content?.trim() || 'Summary not available'
        console.log(`✅ AI summary generated (${aiSummary.length} chars)`)

        // Extract referenced titles using AI with transcript data and retry logic
        const titlesResponse = await retryWithBackoff(async () => {
          await openaiRateLimiter.waitIfNeeded()
          return client.chat.completions.create({
            model: 'gpt-3.5-turbo',
            messages: [
              {
                role: 'system',
                content: 'Extract movie and TV show titles mentioned in this content. Return as a JSON array of strings. Only include actual titles, not generic terms.'
              },
              {
                role: 'user',
                content: `Extract all movie and TV show titles mentioned in this content:\n\n${inputContent}\n\nReturn only a JSON array like ["Title 1", "Title 2"]`
              }
            ],
            max_tokens: 150,
            temperature: 0.1
          })
        }, 2, 1000)

        try {
          const titlesText = titlesResponse.choices[0]?.message?.content?.trim() || '[]'
          referencedTitles = JSON.parse(titlesText)
        } catch (parseError) {
          // Fallback to regex extraction
          const searchText = transcript || (video.title + ' ' + video.description)
          const titleMatches = searchText.match(/["']([^"']{3,50})["']/g)
          referencedTitles = titleMatches ? titleMatches.map(match => match.replace(/["']/g, '')) : []
        }
        console.log(`🎬 Found ${referencedTitles.length} referenced titles:`, referencedTitles)

        // Generate enhanced tags using AI with transcript data and retry logic
        const tagsResponse = await retryWithBackoff(async () => {
          await openaiRateLimiter.waitIfNeeded()
          return client.chat.completions.create({
            model: 'gpt-3.5-turbo',
            messages: [
              {
                role: 'system',
                content: 'Generate relevant tags for this movie/TV content. Focus on genres, themes, and key topics. Return as a JSON array of strings.'
              },
              {
                role: 'user',
                content: `Generate 3-8 relevant tags for this content:\n\n${inputContent}\n\nReturn only a JSON array like ["tag1", "tag2", "tag3"]`
              }
            ],
            max_tokens: 100,
            temperature: 0.3
          })
        }, 2, 1000)

        try {
          const tagsText = tagsResponse.choices[0]?.message?.content?.trim() || '[]'
          aiTags = JSON.parse(tagsText)
        } catch (parseError) {
          // Fallback to basic tag extraction
          aiTags = extractTags(video.title, video.description)
        }
        console.log(`🏷️ Generated ${aiTags.length} AI tags:`, aiTags)

      } catch (aiError) {
        console.error(`❌ AI processing failed for ${video.title}:`, aiError)
        // Fallback to basic processing if AI fails
        aiContentType = classifyContent(video.title, video.description)
        aiTags = extractTags(video.title, video.description)
        aiSummary = video.description?.substring(0, 200) + '...' || 'No summary available'
        console.log(`🔄 Using fallback processing for ${video.title}`)
      }

      console.log(`💾 Adding new video: ${video.title} (AI type: ${aiContentType})`)

      // Insert new content with AI-enhanced data using retry logic
      try {
        const { error: insertError } = await retryWithBackoff(async () => {
          const result = await supabase
            .from('content')
            .insert({
              creator_id: creator.id,
              title: video.title,
              description: video.description,
              content_type: aiContentType, // Use AI-determined content type
              platform_url: `https://www.youtube.com/watch?v=${videoId}`,
              platform_id: videoId,
              thumbnail_url: video.thumbnails.high?.url || video.thumbnails.medium?.url || video.thumbnails.default?.url,
              published_at: video.publishedAt,
              ai_summary: aiSummary, // AI-generated summary
              referenced_titles: referencedTitles, // AI-extracted titles
              tags: aiTags // AI-generated tags
            })

          if (result.error) {
            throw new Error(`Database insert failed: ${result.error.message}`)
          }

          return result
        }, 2, 1000)

        console.log(`✅ Successfully added with AI processing: ${video.title}`)
        contentAdded++
      } catch (insertError) {
        console.error(`❌ Failed to insert content after retries:`, insertError)
      }
    } catch (error) {
      console.error(`❌ Error processing video ${video.title}:`, error)
    }
  }

  console.log(`Added ${contentAdded} new videos for ${creator.name} with AI processing`)
  return contentAdded
}

// Instagram aggregation function
async function aggregateInstagramContent(serviceSupabase: any) {
  const errors: string[] = []
  const processedCreators: string[] = []
  let contentAdded = 0

  try {
    // Get users with Instagram credentials
    const { data: instagramUsers, error: usersError } = await serviceSupabase
      .from('user_preferences')
      .select(`
        user_id,
        instagram_access_token,
        instagram_user_id,
        instagram_token_expires_at
      `)
      .not('instagram_access_token', 'is', null)
      .not('instagram_user_id', 'is', null)

    if (usersError) {
      console.error('Error fetching Instagram users:', usersError)
      errors.push('Failed to fetch Instagram users')
      return { contentAdded: 0, errors, processedCreators }
    }

    if (!instagramUsers || instagramUsers.length === 0) {
      console.log('No Instagram users found')
      return { contentAdded: 0, errors, processedCreators }
    }

    console.log(`Found ${instagramUsers.length} Instagram users`)

    for (const user of instagramUsers) {
      try {
        // Check if token is still valid
        const expiresAt = new Date(user.instagram_token_expires_at)
        if (expiresAt < new Date()) {
          console.log(`Instagram token expired for user ${user.user_id}`)
          continue
        }

        // Get user's Instagram media
        const posts = await instagramAPI.getUserMedia(user.instagram_access_token, 25)
        const movieTVPosts = instagramAPI.filterMovieTVContent(posts)

        console.log(`Found ${movieTVPosts.length} movie/TV Instagram posts for user ${user.user_id}`)

        // Process each post with the multi-platform aggregator
        for (const post of movieTVPosts) {
          try {
            // Find or create creator for this Instagram user
            const creatorId = await findOrCreateInstagramCreator(serviceSupabase, user.instagram_user_id, user.instagram_access_token)

            if (creatorId) {
              const result = await multiPlatformAggregator.aggregateInstagramContent(creatorId, user.instagram_user_id)
              contentAdded += result.contentAdded
              errors.push(...result.errors)
              if (result.contentAdded > 0) {
                processedCreators.push(`instagram_${user.instagram_user_id}`)
              }
            }
          } catch (postError) {
            console.error(`Error processing Instagram post:`, postError)
            errors.push(`Instagram post processing failed: ${postError}`)
          }
        }

      } catch (userError) {
        console.error(`Error processing Instagram user ${user.user_id}:`, userError)
        errors.push(`Instagram user ${user.user_id} failed: ${userError}`)
      }
    }

  } catch (error) {
    console.error('Instagram aggregation failed:', error)
    errors.push(`Instagram aggregation failed: ${error}`)
  }

  return { contentAdded, errors, processedCreators }
}

// Twitter aggregation function
async function aggregateTwitterContent(serviceSupabase: any) {
  const errors: string[] = []
  const processedCreators: string[] = []
  let contentAdded = 0

  try {
    // Get creators with Twitter handles
    const { data: twitterCreators, error: creatorsError } = await serviceSupabase
      .from('creators')
      .select('id, name, handle, platform')
      .eq('platform', 'twitter')

    if (creatorsError) {
      console.error('Error fetching Twitter creators:', creatorsError)
      errors.push('Failed to fetch Twitter creators')
      return { contentAdded: 0, errors, processedCreators }
    }

    if (!twitterCreators || twitterCreators.length === 0) {
      console.log('No Twitter creators found')
      return { contentAdded: 0, errors, processedCreators }
    }

    console.log(`Found ${twitterCreators.length} Twitter creators`)

    for (const creator of twitterCreators) {
      try {
        // Get Twitter user ID from handle
        const twitterHandle = creator.handle.replace('@', '')
        const twitterUser = await twitterAPI.getUserByUsername(twitterHandle)

        if (!twitterUser) {
          console.log(`Twitter user not found: ${twitterHandle}`)
          continue
        }

        // Get recent tweets
        const tweets = await twitterAPI.getUserTweets(twitterUser.id, 50)
        const movieTVTweets = twitterAPI.filterMovieTVContent(tweets)

        console.log(`Found ${movieTVTweets.length} movie/TV tweets for ${creator.name}`)

        // Process tweets with the multi-platform aggregator
        const result = await multiPlatformAggregator.aggregateTwitterContent(creator.id, twitterUser.id)
        contentAdded += result.contentAdded
        errors.push(...result.errors)

        if (result.contentAdded > 0) {
          processedCreators.push(creator.name)
        }

      } catch (creatorError) {
        console.error(`Error processing Twitter creator ${creator.name}:`, creatorError)
        errors.push(`Twitter creator ${creator.name} failed: ${creatorError}`)
      }
    }

  } catch (error) {
    console.error('Twitter aggregation failed:', error)
    errors.push(`Twitter aggregation failed: ${error}`)
  }

  return { contentAdded, errors, processedCreators }
}

// Helper function to find or create Instagram creator
async function findOrCreateInstagramCreator(serviceSupabase: any, instagramUserId: string, accessToken: string): Promise<string | null> {
  try {
    // Check if creator already exists
    const { data: existingCreator } = await serviceSupabase
      .from('creators')
      .select('id')
      .eq('platform', 'instagram')
      .eq('handle', instagramUserId)
      .single()

    if (existingCreator) {
      return existingCreator.id
    }

    // Get Instagram user info
    const userInfo = await instagramAPI.getUserMedia(accessToken, 1)
    if (!userInfo || userInfo.length === 0) {
      return null
    }

    // Create new creator
    const { data: newCreator, error: createError } = await serviceSupabase
      .from('creators')
      .insert({
        name: `Instagram User ${instagramUserId}`,
        handle: instagramUserId,
        platform: 'instagram',
        trust_score: 5.0, // Default trust score
        verified: false
      })
      .select('id')
      .single()

    if (createError) {
      console.error('Error creating Instagram creator:', createError)
      return null
    }

    return newCreator.id

  } catch (error) {
    console.error('Error finding/creating Instagram creator:', error)
    return null
  }
}

function classifyContent(title: string, description: string): string {
  const text = `${title} ${description}`.toLowerCase()

  if (text.includes('review') || text.includes('rating')) return 'review'
  if (text.includes('theory') || text.includes('explained')) return 'theory'
  if (text.includes('trailer') || text.includes('teaser') || text.includes('preview')) return 'news'
  if (text.includes('news') || text.includes('update') || text.includes('announcement')) return 'news'
  if (text.includes('spoiler')) return 'spoiler-free' // Will be corrected by AI later
  if (text.includes('breakdown') || text.includes('analysis')) return 'breakdown'
  if (text.includes('recommend') || text.includes('must watch')) return 'recommendation'

  return 'news' // Default to news for most content
}

function extractTags(title: string, description: string): string[] {
  const text = `${title} ${description}`.toLowerCase()
  const tags: string[] = []
  
  // Common movie/TV related keywords
  const keywords = [
    'movie', 'film', 'cinema', 'tv show', 'series', 'episode',
    'marvel', 'dc', 'disney', 'netflix', 'hbo', 'amazon prime',
    'horror', 'comedy', 'drama', 'action', 'sci-fi', 'fantasy',
    'thriller', 'romance', 'documentary', 'animation'
  ]
  
  keywords.forEach(keyword => {
    if (text.includes(keyword)) {
      tags.push(keyword)
    }
  })
  
  return tags.slice(0, 10) // Limit to 10 tags
}

import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient, createServiceRoleClient } from '@/lib/supabase-server'
import { redditAggregator } from '@/lib/reddit-aggregator'
import { youtubeRSSAggregator } from '@/lib/youtube-rss-aggregator'
import { podcastRSSAggregator } from '@/lib/podcast-rss-aggregator'

export async function POST(request: NextRequest) {
  try {
    const serviceSupabase = createServiceRoleClient()
    const { searchParams } = new URL(request.url)
    const sources = searchParams.get('sources')?.split(',') || ['reddit', 'youtube_rss', 'podcast']
    
    console.log(`🚀 Starting alternative content aggregation for: ${sources.join(', ')}`)

    let totalContentAdded = 0
    const errors: string[] = []
    const sourceResults: Record<string, any> = {}
    const startTime = Date.now()

    // Reddit Aggregation
    if (sources.includes('reddit')) {
      console.log('📱 Starting Reddit aggregation...')
      try {
        const redditResult = await redditAggregator.aggregateAllSubreddits()
        sourceResults.reddit = {
          ...redditResult,
          duration: Date.now() - Date.now()
        }
        totalContentAdded += redditResult.contentAdded
        errors.push(...redditResult.errors)
        
        console.log(`✅ Reddit: ${redditResult.contentAdded} items added`)
      } catch (error) {
        const errorMsg = `Reddit aggregation failed: ${error}`
        console.error(errorMsg)
        errors.push(errorMsg)
        sourceResults.reddit = { contentAdded: 0, error: errorMsg }
      }
    }

    // YouTube RSS Aggregation
    if (sources.includes('youtube_rss')) {
      console.log('📺 Starting YouTube RSS aggregation...')
      try {
        const youtubeResult = await youtubeRSSAggregator.aggregateAllChannels()
        sourceResults.youtube_rss = {
          ...youtubeResult,
          duration: Date.now() - Date.now()
        }
        totalContentAdded += youtubeResult.contentAdded
        errors.push(...youtubeResult.errors)
        
        console.log(`✅ YouTube RSS: ${youtubeResult.contentAdded} items added`)
      } catch (error) {
        const errorMsg = `YouTube RSS aggregation failed: ${error}`
        console.error(errorMsg)
        errors.push(errorMsg)
        sourceResults.youtube_rss = { contentAdded: 0, error: errorMsg }
      }
    }

    // Podcast Aggregation
    if (sources.includes('podcast')) {
      console.log('🎙️ Starting Podcast aggregation...')
      try {
        const podcastResult = await podcastRSSAggregator.aggregateAllPodcasts()
        sourceResults.podcast = {
          ...podcastResult,
          duration: Date.now() - Date.now()
        }
        totalContentAdded += podcastResult.contentAdded
        errors.push(...podcastResult.errors)
        
        console.log(`✅ Podcast: ${podcastResult.contentAdded} items added`)
      } catch (error) {
        const errorMsg = `Podcast aggregation failed: ${error}`
        console.error(errorMsg)
        errors.push(errorMsg)
        sourceResults.podcast = { contentAdded: 0, error: errorMsg }
      }
    }

    const totalDuration = Date.now() - startTime

    // Update aggregation stats
    try {
      await serviceSupabase
        .from('content_stats')
        .upsert({
          id: 'alternative_aggregation',
          last_run: new Date().toISOString(),
          total_content_added: totalContentAdded,
          sources_processed: sources,
          duration_ms: totalDuration,
          errors_count: errors.length
        })
    } catch (statsError) {
      console.warn('Failed to update aggregation stats:', statsError)
    }

    return NextResponse.json({
      success: true,
      message: `Alternative content aggregation completed`,
      contentAdded: totalContentAdded,
      sourceResults,
      duration: totalDuration,
      errors: errors.length > 0 ? errors : undefined,
      summary: {
        reddit: sourceResults.reddit?.contentAdded || 0,
        youtube_rss: sourceResults.youtube_rss?.contentAdded || 0,
        podcast: sourceResults.podcast?.contentAdded || 0,
        total: totalContentAdded,
        sources: sources,
        duration_seconds: Math.round(totalDuration / 1000),
        success_rate: ((sources.length - Object.values(sourceResults).filter(r => r.error).length) / sources.length * 100).toFixed(1) + '%'
      }
    })

  } catch (error) {
    console.error('Alternative aggregation error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      contentAdded: 0
    }, { status: 500 })
  }
}

// GET endpoint to check aggregation status
export async function GET(request: NextRequest) {
  try {
    const serviceSupabase = createServiceRoleClient()
    
    // Get recent aggregation stats
    const { data: stats } = await serviceSupabase
      .from('content_stats')
      .select('*')
      .eq('id', 'alternative_aggregation')
      .single()

    // Get content counts by platform
    const { data: contentCounts } = await serviceSupabase
      .from('content')
      .select('platform, created_at')
      .gte('created_at', new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()) // Last 24 hours

    const platformCounts = contentCounts?.reduce((acc: Record<string, number>, item) => {
      acc[item.platform] = (acc[item.platform] || 0) + 1
      return acc
    }, {}) || {}

    // Get total content counts
    const { data: totalCounts } = await serviceSupabase
      .from('content')
      .select('platform')

    const totalPlatformCounts = totalCounts?.reduce((acc: Record<string, number>, item) => {
      acc[item.platform] = (acc[item.platform] || 0) + 1
      return acc
    }, {}) || {}

    return NextResponse.json({
      success: true,
      lastRun: stats?.last_run || null,
      lastRunContentAdded: stats?.total_content_added || 0,
      lastRunDuration: stats?.duration_ms || 0,
      lastRunErrors: stats?.errors_count || 0,
      contentLast24Hours: platformCounts,
      totalContent: totalPlatformCounts,
      availableSources: ['reddit', 'youtube_rss', 'podcast'],
      recommendations: {
        reddit: 'High-quality discussions from movie/TV communities',
        youtube_rss: 'Video content from popular movie/TV channels (no API key needed)',
        podcast: 'Professional analysis from movie/TV podcasts'
      }
    })

  } catch (error) {
    console.error('Aggregation status error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

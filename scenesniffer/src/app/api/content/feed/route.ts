import { createServerSupabaseClient } from '@/lib/supabase-server'
import { NextRequest, NextResponse } from 'next/server'
import { FeedCache } from '@/lib/cache'

// Import personalization engine with fallback
let personalizationEngine: any = null
try {
  const { personalizationEngine: engine } = require('@/lib/personalization')
  personalizationEngine = engine
} catch (error) {
  console.warn('Personalization engine not available:', error)
}

export async function GET(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)

    // Get pagination parameters
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const offsetParam = searchParams.get('offset')
    const offset = offsetParam ? parseInt(offsetParam) : (page - 1) * limit

    // Get filter parameters (enhanced)
    const contentTypes = searchParams.get('content_types')?.split(',').filter(Boolean) || []
    const genres = searchParams.get('genres')?.split(',').filter(Boolean) || []
    const creators = searchParams.get('creators')?.split(',').filter(Boolean) || []
    const platforms = searchParams.get('platforms')?.split(',').filter(Boolean) || []
    const dateRange = searchParams.get('date_range') || 'all'
    const sortBy = searchParams.get('sort_by') || 'relevance'

    // Legacy support
    const contentType = searchParams.get('type')
    const genre = searchParams.get('genre')
    const creatorId = searchParams.get('creator')

    // Create cache key for this specific request (enhanced)
    const cacheFilters = {
      page,
      limit,
      contentTypes,
      genres,
      creators,
      platforms,
      dateRange,
      sortBy,
      // Legacy support
      contentType,
      genre,
      creatorId
    }

    // Try to get cached result first
    const cachedResult = await FeedCache.getFeedContent(user.id, cacheFilters)
    if (cachedResult) {
      return NextResponse.json({
        ...cachedResult,
        cached: true,
        cacheTimestamp: new Date().toISOString()
      })
    }

    // Get user's preferences (with caching)
    let userPrefs = await FeedCache.getUserPreferences(user.id)
    if (!userPrefs) {
      const { data } = await supabase
        .from('user_preferences')
        .select('genres, streaming_services')
        .eq('user_id', user.id)
        .single()

      userPrefs = data
      if (userPrefs) {
        await FeedCache.setUserPreferences(user.id, userPrefs)
      }
    }

    // Get user's followed creators
    const { data: userCreators } = await supabase
      .from('user_creators')
      .select('creator_id')
      .eq('user_id', user.id)

    if (!userCreators || userCreators.length === 0) {
      return NextResponse.json({
        success: true,
        content: [],
        pagination: {
          page,
          limit,
          total: 0,
          hasMore: false
        },
        message: 'No followed creators found'
      })
    }

    const followedCreatorIds = userCreators.map(uc => uc.creator_id)

    // Build optimized query (gracefully handle missing engagement columns)
    let baseQuery = supabase
      .from('content')
      .select(`
        id,
        title,
        description,
        content_type,
        platform,
        platform_url,
        platform_id,
        thumbnail_url,
        published_at,
        ai_summary,
        referenced_titles,
        tags,
        media_type,
        duration,
        engagement_metrics,
        content_metadata,
        ai_content_quality_score,
        creator_id,
        creators!inner (
          id,
          name,
          platform,
          handle,
          avatar_url,
          trust_score,
          verified
        )
      `)
      .in('creator_id', followedCreatorIds)

    // Apply enhanced filters before ordering for better performance

    // Content type filtering (multiple types supported)
    if (contentTypes.length > 0) {
      baseQuery = baseQuery.in('content_type', contentTypes)
    } else if (contentType) {
      baseQuery = baseQuery.eq('content_type', contentType)
    }

    // Creator filtering (multiple creators supported)
    if (creators.length > 0) {
      baseQuery = baseQuery.in('creator_id', creators)
    } else if (creatorId) {
      baseQuery = baseQuery.eq('creator_id', creatorId)
    }

    // Platform filtering (multiple platforms supported)
    if (platforms.length > 0) {
      baseQuery = baseQuery.in('platform', platforms)
    }

    // Date range filtering
    if (dateRange !== 'all') {
      const now = new Date()
      let startDate: Date

      switch (dateRange) {
        case 'today':
          startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
          break
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
          break
        case 'month':
          startDate = new Date(now.getFullYear(), now.getMonth(), 1)
          break
        default:
          startDate = new Date(0) // All time
      }

      baseQuery = baseQuery.gte('published_at', startDate.toISOString())
    }

    // Genre filtering with improved flexible matching
    if (genres.length > 0) {
      // Use OR logic for multiple genres - content matching ANY of the selected genres
      const genreFilters = genres.map(g => `tags.cs.{${g}}`).join(',')
      baseQuery = baseQuery.or(genreFilters)
    } else if (genre) {
      baseQuery = baseQuery.contains('tags', [genre])
    } else if (userPrefs?.genres && userPrefs.genres.length > 0) {
      // Apply user preferences if no explicit genre filter
      const genreFilters = userPrefs.genres.map(g => `tags.cs.{${g}}`).join(',')
      baseQuery = baseQuery.or(genreFilters)
    }

    // Apply sorting based on sortBy parameter (fallback to published_at for missing columns)
    switch (sortBy) {
      case 'newest':
        baseQuery = baseQuery.order('published_at', { ascending: false })
        break
      case 'popular':
      case 'trending':
        // Fallback to published_at until engagement columns are added
        baseQuery = baseQuery.order('published_at', { ascending: false })
        break
      case 'relevance':
      default:
        // For relevance, we'll sort after scoring
        baseQuery = baseQuery.order('published_at', { ascending: false })
        break
    }

    // Get content with pagination (skip separate count query for better performance)
    const { data: content, error: contentError } = await baseQuery
      .range(offset, offset + limit - 1)

    if (contentError) {
      console.error('Error fetching content:', contentError)
      return NextResponse.json({ error: 'Failed to fetch content' }, { status: 500 })
    }

    // Calculate relevance scores efficiently
    const scoredContent = content?.map(item => ({
      ...item,
      relevanceScore: calculateRelevanceScore(item, userPrefs, user.id)
    })).sort((a, b) => b.relevanceScore - a.relevanceScore) || []

    // Apply advanced personalization (NEW!) with fallback
    let personalizedContent = scoredContent
    if (personalizationEngine) {
      try {
        personalizedContent = await personalizationEngine.getPersonalizedRecommendations(
          user.id,
          scoredContent,
          Math.min(limit * 2, 50) // Get more content for better personalization
        )
      } catch (personalizationError) {
        console.warn('Personalization engine failed, using basic scoring:', personalizationError)
        // Fall back to basic scored content
      }
    } else {
      console.log('Personalization engine not available, using basic scoring')
    }

    // Apply content type balancing for diversity
    const balancedContent = applyContentTypeBalancing(personalizedContent, limit)

    // Get approximate count only when needed (for pagination info)
    let totalCount = balancedContent.length
    if (balancedContent.length === limit) {
      // Only do expensive count query if we might have more results
      const { count } = await supabase
        .from('content')
        .select('*', { count: 'exact', head: true })
        .in('creator_id', followedCreatorIds)
      totalCount = count || 0
    }

    const responseData = {
      success: true,
      content: balancedContent,
      pagination: {
        page,
        limit,
        total: totalCount,
        hasMore: totalCount > offset + limit,
        estimatedTotal: scoredContent.length === limit // Indicate if total is estimated
      },
      filters: {
        contentTypes,
        genres,
        creators,
        dateRange,
        sortBy,
        // Legacy support
        contentType,
        genre,
        creatorId
      },
      performance: {
        itemsReturned: scoredContent.length,
        queryOptimized: true,
        cached: false
      }
    }

    // Cache the result for future requests
    await FeedCache.setFeedContent(user.id, cacheFilters, responseData)

    return NextResponse.json(responseData)

  } catch (error) {
    console.error('Feed error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// Cache for relevance score calculations to avoid repeated computations
const scoreCache = new Map<string, number>()

function calculateRelevanceScore(content: any, userPrefs: any, userId: string): number {
  // Create cache key based on content and user preferences
  const cacheKey = `${content.id}-${userId}-${JSON.stringify(userPrefs?.genres || [])}`

  // Check cache first
  if (scoreCache.has(cacheKey)) {
    return scoreCache.get(cacheKey)!
  }

  let score = 0

  // Base score from creator trust score (optimized calculation)
  const creatorTrustScore = content.creators?.trust_score || 0
  score += creatorTrustScore * 10

  // Boost for verified creators
  if (content.creators?.verified) {
    score += 20
  }

  // Enhanced recency and engagement calculation
  const publishedTime = new Date(content.published_at).getTime()
  const daysSincePublished = (Date.now() - publishedTime) / (1000 * 60 * 60 * 24)
  const recencyScore = Math.max(0, 30 - daysSincePublished)
  score += recencyScore

  // Engagement metrics boost (gracefully handle missing columns)
  const viewCount = content.view_count || 0
  const engagementScore = content.engagement_score || 0

  // Only apply engagement boosts if the columns exist and have data
  if (viewCount > 0) {
    const viewBoost = Math.min(Math.log10(viewCount + 1) * 10, 50) // Max 50 points
    score += viewBoost
  }

  if (engagementScore > 0) {
    score += Math.min(engagementScore * 2, 100) // Max 100 points from engagement

    // Trending detection - content with high engagement velocity gets extra boost
    if (daysSincePublished > 0) {
      const engagementVelocity = engagementScore / daysSincePublished
      if (engagementVelocity > 5) { // High velocity threshold
        const trendingBoost = Math.min(engagementVelocity * 5, 75) // Max 75 points
        score += trendingBoost
      }
    }
  }

  // Optimized content type scoring (static lookup)
  const contentTypeScores = {
    'review': 12,
    'recommendation': 18,
    'theory': 10,
    'news': 8,
    'breakdown': 15,
    'spoiler-free': 14
  } as const

  score += contentTypeScores[content.content_type as keyof typeof contentTypeScores] || 5

  // Optimized AI summary quality boost
  if (content.ai_summary) {
    const summaryLength = content.ai_summary.length
    let summaryBoost = 15
    if (summaryLength > 100) summaryBoost += 5
    if (summaryLength > 200) summaryBoost += 5
    score += summaryBoost
  }

  // Optimized referenced titles boost
  if (content.referenced_titles?.length > 0) {
    score += Math.min(content.referenced_titles.length * 8, 40)
  }

  // Optimized genre matching with user preferences
  if (userPrefs?.genres?.length > 0 && content.tags?.length > 0) {
    let genreMatchScore = 0
    const userGenresLower = userPrefs.genres.map((g: string) => g.toLowerCase())
    const contentTagsLower = content.tags.map((t: string) => t.toLowerCase())

    // Direct matches (optimized with Set for O(1) lookup)
    const directMatches = userGenresLower.filter((genre: string) =>
      contentTagsLower.some((tag: string) => tag.includes(genre))
    ).length
    genreMatchScore += directMatches * 20

    // Semantic matches (optimized lookup table)
    const semanticMap = {
      'action': ['thriller', 'adventure'],
      'horror': ['thriller'],
      'sci-fi': ['science', 'future']
    } as const

    const semanticMatches = userGenresLower.filter((genre: string) => {
      const relatedTerms = semanticMap[genre as keyof typeof semanticMap]
      return relatedTerms?.some(term =>
        contentTagsLower.some(tag => tag.includes(term))
      )
    }).length
    genreMatchScore += semanticMatches * 10

    score += genreMatchScore
  }

  // Optimized tag quality boost
  if (content.tags?.length > 0) {
    score += Math.min(content.tags.length * 2, 12)
  }

  // Optimized content freshness calculation
  if (content.content_type === 'news') {
    score += Math.max(0, 10 - daysSincePublished * 2)
  } else if (content.content_type === 'theory' || content.content_type === 'breakdown') {
    score += Math.min(daysSincePublished * 0.5, 10)
  }

  // Quality penalty for unprocessed content
  if (!content.ai_summary && !content.referenced_titles?.length) {
    score -= 15
  }

  const finalScore = Math.max(0, Math.round(score))

  // Cache the result for future use
  scoreCache.set(cacheKey, finalScore)

  // Limit cache size to prevent memory issues
  if (scoreCache.size > 1000) {
    const firstKey = scoreCache.keys().next().value
    scoreCache.delete(firstKey)
  }

  return finalScore
}

// Content type balancing function for feed diversity
function applyContentTypeBalancing(content: any[], limit: number): any[] {
  if (content.length <= limit) {
    return content
  }

  // Define ideal distribution percentages for content types
  const idealDistribution = {
    'recommendation': 0.25, // 25% - High value content
    'review': 0.30,         // 30% - Popular content type
    'breakdown': 0.20,      // 20% - Educational content
    'theory': 0.15,         // 15% - Engaging for fans
    'spoiler-free': 0.10,   // 10% - Broad appeal
    'news': 0.10           // 10% - Timely content
  } as const

  // Group content by type
  const contentByType = content.reduce((acc, item) => {
    const type = item.content_type || 'review'
    if (!acc[type]) acc[type] = []
    acc[type].push(item)
    return acc
  }, {} as Record<string, any[]>)

  // Calculate target counts for each type
  const targetCounts = Object.entries(idealDistribution).reduce((acc, [type, percentage]) => {
    acc[type] = Math.floor(limit * percentage)
    return acc
  }, {} as Record<string, number>)

  // Select content maintaining diversity
  const balancedContent: any[] = []
  const remainingContent: any[] = []

  // First pass: fill target quotas for each content type
  Object.entries(contentByType).forEach(([type, items]) => {
    const targetCount = targetCounts[type] || Math.floor(limit * 0.05) // 5% default
    const selectedItems = items.slice(0, targetCount)
    const remaining = items.slice(targetCount)

    balancedContent.push(...selectedItems)
    remainingContent.push(...remaining)
  })

  // Second pass: fill remaining slots with highest scoring content
  const remainingSlots = limit - balancedContent.length
  if (remainingSlots > 0) {
    const topRemaining = remainingContent
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, remainingSlots)

    balancedContent.push(...topRemaining)
  }

  // Final sort by relevance score
  return balancedContent
    .sort((a, b) => b.relevanceScore - a.relevanceScore)
    .slice(0, limit)
}

// GET endpoint for content statistics
export async function POST(request: NextRequest) {
  try {
    const supabase = await createServerSupabaseClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's followed creators
    const { data: userCreators } = await supabase
      .from('user_creators')
      .select('creator_id')
      .eq('user_id', user.id)

    if (!userCreators || userCreators.length === 0) {
      return NextResponse.json({
        success: true,
        stats: {
          totalContent: 0,
          contentByType: {},
          recentContent: 0,
          topCreators: []
        }
      })
    }

    const followedCreatorIds = userCreators.map(uc => uc.creator_id)

    // Get content statistics
    const { data: contentStats } = await supabase
      .from('content')
      .select('content_type, creator_id, published_at, creators(name, trust_score)')
      .in('creator_id', followedCreatorIds)

    if (!contentStats) {
      return NextResponse.json({
        success: true,
        stats: {
          totalContent: 0,
          contentByType: {},
          recentContent: 0,
          topCreators: []
        }
      })
    }

    // Calculate statistics
    const totalContent = contentStats.length
    
    const contentByType = contentStats.reduce((acc: any, item) => {
      acc[item.content_type] = (acc[item.content_type] || 0) + 1
      return acc
    }, {})

    const recentContent = contentStats.filter(item => {
      const publishedDate = new Date(item.published_at)
      const daysSince = (Date.now() - publishedDate.getTime()) / (1000 * 60 * 60 * 24)
      return daysSince <= 7
    }).length

    const creatorStats = contentStats.reduce((acc: any, item) => {
      const creatorId = item.creator_id
      if (!acc[creatorId]) {
        acc[creatorId] = {
          name: item.creators?.name || 'Unknown',
          contentCount: 0,
          trustScore: item.creators?.trust_score || 0
        }
      }
      acc[creatorId].contentCount++
      return acc
    }, {})

    const topCreators = Object.values(creatorStats)
      .sort((a: any, b: any) => b.contentCount - a.contentCount)
      .slice(0, 5)

    return NextResponse.json({
      success: true,
      stats: {
        totalContent,
        contentByType,
        recentContent,
        topCreators
      }
    })

  } catch (error) {
    console.error('Stats error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

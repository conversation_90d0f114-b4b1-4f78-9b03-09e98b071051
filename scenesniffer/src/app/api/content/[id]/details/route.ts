import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase-server'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createServerSupabaseClient()
    
    // Get current user (optional for public content)
    const { data: { user } } = await supabase.auth.getUser()
    
    const contentId = params.id

    // Fetch comprehensive content details using the database function
    const { data: contentDetails, error: detailsError } = await supabase
      .rpc('get_content_details', { content_id: contentId })
      .single()

    if (detailsError) {
      console.error('Error fetching content details:', detailsError)
      
      // Fallback to direct query if function doesn't exist
      const { data: fallbackData, error: fallbackError } = await supabase
        .from('content')
        .select(`
          id,
          title,
          description,
          content_type,
          platform,
          platform_url,
          platform_id,
          thumbnail_url,
          published_at,
          ai_summary,
          referenced_titles,
          tags,
          media_type,
          media_urls,
          duration,
          engagement_metrics,
          content_metadata,
          ai_extracted_text,
          ai_content_quality_score,
          ai_topics,
          creators!inner (
            name,
            handle,
            avatar_url,
            verified,
            trust_score,
            platform as creator_platform
          )
        `)
        .eq('id', contentId)
        .single()

      if (fallbackError) {
        return NextResponse.json({ 
          error: 'Content not found' 
        }, { status: 404 })
      }

      // Transform fallback data to match expected format
      const transformedData = {
        ...fallbackData,
        creator_name: fallbackData.creators.name,
        creator_handle: fallbackData.creators.handle,
        creator_avatar: fallbackData.creators.avatar_url,
        creator_verified: fallbackData.creators.verified,
        creator_trust_score: fallbackData.creators.trust_score,
        display_type: getDisplayType(fallbackData.platform, fallbackData.media_type),
        engagement_summary: getEngagementSummary(fallbackData.platform, fallbackData.engagement_metrics),
        duration_display: getDurationDisplay(fallbackData.duration),
        platform_specific_data: getPlatformSpecificData(fallbackData)
      }

      return NextResponse.json({
        success: true,
        content: transformedData
      })
    }

    // If user is authenticated, track this as a view
    if (user) {
      try {
        await supabase
          .from('content_interactions')
          .insert({
            content_id: contentId,
            user_id: user.id,
            interaction_type: 'view',
            metadata: {
              source: 'details_page',
              timestamp: new Date().toISOString()
            }
          })
      } catch (trackingError) {
        // Don't fail the request if tracking fails
        console.warn('Failed to track content view:', trackingError)
      }
    }

    return NextResponse.json({
      success: true,
      content: contentDetails
    })

  } catch (error) {
    console.error('Content details error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// Helper functions for fallback data transformation
function getDisplayType(platform: string, mediaType?: string): string {
  switch (platform) {
    case 'youtube':
      return 'YouTube Video'
    case 'instagram':
      if (mediaType === 'reel') return 'Instagram Reel'
      if (mediaType === 'carousel') return 'Instagram Carousel'
      if (mediaType === 'story') return 'Instagram Story'
      return 'Instagram Post'
    case 'twitter':
      if (mediaType === 'thread') return 'Twitter Thread'
      return 'Tweet'
    default:
      return 'Content'
  }
}

function getEngagementSummary(platform: string, metrics: any): string {
  if (!metrics) return 'No engagement data'
  
  switch (platform) {
    case 'youtube':
      return `${(metrics.views || 0).toLocaleString()} views`
    case 'instagram':
      return `${(metrics.likes || 0).toLocaleString()} likes`
    case 'twitter':
      return `${(metrics.retweets || 0).toLocaleString()} retweets`
    default:
      return 'No engagement data'
  }
}

function getDurationDisplay(duration?: number): string | null {
  if (!duration || duration <= 0) return null
  
  if (duration >= 3600) {
    const hours = Math.floor(duration / 3600)
    const minutes = Math.floor((duration % 3600) / 60)
    return `${hours}h ${minutes}m`
  } else if (duration >= 60) {
    const minutes = Math.floor(duration / 60)
    const seconds = duration % 60
    return `${minutes}m ${seconds}s`
  } else {
    return `${duration}s`
  }
}

function getPlatformSpecificData(content: any): any {
  switch (content.platform) {
    case 'youtube':
      return {
        video_id: content.platform_id,
        embed_url: `https://www.youtube.com/embed/${content.platform_id}`,
        watch_url: content.platform_url,
        channel_url: `https://www.youtube.com/channel/${content.content_metadata?.channel_id || ''}`,
        category: content.content_metadata?.category,
        language: content.content_metadata?.language
      }
    case 'instagram':
      return {
        post_id: content.platform_id,
        post_url: content.platform_url,
        media_type: content.media_type,
        media_count: content.media_urls?.length || 1,
        profile_url: `https://instagram.com/${content.creator_handle}`,
        hashtags: content.content_metadata?.hashtags
      }
    case 'twitter':
      return {
        tweet_id: content.platform_id,
        tweet_url: content.platform_url,
        is_thread: content.media_type === 'thread',
        thread_length: content.media_type === 'thread' ? (content.media_urls?.length || 1) : 1,
        profile_url: `https://twitter.com/${content.creator_handle}`,
        mentions: content.content_metadata?.mentions
      }
    default:
      return {}
  }
}

// POST endpoint to update content details (admin only)
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createServerSupabaseClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const contentId = params.id
    const updates = await request.json()

    // Update content details
    const { data: updatedContent, error: updateError } = await supabase
      .from('content')
      .update({
        ...updates,
        updated_at: new Date().toISOString()
      })
      .eq('id', contentId)
      .select()
      .single()

    if (updateError) {
      console.error('Error updating content:', updateError)
      return NextResponse.json({ 
        error: 'Failed to update content' 
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      content: updatedContent
    })

  } catch (error) {
    console.error('Content update error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

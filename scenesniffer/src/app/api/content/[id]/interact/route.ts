import { NextRequest, NextResponse } from 'next/server'
import { createServerSupabaseClient } from '@/lib/supabase-server'

interface InteractionRequest {
  interaction_type: 'view' | 'click' | 'share' | 'bookmark' | 'like' | 'time_spent'
  interaction_value?: number // For time_spent in seconds
  metadata?: Record<string, any>
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createServerSupabaseClient()
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const contentId = params.id
    const body: InteractionRequest = await request.json()

    // Validate interaction type
    const validTypes = ['view', 'click', 'share', 'bookmark', 'like', 'time_spent']
    if (!validTypes.includes(body.interaction_type)) {
      return NextResponse.json({ 
        error: 'Invalid interaction type' 
      }, { status: 400 })
    }

    // Validate interaction value for time_spent
    if (body.interaction_type === 'time_spent' && (!body.interaction_value || body.interaction_value < 0)) {
      return NextResponse.json({ 
        error: 'time_spent interactions require a positive interaction_value' 
      }, { status: 400 })
    }

    // Check if content exists
    const { data: content, error: contentError } = await supabase
      .from('content')
      .select('id, title')
      .eq('id', contentId)
      .single()

    if (contentError || !content) {
      return NextResponse.json({ 
        error: 'Content not found' 
      }, { status: 404 })
    }

    // For view interactions, check if user has already viewed this content recently
    // to avoid inflating view counts
    if (body.interaction_type === 'view') {
      const { data: recentView } = await supabase
        .from('content_interactions')
        .select('id')
        .eq('content_id', contentId)
        .eq('user_id', user.id)
        .eq('interaction_type', 'view')
        .gte('created_at', new Date(Date.now() - 5 * 60 * 1000).toISOString()) // Last 5 minutes
        .single()

      if (recentView) {
        // Don't record duplicate view, but return success
        return NextResponse.json({ 
          success: true, 
          message: 'View already recorded recently' 
        })
      }
    }

    // Record the interaction
    const { error: insertError } = await supabase
      .from('content_interactions')
      .insert({
        content_id: contentId,
        user_id: user.id,
        interaction_type: body.interaction_type,
        interaction_value: body.interaction_value || 1.0,
        metadata: body.metadata || {}
      })

    if (insertError) {
      console.error('Error recording interaction:', insertError)
      return NextResponse.json({ 
        error: 'Failed to record interaction' 
      }, { status: 500 })
    }

    // Return success with some analytics
    const response = {
      success: true,
      message: 'Interaction recorded successfully',
      content_id: contentId,
      interaction_type: body.interaction_type
    }

    // For view interactions, also return updated view count
    if (body.interaction_type === 'view') {
      const { data: updatedContent } = await supabase
        .from('content')
        .select('view_count')
        .eq('id', contentId)
        .single()

      if (updatedContent) {
        response.view_count = updatedContent.view_count
      }
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Content interaction error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET endpoint to retrieve interaction stats for content
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createServerSupabaseClient()
    
    // Get current user (optional for stats)
    const { data: { user } } = await supabase.auth.getUser()
    const contentId = params.id

    // Get content with engagement metrics
    const { data: content, error: contentError } = await supabase
      .from('content')
      .select('id, title, view_count, engagement_score, last_viewed_at')
      .eq('id', contentId)
      .single()

    if (contentError || !content) {
      return NextResponse.json({ 
        error: 'Content not found' 
      }, { status: 404 })
    }

    // Get interaction breakdown
    const { data: interactions, error: interactionsError } = await supabase
      .from('content_interactions')
      .select('interaction_type, COUNT(*) as count, SUM(interaction_value) as total_value')
      .eq('content_id', contentId)
      .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()) // Last 30 days

    if (interactionsError) {
      console.error('Error fetching interactions:', interactionsError)
    }

    // Get user's interactions if authenticated
    let userInteractions = null
    if (user) {
      const { data } = await supabase
        .from('content_interactions')
        .select('interaction_type, created_at, interaction_value')
        .eq('content_id', contentId)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(10)

      userInteractions = data
    }

    return NextResponse.json({
      success: true,
      content: {
        id: content.id,
        title: content.title,
        view_count: content.view_count,
        engagement_score: content.engagement_score,
        last_viewed_at: content.last_viewed_at
      },
      interactions: interactions || [],
      user_interactions: userInteractions
    })

  } catch (error) {
    console.error('Content stats error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

import { createServerSupabaseClient } from '@/lib/supabase-server'
import { OpenAIService } from '@/lib/openai'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createServerSupabaseClient()
    const { id: contentId } = await params

    // Get the content
    const { data: content, error: contentError } = await supabase
      .from('content')
      .select('*')
      .eq('id', contentId)
      .single()

    if (contentError || !content) {
      return NextResponse.json({
        success: false,
        error: 'Content not found'
      }, { status: 404 })
    }

    // Initialize OpenAI service
    const openaiService = new OpenAIService()

    // Create analysis prompt
    const analysisPrompt = `
Analyze this content and provide a detailed analysis in JSON format:

Title: ${content.title}
Description: ${content.description}
Content Type: ${content.content_type}
AI Summary: ${content.ai_summary || 'Not available'}
Referenced Titles: ${content.referenced_titles?.join(', ') || 'None detected'}

Please provide analysis in this exact JSON structure:
{
  "contentType": "roundup|review|theory|news|breakdown",
  "referencedMovies": [
    {
      "title": "Movie Title",
      "slug": "movie-title-slug",
      "timestamp": "2:15" (if applicable),
      "sentiment": "positive|negative|mixed|neutral",
      "confidence": 0.85
    }
  ],
  "spoilerLevel": "spoiler-free|minor-spoilers|major-spoilers",
  "keyTopics": ["topic1", "topic2", "topic3"],
  "aiSummary": "Brief summary of what this content covers",
  "targetAudience": ["horror fans", "marvel fans", etc],
  "watchTime": "15 minutes" (estimate if video content)
}

Focus on:
1. Detecting the primary content type based on language and structure
2. Extracting all movie/TV show references with sentiment
3. Determining spoiler level from content
4. Identifying key topics and themes
5. Determining target audience
`

    try {
      const analysisResult = await openaiService.generateCompletion(analysisPrompt, {
        maxTokens: 1000,
        temperature: 0.3
      })

      if (!analysisResult) {
        throw new Error('Failed to generate AI analysis')
      }

      // Parse the JSON response
      let analysis
      try {
        analysis = JSON.parse(analysisResult)
      } catch (parseError) {
        console.error('Error parsing AI analysis JSON:', parseError)
        throw new Error('Invalid AI analysis format')
      }

      // Validate required fields
      if (!analysis.contentType || !analysis.aiSummary) {
        throw new Error('Incomplete AI analysis')
      }

      // Generate slugs for referenced movies
      if (analysis.referencedMovies) {
        analysis.referencedMovies = analysis.referencedMovies.map((movie: any) => ({
          ...movie,
          slug: movie.slug || movie.title.toLowerCase()
            .replace(/[^a-z0-9\s]/g, '')
            .replace(/\s+/g, '-')
            .trim()
        }))
      }

      // Update the content with analysis
      const { error: updateError } = await supabase
        .from('content')
        .update({
          content_analysis: analysis,
          updated_at: new Date().toISOString()
        })
        .eq('id', contentId)

      if (updateError) {
        console.error('Error updating content with analysis:', updateError)
        throw new Error('Failed to save analysis')
      }

      // Also update referenced_titles if we found new ones
      if (analysis.referencedMovies && analysis.referencedMovies.length > 0) {
        const newReferencedTitles = analysis.referencedMovies.map((movie: any) => movie.title)
        
        await supabase
          .from('content')
          .update({
            referenced_titles: newReferencedTitles
          })
          .eq('id', contentId)
      }

      return NextResponse.json({
        success: true,
        analysis,
        message: 'Content analysis completed successfully'
      })

    } catch (aiError) {
      console.error('AI analysis error:', aiError)
      
      // Fallback: Create basic analysis from existing data
      const fallbackAnalysis = {
        contentType: content.content_type || 'review',
        referencedMovies: content.referenced_titles?.map((title: string) => ({
          title,
          slug: title.toLowerCase().replace(/[^a-z0-9\s]/g, '').replace(/\s+/g, '-'),
          sentiment: 'neutral',
          confidence: 0.5
        })) || [],
        spoilerLevel: 'spoiler-free',
        keyTopics: content.tags || [],
        aiSummary: content.ai_summary || content.description || 'Content analysis not available',
        targetAudience: ['general audience'],
        watchTime: null
      }

      // Save fallback analysis
      await supabase
        .from('content')
        .update({
          content_analysis: fallbackAnalysis
        })
        .eq('id', contentId)

      return NextResponse.json({
        success: true,
        analysis: fallbackAnalysis,
        message: 'Basic analysis completed (AI analysis failed)',
        fallback: true
      })
    }

  } catch (error) {
    console.error('Error analyzing content:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

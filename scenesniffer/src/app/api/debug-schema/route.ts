import { createServerSupabaseClient } from '@/lib/supabase-server'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const supabase = await createServerSupabaseClient()
    
    // Check if users table exists and get its structure
    const { data: usersData, error: usersError } = await supabase
      .from('users')
      .select('*')
      .limit(5)
    
    // Check if user_preferences table exists
    const { data: prefsData, error: prefsError } = await supabase
      .from('user_preferences')
      .select('*')
      .limit(1)
    
    return NextResponse.json({
      success: true,
      tables: {
        users: {
          exists: !usersError,
          error: usersError?.message,
          sampleData: usersData
        },
        user_preferences: {
          exists: !prefsError,
          error: prefsError?.message,
          sampleData: prefsData
        }
      }
    })
  } catch (error) {
    console.error('Schema debug error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

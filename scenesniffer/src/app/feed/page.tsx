'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/components/providers/auth-provider'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { CreatorManager } from '@/components/creators/creator-manager'
import { RefreshWithProgress } from '@/components/RefreshWithProgress'
import { FeedFilters, type FeedFilters as FeedFiltersType } from '@/components/feed/FeedFilters'
import { Film, Users, Settings, LogOut, Bell, Search, Play, ExternalLink, Clock, Star, UserPlus, X, Loader2, Edit, Plus, Check, Package, Bookmark, Eye, Heart, MessageCircle, Repeat2, Instagram, Twitter, Youtube } from 'lucide-react'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { CurrentlyWatchingSection } from '@/components/watchlist/CurrentlyWatchingSection'
// import { WatchlistButtonCompact } from '@/components/watchlist/WatchlistButton'
import { GENRES } from '@/utils/constants'

export default function FeedPage() {
  const { user, loading, signOut } = useAuth()
  const router = useRouter()
  const [preferences, setPreferences] = useState<any>(null)
  const [loadingPrefs, setLoadingPrefs] = useState(true)
  const [content, setContent] = useState<any[]>([])
  const [loadingContent, setLoadingContent] = useState(true)
  const [contentStats, setContentStats] = useState<any>(null)
  const [showCreatorManager, setShowCreatorManager] = useState(false)
  const [autoRefreshing, setAutoRefreshing] = useState(false)
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalContent, setTotalContent] = useState(0)
  const [loadingMore, setLoadingMore] = useState(false)
  const [showAllGenres, setShowAllGenres] = useState(false)
  const [editingGenres, setEditingGenres] = useState(false)
  const [selectedGenres, setSelectedGenres] = useState<string[]>([])
  const [savingGenres, setSavingGenres] = useState(false)
  const [feedFilters, setFeedFilters] = useState<FeedFiltersType>({
    contentTypes: [],
    genres: [],
    creators: [],
    platforms: [],
    dateRange: 'all',
    sortBy: 'relevance'
  })
  const [availableCreators, setAvailableCreators] = useState<Array<{ id: string; name: string; handle: string }>>([])
  const itemsPerPage = 25

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth')
    }
  }, [user, loading, router])

  useEffect(() => {
    if (user) {
      fetchPreferences()
      fetchContent()
      fetchContentStats()
      fetchAvailableCreators()
    }
  }, [user])

  // Refetch content when filters change
  useEffect(() => {
    if (user) {
      fetchContent()
    }
  }, [feedFilters, user])

  // Auto-refresh feed every 5 minutes
  useEffect(() => {
    if (!user) return

    const interval = setInterval(() => {
      console.log('🔄 Auto-refreshing feed...')
      fetchContent(true) // Pass true to indicate auto-refresh
      fetchContentStats()
    }, 5 * 60 * 1000) // 5 minutes

    return () => clearInterval(interval)
  }, [user])

  const fetchPreferences = async () => {
    try {
      const response = await fetch('/api/user/preferences')
      if (response.ok) {
        const data = await response.json()
        setPreferences(data)
      }
    } catch (error) {
      console.error('Error fetching preferences:', error)
    } finally {
      setLoadingPrefs(false)
    }
  }

  const fetchContent = async (isAutoRefresh = false, page = 1, append = false) => {
    try {
      if (isAutoRefresh) {
        setAutoRefreshing(true)
      }
      if (!append) {
        setLoadingContent(true)
      } else {
        setLoadingMore(true)
      }

      const offset = (page - 1) * itemsPerPage

      // Build query parameters with filters
      const params = new URLSearchParams({
        limit: itemsPerPage.toString(),
        offset: offset.toString()
      })

      // Add filter parameters
      if (feedFilters.contentTypes.length > 0) {
        params.set('content_types', feedFilters.contentTypes.join(','))
      }
      if (feedFilters.genres.length > 0) {
        params.set('genres', feedFilters.genres.join(','))
      }
      if (feedFilters.creators.length > 0) {
        params.set('creators', feedFilters.creators.join(','))
      }
      if (feedFilters.platforms.length > 0) {
        params.set('platforms', feedFilters.platforms.join(','))
      }
      if (feedFilters.dateRange !== 'all') {
        params.set('date_range', feedFilters.dateRange)
      }
      if (feedFilters.sortBy !== 'relevance') {
        params.set('sort_by', feedFilters.sortBy)
      }

      const response = await fetch(`/api/content/feed?${params.toString()}`)
      if (response.ok) {
        const data = await response.json()
        if (append) {
          setContent(prev => [...prev, ...(data.content || [])])
        } else {
          setContent(data.content || [])
        }
        setTotalContent(data.pagination?.total || 0)
        setLastRefresh(new Date())
      }
    } catch (error) {
      console.error('Error fetching content:', error)
    } finally {
      setLoadingContent(false)
      setLoadingMore(false)
      if (isAutoRefresh) {
        setAutoRefreshing(false)
      }
    }
  }

  const fetchAvailableCreators = async () => {
    try {
      const response = await fetch('/api/user/preferences')
      if (response.ok) {
        const data = await response.json()
        setAvailableCreators(data.creators || [])
      }
    } catch (error) {
      console.error('Error fetching available creators:', error)
    }
  }

  const handleFiltersChange = (newFilters: FeedFiltersType) => {
    setFeedFilters(newFilters)
  }

  const fetchContentStats = async () => {
    try {
      const response = await fetch('/api/content/feed', { method: 'POST' })
      if (response.ok) {
        const data = await response.json()
        setContentStats(data.stats)
      }
    } catch (error) {
      console.error('Error fetching content stats:', error)
    }
  }

  const triggerContentAggregation = async () => {
    try {
      setLoadingContent(true)
      const response = await fetch('/api/content/aggregate', { method: 'POST' })
      if (response.ok) {
        const data = await response.json()
        console.log('Content aggregation result:', data)
        // Refresh content after aggregation
        await fetchContent()
        await fetchContentStats()
      }
    } catch (error) {
      console.error('Error triggering content aggregation:', error)
    } finally {
      setLoadingContent(false)
    }
  }

  const handleSignOut = async () => {
    await signOut()
    router.push('/')
  }

  const handleCreatorManagerClose = () => {
    setShowCreatorManager(false)
    // Refresh preferences to show updated creator count
    fetchPreferences()
  }

  const handleEditGenres = () => {
    setSelectedGenres(preferences?.preferences?.genres || [])
    setEditingGenres(true)
  }

  const toggleGenre = (genre: string) => {
    if (selectedGenres.includes(genre)) {
      setSelectedGenres(selectedGenres.filter(g => g !== genre))
    } else {
      setSelectedGenres([...selectedGenres, genre])
    }
  }

  const removeGenre = (genreToRemove: string) => {
    const updatedGenres = (preferences?.preferences?.genres || []).filter((g: string) => g !== genreToRemove)
    updateUserGenres(updatedGenres)
  }

  const saveGenres = async () => {
    setSavingGenres(true)
    await updateUserGenres(selectedGenres)
    setEditingGenres(false)
    setSavingGenres(false)
  }

  const updateUserGenres = async (newGenres: string[]) => {
    try {
      const response = await fetch('/api/user/preferences', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          genres: newGenres,
          creators: preferences?.creators?.map((c: any) => ({ platform: c.platform, handle: c.handle })) || [],
          streaming_services: preferences?.preferences?.streaming_services || []
        })
      })

      if (response.ok) {
        // Refresh preferences
        fetchPreferences()
      }
    } catch (error) {
      console.error('Error updating genres:', error)
    }
  }

  if (loading || loadingPrefs) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      {/* Header */}
      <header className="border-b border-white/20 bg-black/20 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Film className="h-8 w-8 text-purple-400" />
              <h1 className="text-2xl font-bold text-white">SceneSniffer</h1>
            </div>

            <div className="flex items-center gap-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/discover')}
                className="text-white border-white/30 hover:bg-white/10"
              >
                <Search className="h-4 w-4 mr-2" />
                Discover
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/watchlist')}
                className="text-white border-white/30 hover:bg-white/10"
              >
                <Bookmark className="h-4 w-4 mr-2" />
                My Watchlist
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => router.push('/bundles')}
                className="text-white border-white/30 hover:bg-white/10"
              >
                <Package className="h-4 w-4 mr-2" />
                Discover Bundles
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCreatorManager(true)}
                className="text-white border-white/30 hover:bg-white/10"
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Manage Creators
              </Button>
              <Button variant="outline" size="sm" className="text-white border-white/30 hover:bg-white/10">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
              <Button variant="outline" size="sm" className="text-white border-white/30 hover:bg-white/10">
                <Bell className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" className="text-white border-white/30 hover:bg-white/10">
                <Settings className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleSignOut}
                className="text-white border-white/30 hover:bg-white/10"
              >
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-white mb-2">
            Welcome back, {user.email?.split('@')[0]}! 👋
          </h2>
          <p className="text-gray-300 text-lg">
            Your personalized cinematic pulse is ready. Here's what's new from your favorite creators.
          </p>
        </div>

        {/* Currently Watching Section */}
        <CurrentlyWatchingSection className="mb-8" limit={6} />

        {/* User Stats */}
        {preferences && (
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card className="bg-white/10 border-white/20 text-white">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-2">
                    <Film className="h-5 w-5 text-purple-400" />
                    Genres
                  </div>
                  <Dialog open={editingGenres} onOpenChange={setEditingGenres}>
                    <DialogTrigger asChild>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={handleEditGenres}
                        className="text-purple-400 hover:bg-purple-400/20 h-8 w-8 p-0"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="bg-gray-900 border-gray-700 text-white max-w-2xl">
                      <DialogHeader>
                        <DialogTitle>Edit Your Genres</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <p className="text-gray-300 text-sm">
                          Select genres you enjoy. This helps us curate content that matches your taste.
                        </p>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                          {GENRES.map((genre) => {
                            const isSelected = selectedGenres.includes(genre)
                            return (
                              <Button
                                key={genre}
                                variant={isSelected ? "default" : "outline"}
                                onClick={() => toggleGenre(genre)}
                                className={`
                                  relative h-10 text-sm transition-all duration-200 justify-start
                                  ${isSelected
                                    ? 'bg-purple-600 hover:bg-purple-700 text-white border-purple-600'
                                    : 'bg-transparent border-white/30 text-white hover:bg-white/10 hover:border-white/50'
                                  }
                                `}
                              >
                                {isSelected && (
                                  <Check className="h-4 w-4 mr-2" />
                                )}
                                {genre}
                              </Button>
                            )
                          })}
                        </div>
                        <div className="flex justify-between items-center pt-4 border-t border-white/20">
                          <p className="text-sm text-gray-400">
                            Selected: {selectedGenres.length} genres
                          </p>
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              onClick={() => setEditingGenres(false)}
                              className="border-white/30 text-white hover:bg-white/10"
                            >
                              Cancel
                            </Button>
                            <Button
                              onClick={saveGenres}
                              disabled={savingGenres || selectedGenres.length === 0}
                              className="bg-purple-600 hover:bg-purple-700"
                            >
                              {savingGenres ? (
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              ) : null}
                              Save Changes
                            </Button>
                          </div>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold mb-1">
                  {preferences.preferences?.genres?.length || 0}
                </div>
                <p className="text-gray-300 text-sm">Selected genres</p>
                <div className="flex flex-wrap gap-1 mt-2">
                  {(showAllGenres
                    ? preferences.preferences?.genres
                    : preferences.preferences?.genres?.slice(0, 3)
                  )?.map((genre: string) => (
                    <button
                      key={genre}
                      onClick={() => removeGenre(genre)}
                      className="group px-2 py-1 bg-purple-600/20 text-purple-300 text-xs rounded hover:bg-red-500/20 hover:text-red-300 transition-colors cursor-pointer flex items-center gap-1"
                      title="Click to remove"
                    >
                      {genre}
                      <X className="h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity" />
                    </button>
                  ))}
                  {(preferences.preferences?.genres?.length || 0) > 3 && !showAllGenres && (
                    <button
                      onClick={() => setShowAllGenres(true)}
                      className="px-2 py-1 bg-gray-600/20 text-gray-300 text-xs rounded hover:bg-gray-500/20 transition-colors cursor-pointer"
                    >
                      +{(preferences.preferences?.genres?.length || 0) - 3}
                    </button>
                  )}
                  {showAllGenres && (preferences.preferences?.genres?.length || 0) > 3 && (
                    <button
                      onClick={() => setShowAllGenres(false)}
                      className="px-2 py-1 bg-gray-600/20 text-gray-300 text-xs rounded hover:bg-gray-500/20 transition-colors cursor-pointer"
                    >
                      Show less
                    </button>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card
              className="bg-white/10 border-white/20 text-white hover:bg-white/15 transition-colors cursor-pointer"
              onClick={() => setShowCreatorManager(true)}
            >
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-purple-400" />
                    Creators
                  </div>
                  <UserPlus className="h-4 w-4 text-gray-400" />
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold mb-1">
                  {preferences.creators?.length || 0}
                </div>
                <p className="text-gray-300 text-sm">Followed creators</p>
                <div className="space-y-1 mt-2">
                  {preferences.creators?.slice(0, 2).map((creator: any, index: number) => (
                    <div key={index} className="flex items-center gap-2">
                      <span className="text-xs text-gray-400 capitalize">{creator.platform}:</span>
                      <span className="text-white text-xs">{creator.handle}</span>
                    </div>
                  ))}
                  {(preferences.creators?.length || 0) > 2 && (
                    <p className="text-gray-400 text-xs">
                      +{(preferences.creators?.length || 0) - 2} more
                    </p>
                  )}
                </div>
                <p className="text-purple-300 text-xs mt-2 opacity-75">
                  Click to manage →
                </p>
              </CardContent>
            </Card>

            <Card className="bg-white/10 border-white/20 text-white">
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <svg className="h-5 w-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  Streaming
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold mb-1">
                  {preferences.preferences?.streaming_services?.length || 0}
                </div>
                <p className="text-gray-300 text-sm">Connected services</p>
              </CardContent>
            </Card>

            <Card
              className="bg-gradient-to-br from-purple-600/20 to-blue-600/20 border-purple-400/30 text-white hover:from-purple-600/30 hover:to-blue-600/30 transition-all cursor-pointer group"
              onClick={() => router.push('/bundles')}
            >
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center justify-between text-lg">
                  <div className="flex items-center gap-2">
                    <Package className="h-5 w-5 text-purple-400" />
                    Creator Bundles
                  </div>
                  <ExternalLink className="h-4 w-4 text-gray-400 group-hover:text-purple-400 transition-colors" />
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold mb-1 text-purple-300">
                  New
                </div>
                <p className="text-gray-300 text-sm mb-2">Curated collections</p>
                <div className="space-y-1">
                  <div className="flex items-center gap-1">
                    <span className="text-xs text-purple-400">👻</span>
                    <span className="text-xs text-gray-300">Horror Masters</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="text-xs text-purple-400">🚀</span>
                    <span className="text-xs text-gray-300">Sci-Fi Experts</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <span className="text-xs text-purple-400">🎭</span>
                    <span className="text-xs text-gray-300">Indie Cinema</span>
                  </div>
                </div>
                <p className="text-purple-300 text-xs mt-2 opacity-75 group-hover:opacity-100 transition-opacity">
                  Discover bundles →
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Content Feed Section */}
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <h2 className="text-2xl font-bold text-white">Your Personalized Feed</h2>
              {autoRefreshing && (
                <div className="flex items-center gap-2 text-sm text-gray-400">
                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-purple-400"></div>
                  <span>Auto-refreshing...</span>
                </div>
              )}
              {lastRefresh && !autoRefreshing && (
                <div className="text-xs text-gray-500">
                  Last updated: {lastRefresh.toLocaleTimeString()}
                </div>
              )}
              {content.length > 0 && (
                <div className="text-xs text-gray-400">
                  Showing {content.length}{totalContent > 0 && totalContent !== content.length ? ` of ${totalContent}` : ''} items
                </div>
              )}
            </div>
            <div className="flex gap-2">
              <RefreshWithProgress
                onRefreshComplete={() => {
                  // Refresh the feed when background job completes
                  fetchContent()
                  fetchContentStats()
                }}
              />
              <Button
                onClick={triggerContentAggregation}
                disabled={loadingContent}
                variant="outline"
                className="text-white border-white/30 hover:bg-white/10"
              >
                {loadingContent ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : (
                  <Plus className="mr-2 h-4 w-4" />
                )}
                Aggregate Content
              </Button>
              <Button
                onClick={() => router.push('/demo')}
                variant="outline"
                className="text-white border-white/30 hover:bg-white/10"
              >
                <Search className="mr-2 h-4 w-4" />
                Search Demo
              </Button>
            </div>
          </div>

          {/* Content Stats */}
          {contentStats && (
            <div className="grid md:grid-cols-4 gap-4 mb-6">
              <Card className="bg-white/10 border-white/20 text-white">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold">{contentStats.totalContent}</div>
                  <p className="text-gray-300 text-sm">Total Content</p>
                </CardContent>
              </Card>
              <Card className="bg-white/10 border-white/20 text-white">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold">{contentStats.recentContent}</div>
                  <p className="text-gray-300 text-sm">This Week</p>
                </CardContent>
              </Card>
              <Card className="bg-white/10 border-white/20 text-white">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold">{Object.keys(contentStats.contentByType || {}).length}</div>
                  <p className="text-gray-300 text-sm">Content Types</p>
                </CardContent>
              </Card>
              <Card className="bg-white/10 border-white/20 text-white">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold">{contentStats.topCreators?.length || 0}</div>
                  <p className="text-gray-300 text-sm">Active Creators</p>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Feed Filters */}
          <FeedFilters
            onFiltersChange={handleFiltersChange}
            availableCreators={availableCreators}
            className="mb-6"
          />

          {/* Content Feed */}
          {loadingContent ? (
            <div className="flex justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white"></div>
            </div>
          ) : content.length > 0 ? (
            <div>
              <div className="space-y-4">
                {content.map((item) => (
                <Card key={item.id} className="bg-white/10 border-white/20 text-white hover:bg-white/15 transition-colors cursor-pointer group">
                  <CardContent className="p-6">
                    <div className="flex gap-4" onClick={() => router.push(`/content/${item.id}`)}>
                      {/* Thumbnail */}
                      <div className="flex-shrink-0">
                        <img
                          src={item.thumbnail_url}
                          alt={item.title}
                          className="w-32 h-20 object-cover rounded-lg group-hover:opacity-90 transition-opacity"
                          onError={(e) => {
                            e.currentTarget.src = '/placeholder-video.jpg'
                          }}
                        />
                      </div>

                      {/* Content Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between mb-2">
                          <h3 className="text-lg font-semibold text-white line-clamp-2 pr-4 group-hover:text-purple-300 transition-colors">
                            {item.title}
                          </h3>
                          <div className="flex items-center gap-2 text-sm text-gray-300">
                            <Star className="h-4 w-4" />
                            <span>{item.relevanceScore}</span>
                          </div>
                        </div>

                        <div className="flex items-center gap-4 mb-3 text-sm text-gray-300">
                          <div className="flex items-center gap-1">
                            <Users className="h-4 w-4" />
                            <span>{item.creators?.name}</span>
                            {item.creators?.handle && (
                              <span className="text-gray-400">({item.creators.handle})</span>
                            )}
                            {item.creators?.verified && <span className="text-blue-400">✓</span>}
                          </div>
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            <span>{new Date(item.published_at).toLocaleDateString()}</span>
                          </div>

                          {/* Platform indicator */}
                          <div className="flex items-center gap-1">
                            {item.platform === 'youtube' && <Youtube className="h-4 w-4 text-red-500" />}
                            {item.platform === 'instagram' && <Instagram className="h-4 w-4 text-pink-500" />}
                            {item.platform === 'twitter' && <Twitter className="h-4 w-4 text-blue-500" />}
                            <span className="capitalize">{item.platform || 'YouTube'}</span>
                          </div>

                          <span className="px-2 py-1 bg-purple-600/20 text-purple-300 rounded text-xs">
                            {item.content_type}
                          </span>
                        </div>

                        {/* Platform-specific engagement metrics */}
                        {item.engagement_metrics && (
                          <div className="flex items-center gap-4 mb-3 text-xs text-gray-400">
                            {item.platform === 'youtube' && (
                              <>
                                {item.engagement_metrics.views && (
                                  <div className="flex items-center gap-1">
                                    <Eye className="h-3 w-3" />
                                    <span>{item.engagement_metrics.views.toLocaleString()}</span>
                                  </div>
                                )}
                                {item.engagement_metrics.likes && (
                                  <div className="flex items-center gap-1">
                                    <Heart className="h-3 w-3" />
                                    <span>{item.engagement_metrics.likes.toLocaleString()}</span>
                                  </div>
                                )}
                              </>
                            )}
                            {item.platform === 'instagram' && (
                              <>
                                {item.engagement_metrics.likes && (
                                  <div className="flex items-center gap-1">
                                    <Heart className="h-3 w-3" />
                                    <span>{item.engagement_metrics.likes.toLocaleString()}</span>
                                  </div>
                                )}
                                {item.engagement_metrics.comments && (
                                  <div className="flex items-center gap-1">
                                    <MessageCircle className="h-3 w-3" />
                                    <span>{item.engagement_metrics.comments.toLocaleString()}</span>
                                  </div>
                                )}
                              </>
                            )}
                            {item.platform === 'twitter' && (
                              <>
                                {item.engagement_metrics.retweets && (
                                  <div className="flex items-center gap-1">
                                    <Repeat2 className="h-3 w-3" />
                                    <span>{item.engagement_metrics.retweets.toLocaleString()}</span>
                                  </div>
                                )}
                                {item.engagement_metrics.likes && (
                                  <div className="flex items-center gap-1">
                                    <Heart className="h-3 w-3" />
                                    <span>{item.engagement_metrics.likes.toLocaleString()}</span>
                                  </div>
                                )}
                              </>
                            )}
                            {item.duration && (
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                <span>
                                  {item.duration >= 3600
                                    ? `${Math.floor(item.duration / 3600)}h ${Math.floor((item.duration % 3600) / 60)}m`
                                    : item.duration >= 60
                                    ? `${Math.floor(item.duration / 60)}m ${item.duration % 60}s`
                                    : `${item.duration}s`
                                  }
                                </span>
                              </div>
                            )}
                          </div>
                        )}

                        <p className="text-gray-300 text-sm line-clamp-2 mb-4">
                          {item.ai_summary || item.description}
                        </p>

                        <div className="flex items-center justify-between">
                          <div className="flex flex-wrap gap-1">
                            {item.tags?.slice(0, 3).map((tag: string) => (
                              <span key={tag} className="px-2 py-1 bg-gray-600/20 text-gray-300 rounded text-xs">
                                {tag}
                              </span>
                            ))}
                            {item.referenced_titles?.length > 0 && (
                              <span className="px-2 py-1 bg-blue-600/20 text-blue-300 rounded text-xs">
                                {item.referenced_titles.length} movie{item.referenced_titles.length !== 1 ? 's' : ''}
                              </span>
                            )}
                          </div>

                          <div className="flex gap-2">
                            <Button
                              onClick={(e) => {
                                e.stopPropagation()
                                window.open(item.platform_url, '_blank')
                              }}
                              size="sm"
                              variant="outline"
                              className="border-gray-600 text-gray-300 hover:bg-gray-600/20"
                            >
                              {item.platform === 'youtube' && <Play className="mr-1 h-3 w-3" />}
                              {item.platform === 'instagram' && <Instagram className="mr-1 h-3 w-3" />}
                              {item.platform === 'twitter' && <Twitter className="mr-1 h-3 w-3" />}
                              {!item.platform && <Play className="mr-1 h-3 w-3" />}
                              {item.platform === 'youtube' ? 'Watch' :
                               item.platform === 'instagram' ? 'View Post' :
                               item.platform === 'twitter' ? 'View Tweet' : 'Watch'}
                              <ExternalLink className="ml-1 h-3 w-3" />
                            </Button>
                            <Button
                              onClick={(e) => {
                                e.stopPropagation()
                                router.push(`/content/${item.id}`)
                              }}
                              size="sm"
                              className="bg-purple-600 hover:bg-purple-700"
                            >
                              View Details
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                ))}
              </div>

              {/* Load More Button */}
              {content.length > 0 && content.length < totalContent && (
                <div className="mt-8 text-center">
                  <Button
                    onClick={() => {
                      const nextPage = Math.floor(content.length / itemsPerPage) + 1
                      setCurrentPage(nextPage)
                      fetchContent(false, nextPage, true)
                    }}
                    disabled={loadingMore}
                    className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-3"
                  >
                    {loadingMore ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Loading More...
                      </>
                    ) : (
                      <>
                        Load More ({totalContent - content.length} remaining)
                      </>
                    )}
                  </Button>
                  <div className="mt-2 text-sm text-gray-400">
                    Showing {content.length} of {totalContent} items
                  </div>
                </div>
              )}
            </div>
          ) : (
            <Card className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 border-purple-400/30">
              <CardContent className="p-8 text-center">
                <Film className="h-12 w-12 text-purple-400 mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-white mb-2">No Content Yet</h3>
                <p className="text-gray-300 mb-4">
                  We haven't found any recent content from your followed creators yet.
                </p>
                <Button
                  onClick={triggerContentAggregation}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  <Search className="mr-2 h-4 w-4" />
                  Find Content
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Creator Manager Modal */}
      {showCreatorManager && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 rounded-lg border border-white/20 w-full max-w-4xl max-h-[90vh] overflow-hidden">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-white/20">
              <h2 className="text-2xl font-bold text-white">Manage Your Creators</h2>
              <Button
                variant="outline"
                size="sm"
                onClick={handleCreatorManagerClose}
                className="text-white border-white/30 hover:bg-white/10"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Modal Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <CreatorManager />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

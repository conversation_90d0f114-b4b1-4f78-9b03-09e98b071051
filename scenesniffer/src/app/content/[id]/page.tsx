'use client'

import { useEffect, useState } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
// import { WatchlistButtonCompact } from '@/components/watchlist/WatchlistButton'
import { Separator } from '@/components/ui/separator'
import { 
  Play, 
  ExternalLink, 
  Clock, 
  Star, 
  AlertTriangle, 
  CheckCircle, 
  Film,
  ArrowLeft,
  Bookmark,
  Share2,
  ThumbsUp,
  ThumbsDown,
  Meh,
  Shield
} from 'lucide-react'
import Link from 'next/link'

interface ContentAnalysis {
  contentType: 'roundup' | 'review' | 'theory' | 'news' | 'breakdown'
  referencedMovies: {
    title: string
    slug: string
    timestamp?: string
    sentiment: 'positive' | 'negative' | 'mixed' | 'neutral'
    confidence: number
  }[]
  spoilerLevel: 'spoiler-free' | 'minor-spoilers' | 'major-spoilers'
  keyTopics: string[]
  aiSummary: string
  targetAudience: string[]
  watchTime?: string
}

interface ContentDetail {
  id: string
  title: string
  description: string
  content_type: string
  platform_url: string
  thumbnail_url: string
  published_at: string
  ai_summary: string
  referenced_titles: string[]
  tags: string[]
  creators: {
    id: string
    name: string
    platform: string
    handle: string
    avatar_url: string
    trust_score: number
    verified: boolean
  }
  analysis?: ContentAnalysis
}

export default function ContentDetailPage() {
  const params = useParams()
  const router = useRouter()
  const [content, setContent] = useState<ContentDetail | null>(null)
  const [loading, setLoading] = useState(true)
  const [analyzing, setAnalyzing] = useState(false)
  const [relatedContent, setRelatedContent] = useState<any[]>([])

  useEffect(() => {
    if (params.id) {
      fetchContentDetail(params.id as string)
    }
  }, [params.id])

  const fetchContentDetail = async (contentId: string) => {
    try {
      setLoading(true)
      const response = await fetch(`/api/content/${contentId}`)
      const data = await response.json()
      
      if (data.success) {
        setContent(data.content)
        
        // Fetch related content
        if (data.content.creators?.id) {
          fetchRelatedContent(data.content.creators.id, contentId)
        }
      }
    } catch (error) {
      console.error('Error fetching content:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchRelatedContent = async (creatorId: string, excludeId: string) => {
    try {
      const response = await fetch(`/api/creators/${creatorId}/content?exclude=${excludeId}&limit=5`)
      const data = await response.json()
      
      if (data.success) {
        setRelatedContent(data.content)
      }
    } catch (error) {
      console.error('Error fetching related content:', error)
    }
  }

  const triggerAIAnalysis = async () => {
    if (!content) return
    
    try {
      setAnalyzing(true)
      const response = await fetch(`/api/content/${content.id}/analyze`, {
        method: 'POST'
      })
      const data = await response.json()
      
      if (data.success) {
        setContent(prev => prev ? { ...prev, analysis: data.analysis } : null)
      }
    } catch (error) {
      console.error('Error analyzing content:', error)
    } finally {
      setAnalyzing(false)
    }
  }

  const getSentimentIcon = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return <ThumbsUp className="h-4 w-4 text-green-500" />
      case 'negative': return <ThumbsDown className="h-4 w-4 text-red-500" />
      case 'mixed': return <Meh className="h-4 w-4 text-yellow-500" />
      default: return <Shield className="h-4 w-4 text-gray-500" />
    }
  }

  const getSpoilerIcon = (level: string) => {
    switch (level) {
      case 'spoiler-free': return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'minor-spoilers': return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'major-spoilers': return <AlertTriangle className="h-4 w-4 text-red-500" />
      default: return <Shield className="h-4 w-4 text-gray-500" />
    }
  }

  const getContentTypeBadge = (type: string) => {
    const colors = {
      'roundup': 'bg-purple-100 text-purple-800',
      'review': 'bg-blue-100 text-blue-800',
      'theory': 'bg-green-100 text-green-800',
      'news': 'bg-red-100 text-red-800',
      'breakdown': 'bg-orange-100 text-orange-800'
    }
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800'
  }

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  if (!content) {
    return (
      <div className="container mx-auto px-4 py-8">
        <Card>
          <CardContent className="p-8 text-center">
            <h2 className="text-xl font-semibold mb-2">Content Not Found</h2>
            <p className="text-gray-600 mb-4">The content you're looking for doesn't exist.</p>
            <Button onClick={() => router.back()}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-purple-800 to-indigo-900">
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Header */}
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="mb-4 text-white hover:bg-white/10"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Feed
          </Button>

          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-white">{content.title}</h1>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="border-white/20 text-white hover:bg-white/10">
                <Bookmark className="mr-2 h-4 w-4" />
                Save
              </Button>
              <Button variant="outline" size="sm" className="border-white/20 text-white hover:bg-white/10">
                <Share2 className="mr-2 h-4 w-4" />
                Share
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Creator Content Card */}
            <Card className="bg-white/10 border-white/20 text-white">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Avatar>
                      <AvatarImage src={content.creators?.avatar_url} />
                      <AvatarFallback className="bg-purple-600 text-white">
                        {content.creators?.name?.[0]}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-semibold text-white">{content.creators?.name}</h3>
                        {content.creators?.verified && (
                          <CheckCircle className="h-4 w-4 text-blue-400" />
                        )}
                      </div>
                      <p className="text-sm text-gray-300">
                        {content.creators?.platform} • Trust Score: {content.creators?.trust_score}/10
                      </p>
                    </div>
                  </div>
                  <Badge className={getContentTypeBadge(content.analysis?.contentType || content.content_type)}>
                    {content.analysis?.contentType || content.content_type}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                {/* Thumbnail Image */}
                {content.thumbnail_url && (
                  <div className="mb-4">
                    <div className="relative aspect-video rounded-lg overflow-hidden">
                      <img
                        src={content.thumbnail_url}
                        alt={content.title}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none'
                          e.currentTarget.nextElementSibling.style.display = 'flex'
                        }}
                      />
                      {/* Fallback placeholder */}
                      <div className="absolute inset-0 bg-gradient-to-br from-purple-600 to-purple-800 items-center justify-center hidden">
                        <Play className="h-16 w-16 text-white/60" />
                      </div>
                      {/* Play overlay */}
                      <div className="absolute inset-0 bg-black/40 flex items-center justify-center group-hover:bg-black/60 transition-colors">
                        <Button asChild className="bg-purple-600 hover:bg-purple-700">
                          <a href={content.platform_url} target="_blank" rel="noopener noreferrer">
                            <Play className="mr-2 h-4 w-4" />
                            Watch on {content.creators?.platform}
                            <ExternalLink className="ml-2 h-4 w-4" />
                          </a>
                        </Button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Description */}
                <p className="text-gray-300 mb-4">{content.description}</p>

                {/* Metadata */}
                <div className="flex items-center gap-4 text-sm text-gray-400">
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    {new Date(content.published_at).toLocaleDateString()}
                  </div>
                  {content.analysis?.watchTime && (
                    <div className="flex items-center gap-1">
                      <Play className="h-4 w-4" />
                      {content.analysis.watchTime}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* AI Analysis Card */}
            <Card className="bg-white/10 border-white/20 text-white">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2 text-white">
                    <Star className="h-5 w-5 text-yellow-400" />
                    AI Content Analysis
                  </CardTitle>
                  {!content.analysis && (
                    <Button
                      onClick={triggerAIAnalysis}
                      disabled={analyzing}
                      size="sm"
                      className="border-white/20 text-white hover:bg-white/10"
                      variant="outline"
                    >
                      {analyzing ? 'Analyzing...' : 'Analyze Content'}
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                {content.analysis ? (
                  <div className="space-y-4">
                    {/* Summary */}
                    <div>
                      <h4 className="font-medium mb-2 text-white">AI Summary</h4>
                      <p className="text-gray-300">{content.analysis.aiSummary}</p>
                    </div>

                    {/* Content Details */}
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium mb-2 flex items-center gap-2 text-white">
                          {getSpoilerIcon(content.analysis.spoilerLevel)}
                          Spoiler Level
                        </h4>
                        <Badge variant="outline" className="border-white/20 text-white">
                          {content.analysis.spoilerLevel.replace('-', ' ')}
                        </Badge>
                      </div>
                      <div>
                        <h4 className="font-medium mb-2 text-white">Target Audience</h4>
                        <div className="flex flex-wrap gap-1">
                          {content.analysis.targetAudience.map((audience, index) => (
                            <Badge key={index} variant="secondary" className="text-xs bg-purple-600 text-white">
                              {audience}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>

                    {/* Key Topics */}
                    <div>
                      <h4 className="font-medium mb-2 text-white">Key Topics</h4>
                      <div className="flex flex-wrap gap-1">
                        {content.analysis.keyTopics.map((topic, index) => (
                          <Badge key={index} variant="outline" className="text-xs border-white/20 text-white">
                            {topic}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-400">
                    <Star className="h-12 w-12 mx-auto mb-4 text-gray-500" />
                    <p>AI analysis not available yet</p>
                    <p className="text-sm">Click "Analyze Content" to generate insights</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Referenced Movies */}
            {(content.analysis?.referencedMovies?.length || content.referenced_titles?.length) && (
              <Card className="bg-white/10 border-white/20 text-white">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <Film className="h-5 w-5" />
                    Movies & Shows Discussed
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {content.analysis?.referencedMovies?.map((movie, index) => (
                      <Link
                        key={index}
                        href={`/movie/${movie.slug}`}
                        className="block"
                      >
                        <Card className="hover:shadow-md transition-shadow cursor-pointer bg-white/5 border-white/20">
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                              <div className="flex-1">
                                <h4 className="font-medium text-white">{movie.title}</h4>
                                {movie.timestamp && (
                                  <p className="text-sm text-gray-300">
                                    Discussed at {movie.timestamp}
                                  </p>
                                )}
                              </div>
                              <div className="flex items-center gap-2">
                                {getSentimentIcon(movie.sentiment)}
                                <Badge variant="outline" className="text-xs border-white/20 text-white">
                                  {Math.round(movie.confidence * 100)}%
                                </Badge>
                                {/* Temporarily disabled watchlist button */}
                                {/* {movie.tmdb_id && (
                                  <WatchlistButtonCompact
                                    tmdbId={movie.tmdb_id}
                                    contentType="movie"
                                    title={movie.title}
                                  />
                                )} */}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </Link>
                    )) || content.referenced_titles?.map((title, index) => (
                      <Link
                        key={index}
                        href={`/movie/${title.toLowerCase().replace(/\s+/g, '-')}`}
                        className="block"
                      >
                        <Card className="hover:shadow-md transition-shadow cursor-pointer bg-white/5 border-white/20">
                          <CardContent className="p-4">
                            <div className="flex items-center justify-between">
                              <div>
                                <h4 className="font-medium text-white">{title}</h4>
                                <p className="text-sm text-gray-300">Click to explore</p>
                              </div>
                              {/* Note: We'd need TMDb ID to add watchlist functionality here */}
                            </div>
                          </CardContent>
                        </Card>
                      </Link>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
        </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Related Content */}
            {relatedContent.length > 0 && (
              <Card className="bg-white/10 border-white/20 text-white">
                <CardHeader>
                  <CardTitle className="text-white">More from {content.creators?.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {relatedContent.map((item) => (
                      <Link
                        key={item.id}
                        href={`/content/${item.id}`}
                        className="block"
                      >
                        <div className="p-3 rounded-lg border border-white/20 hover:bg-white/10 transition-colors">
                          <h4 className="font-medium text-sm mb-1 text-white">{item.title}</h4>
                          <p className="text-xs text-gray-300">
                            {new Date(item.published_at).toLocaleDateString()}
                          </p>
                        </div>
                      </Link>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

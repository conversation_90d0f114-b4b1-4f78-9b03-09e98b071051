'use client'

import { useState } from 'react'
import { CreatorBundle } from '@/types'
import { BundleCard } from './bundle-card'
import { BundleDetail } from './bundle-detail'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Loader2, Package, RefreshCw, Filter, Search, X, SlidersHorizontal, Grid, List } from 'lucide-react'

interface BundleGridProps {
  bundles: CreatorBundle[]
  subscribedBundleIds?: string[]
  loading?: boolean
  error?: string | null
  onAddBundle?: (bundleId: string) => Promise<void>
  onLoadMore?: () => void
  onRefresh?: () => void
  hasMore?: boolean
  showFilters?: boolean
  onFilterChange?: (filters: any) => void
  currentFilters?: any
}

export function BundleGrid({
  bundles,
  subscribedBundleIds = [],
  loading = false,
  error = null,
  onAddBundle,
  onLoadMore,
  onRefresh,
  hasMore = false,
  showFilters = false,
  onFilterChange,
  currentFilters = {}
}: BundleGridProps) {
  const [selectedBundle, setSelectedBundle] = useState<CreatorBundle | null>(null)
  const [addingBundles, setAddingBundles] = useState<Set<string>>(new Set())
  const [searchQuery, setSearchQuery] = useState('')
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [sortBy, setSortBy] = useState('newest')

  // Available themes extracted from bundles
  const availableThemes = Array.from(new Set(
    bundles.flatMap(bundle => bundle.themes || [])
  )).sort()

  // Filter bundles based on search and filters
  const filteredBundles = bundles.filter(bundle => {
    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      const matchesTitle = bundle.title.toLowerCase().includes(query)
      const matchesDescription = bundle.description?.toLowerCase().includes(query)
      const matchesThemes = bundle.themes?.some(theme =>
        theme.toLowerCase().includes(query)
      )
      if (!matchesTitle && !matchesDescription && !matchesThemes) {
        return false
      }
    }

    // Theme filter
    if (currentFilters.theme && currentFilters.theme !== 'all') {
      if (!bundle.themes?.includes(currentFilters.theme)) {
        return false
      }
    }

    return true
  })

  // Sort filtered bundles
  const sortedBundles = [...filteredBundles].sort((a, b) => {
    switch (sortBy) {
      case 'newest':
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
      case 'oldest':
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
      case 'name':
        return a.title.localeCompare(b.title)
      case 'creators':
        return (b.creator_ids?.length || 0) - (a.creator_ids?.length || 0)
      default:
        return 0
    }
  })

  const handleAddBundle = async (bundleId: string) => {
    if (!onAddBundle) return
    
    setAddingBundles(prev => new Set(prev).add(bundleId))
    try {
      await onAddBundle(bundleId)
    } finally {
      setAddingBundles(prev => {
        const newSet = new Set(prev)
        newSet.delete(bundleId)
        return newSet
      })
    }
  }

  const handleViewBundle = (bundleId: string) => {
    const bundle = bundles.find(b => b.id === bundleId)
    if (bundle) {
      setSelectedBundle(bundle)
    }
  }

  const isSubscribed = (bundleId: string) => {
    return subscribedBundleIds.includes(bundleId)
  }

  const isAdding = (bundleId: string) => {
    return addingBundles.has(bundleId)
  }

  if (error) {
    return (
      <Card className="bg-red-600/20 border-red-400/30">
        <CardContent className="p-6 text-center">
          <div className="text-red-300 mb-4">
            <Package className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <h3 className="text-lg font-semibold">Error Loading Bundles</h3>
            <p className="text-sm">{error}</p>
          </div>
          {onRefresh && (
            <Button
              onClick={onRefresh}
              variant="outline"
              className="text-red-300 border-red-400/30 hover:bg-red-600/10"
            >
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <>
      <div className="space-y-6">
        {/* Search and Filters */}
        {showFilters && (
          <div className="space-y-4">
            {/* Search Bar */}
            <Card className="bg-white/10 border-white/20">
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search bundles by name, theme, or description..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 bg-gray-800/50 border-gray-600 text-white placeholder-gray-400"
                    />
                    {searchQuery && (
                      <button
                        onClick={() => setSearchQuery('')}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    )}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                    className="text-white border-white/30 hover:bg-white/10"
                  >
                    <SlidersHorizontal className="h-4 w-4 mr-2" />
                    Filters
                  </Button>

                  <div className="flex items-center gap-1 border border-white/30 rounded-md p-1">
                    <Button
                      variant={viewMode === 'grid' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('grid')}
                      className="h-8 w-8 p-0"
                    >
                      <Grid className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewMode === 'list' ? 'default' : 'ghost'}
                      size="sm"
                      onClick={() => setViewMode('list')}
                      className="h-8 w-8 p-0"
                    >
                      <List className="h-4 w-4" />
                    </Button>
                  </div>

                  {onRefresh && (
                    <Button
                      onClick={onRefresh}
                      variant="outline"
                      size="sm"
                      className="text-white border-white/30 hover:bg-white/10"
                    >
                      <RefreshCw className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Advanced Filters */}
            {showAdvancedFilters && (
              <Card className="bg-white/10 border-white/20">
                <CardContent className="p-4">
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    {/* Platform Filter */}
                    <div>
                      <label className="text-sm font-medium text-white mb-2 block">Platform</label>
                      <select
                        value={currentFilters.platform || 'youtube'}
                        onChange={(e) => onFilterChange?.({ ...currentFilters, platform: e.target.value })}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded text-white text-sm"
                      >
                        <option value="youtube">YouTube</option>
                        <option value="instagram">Instagram</option>
                        <option value="twitter">Twitter</option>
                        <option value="tiktok">TikTok</option>
                        <option value="twitch">Twitch</option>
                      </select>
                    </div>

                    {/* Theme Filter */}
                    <div>
                      <label className="text-sm font-medium text-white mb-2 block">Theme</label>
                      <select
                        value={currentFilters.theme || 'all'}
                        onChange={(e) => onFilterChange?.({ ...currentFilters, theme: e.target.value })}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded text-white text-sm"
                      >
                        <option value="all">All Themes</option>
                        {availableThemes.map(theme => (
                          <option key={theme} value={theme}>{theme}</option>
                        ))}
                      </select>
                    </div>

                    {/* Sort By */}
                    <div>
                      <label className="text-sm font-medium text-white mb-2 block">Sort By</label>
                      <select
                        value={sortBy}
                        onChange={(e) => setSortBy(e.target.value)}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded text-white text-sm"
                      >
                        <option value="newest">Newest First</option>
                        <option value="oldest">Oldest First</option>
                        <option value="name">Name A-Z</option>
                        <option value="creators">Most Creators</option>
                      </select>
                    </div>

                    {/* Status Filter */}
                    <div>
                      <label className="text-sm font-medium text-white mb-2 block">Status</label>
                      <select
                        value={currentFilters.status || 'active'}
                        onChange={(e) => onFilterChange?.({ ...currentFilters, status: e.target.value })}
                        className="w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded text-white text-sm"
                      >
                        <option value="active">Active</option>
                        <option value="draft">Draft</option>
                        <option value="inactive">Inactive</option>
                      </select>
                    </div>
                  </div>

                  {/* Active Filters */}
                  {(searchQuery || currentFilters.theme !== 'all') && (
                    <div className="mt-4 pt-4 border-t border-white/20">
                      <div className="flex items-center gap-2 flex-wrap">
                        <span className="text-sm text-gray-300">Active filters:</span>
                        {searchQuery && (
                          <Badge
                            variant="secondary"
                            className="bg-purple-600/20 text-purple-300 hover:bg-purple-600/30"
                          >
                            Search: "{searchQuery}"
                            <button
                              onClick={() => setSearchQuery('')}
                              className="ml-1 hover:text-white"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </Badge>
                        )}
                        {currentFilters.theme && currentFilters.theme !== 'all' && (
                          <Badge
                            variant="secondary"
                            className="bg-blue-600/20 text-blue-300 hover:bg-blue-600/30"
                          >
                            Theme: {currentFilters.theme}
                            <button
                              onClick={() => onFilterChange?.({ ...currentFilters, theme: 'all' })}
                              className="ml-1 hover:text-white"
                            >
                              <X className="h-3 w-3" />
                            </button>
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Results Summary */}
            <div className="flex items-center justify-between text-sm text-gray-300">
              <span>
                {searchQuery || currentFilters.theme !== 'all'
                  ? `${sortedBundles.length} of ${bundles.length} bundles`
                  : `${bundles.length} bundles`
                }
              </span>
              {availableThemes.length > 0 && (
                <span>{availableThemes.length} themes available</span>
              )}
            </div>
          </div>
        )}

        {/* Bundle Grid/List */}
        {sortedBundles.length === 0 && !loading ? (
          <Card className="bg-white/10 border-white/20">
            <CardContent className="p-12 text-center">
              <Package className="h-16 w-16 text-gray-400 mx-auto mb-4 opacity-50" />
              <h3 className="text-xl font-semibold text-white mb-2">
                {searchQuery || currentFilters.theme !== 'all' ? 'No Matching Bundles' : 'No Bundles Found'}
              </h3>
              <p className="text-gray-300 mb-4">
                {searchQuery || currentFilters.theme !== 'all'
                  ? 'Try adjusting your search or filters to find more bundles.'
                  : 'No creator bundles are available for the selected platform.'
                }
              </p>
              {(searchQuery || currentFilters.theme !== 'all') && (
                <Button
                  onClick={() => {
                    setSearchQuery('')
                    onFilterChange?.({ ...currentFilters, theme: 'all' })
                  }}
                  variant="outline"
                  className="text-white border-white/30 hover:bg-white/10 mr-2"
                >
                  Clear Filters
                </Button>
              )}
              {onRefresh && (
                <Button
                  onClick={onRefresh}
                  variant="outline"
                  className="text-white border-white/30 hover:bg-white/10"
                >
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Refresh
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className={
            viewMode === 'grid'
              ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
              : "space-y-4"
          }>
            {sortedBundles.map((bundle) => (
              <BundleCard
                key={bundle.id}
                bundle={bundle}
                isSubscribed={isSubscribed(bundle.id)}
                onAdd={handleAddBundle}
                onView={handleViewBundle}
                loading={isAdding(bundle.id)}
                compact={viewMode === 'list'}
              />
            ))}
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-purple-400" />
            <span className="ml-3 text-gray-300">Loading bundles...</span>
          </div>
        )}

        {/* Load More */}
        {hasMore && !loading && (
          <div className="text-center">
            <Button
              onClick={onLoadMore}
              variant="outline"
              className="text-white border-white/30 hover:bg-white/10"
            >
              Load More Bundles
            </Button>
          </div>
        )}

        {/* Stats */}
        {sortedBundles.length > 0 && (
          <div className="text-center text-sm text-gray-400">
            Showing {sortedBundles.length} bundle{sortedBundles.length !== 1 ? 's' : ''}
            {searchQuery || currentFilters.theme !== 'all' ? ` of ${bundles.length} total` : ''}
            {subscribedBundleIds.length > 0 && (
              <span> • {subscribedBundleIds.length} subscribed</span>
            )}
          </div>
        )}
      </div>

      {/* Bundle Detail Modal */}
      {selectedBundle && (
        <BundleDetail
          bundle={selectedBundle}
          isSubscribed={isSubscribed(selectedBundle.id)}
          onAdd={handleAddBundle}
          onClose={() => setSelectedBundle(null)}
          loading={isAdding(selectedBundle.id)}
        />
      )}
    </>
  )
}

'use client'

import { useState } from 'react'
import { CreatorBundle } from '@/types'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Users, Clock, Plus, Check, Eye, Calendar } from 'lucide-react'
// import { useBundleAnalytics, useBundleViewTracking } from '@/hooks/useBundleAnalytics'

interface BundleCardProps {
  bundle: CreatorBundle
  isSubscribed?: boolean
  onAdd?: (bundleId: string) => Promise<void>
  onView?: (bundleId: string) => void
  loading?: boolean
  compact?: boolean
}

export function BundleCard({
  bundle,
  isSubscribed = false,
  onAdd,
  onView,
  loading = false,
  compact = false
}: BundleCardProps) {
  const [isAdding, setIsAdding] = useState(false)
  // const { trackBundleAdd, trackInteraction } = useBundleAnalytics()

  // Automatically track view when component mounts
  // useBundleViewTracking(bundle.id)

  const handleAdd = async () => {
    if (!onAdd || isSubscribed) return

    setIsAdding(true)
    try {
      await onAdd(bundle.id)
      // Track bundle addition
      // await trackBundleAdd(bundle.id)
    } finally {
      setIsAdding(false)
    }
  }

  const handleView = () => {
    if (onView) {
      onView(bundle.id)
    }
    // Track detailed view interaction
    // trackInteraction(bundle.id, 'view', { source: 'bundle_card', action: 'detailed_view' })
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'youtube': return '📺'
      case 'tiktok': return '🎵'
      case 'instagram': return '📸'
      case 'twitter': return '🐦'
      case 'twitch': return '🎮'
      default: return '🎬'
    }
  }

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  return (
    <Card className={`bg-white/10 border-white/20 text-white hover:bg-white/15 transition-all duration-200 ${
      compact ? 'h-auto' : 'h-full'
    }`}>
      <CardHeader className={compact ? 'pb-3' : 'pb-4'}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className={`flex items-center gap-2 ${compact ? 'text-lg' : 'text-xl'}`}>
              <span className="text-2xl">{getPlatformIcon(bundle.platform)}</span>
              <span className="line-clamp-2">{bundle.title}</span>
            </CardTitle>
            {!compact && bundle.description && (
              <CardDescription className="text-gray-300 mt-2 line-clamp-2">
                {bundle.description}
              </CardDescription>
            )}
          </div>
        </div>

        {/* Tags */}
        {bundle.tags && bundle.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-2">
            {bundle.tags.slice(0, compact ? 3 : 5).map((tag) => (
              <Badge 
                key={tag} 
                className="text-xs bg-purple-600/20 text-purple-300 border-purple-400/30"
              >
                {tag}
              </Badge>
            ))}
            {bundle.tags.length > (compact ? 3 : 5) && (
              <Badge className="text-xs bg-gray-600/20 text-gray-400">
                +{bundle.tags.length - (compact ? 3 : 5)}
              </Badge>
            )}
          </div>
        )}
      </CardHeader>

      <CardContent className={compact ? 'pt-0' : ''}>
        {/* Stats */}
        <div className="flex items-center gap-4 text-sm text-gray-300 mb-4">
          <div className="flex items-center gap-1">
            <Users className="h-4 w-4" />
            <span>{bundle.creator_count} creators</span>
          </div>
          <div className="flex items-center gap-1">
            <Clock className="h-4 w-4" />
            <span>Updated {getTimeAgo(bundle.refreshed_at)}</span>
          </div>
          <div className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            <span className="capitalize">{bundle.platform}</span>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center gap-2">
          {isSubscribed ? (
            <Button
              disabled
              className="flex-1 bg-green-600/20 text-green-300 border-green-400/30 cursor-not-allowed"
            >
              <Check className="mr-2 h-4 w-4" />
              Added
            </Button>
          ) : (
            <Button
              onClick={handleAdd}
              disabled={isAdding || loading}
              className="flex-1 bg-purple-600 hover:bg-purple-700 disabled:opacity-50"
            >
              {isAdding ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  Adding...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Bundle
                </>
              )}
            </Button>
          )}
          
          <Button
            onClick={handleView}
            variant="outline"
            size="sm"
            className="text-white border-white/30 hover:bg-white/10"
          >
            <Eye className="h-4 w-4" />
          </Button>
        </div>

        {/* Bundle Status */}
        {bundle.status !== 'active' && (
          <div className="mt-2">
            <Badge 
              className={`text-xs ${
                bundle.status === 'draft' 
                  ? 'bg-yellow-600/20 text-yellow-300' 
                  : 'bg-gray-600/20 text-gray-400'
              }`}
            >
              {bundle.status}
            </Badge>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

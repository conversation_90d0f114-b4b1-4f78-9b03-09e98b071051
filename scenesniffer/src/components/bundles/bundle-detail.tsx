'use client'

import { useState, useEffect } from 'react'
import { CreatorBundle, BundleCreator } from '@/types'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { X, Users, Clock, Plus, Check, Star, ExternalLink, Calendar, Loader2 } from 'lucide-react'
// import { useBundleAnalytics } from '@/hooks/useBundleAnalytics'

interface BundleDetailProps {
  bundle: CreatorBundle
  isSubscribed?: boolean
  onAdd?: (bundleId: string) => Promise<void>
  onClose: () => void
  loading?: boolean
}

export function BundleDetail({
  bundle,
  isSubscribed = false,
  onAdd,
  onClose,
  loading = false
}: BundleDetailProps) {
  const [bundleDetails, setBundleDetails] = useState<CreatorBundle | null>(null)
  const [detailsLoading, setDetailsLoading] = useState(false)
  const [isAdding, setIsAdding] = useState(false)
  // const { trackBundleAdd, trackCreatorClick, trackInteraction } = useBundleAnalytics()

  // Fetch detailed bundle information
  useEffect(() => {
    const fetchBundleDetails = async () => {
      setDetailsLoading(true)
      try {
        const response = await fetch(`/api/bundles/${bundle.id}`)
        const data = await response.json()
        
        if (data.success) {
          setBundleDetails(data.bundle)
        }
      } catch (error) {
        console.error('Error fetching bundle details:', error)
      } finally {
        setDetailsLoading(false)
      }
    }

    fetchBundleDetails()
  }, [bundle.id])

  const handleAdd = async () => {
    if (!onAdd || isSubscribed) return

    setIsAdding(true)
    try {
      await onAdd(bundle.id)
      // Track bundle addition from detail view
      // await trackBundleAdd(bundle.id)
      // await trackInteraction(bundle.id, 'add', {
      //   source: 'bundle_detail',
      //   creator_count: bundle.creator_count
      // })
    } finally {
      setIsAdding(false)
    }
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'youtube': return '📺'
      case 'tiktok': return '🎵'
      case 'instagram': return '📸'
      case 'twitter': return '🐦'
      case 'twitch': return '🎮'
      default: return '🎬'
    }
  }

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 24) {
      return `${diffInHours}h ago`
    } else {
      const diffInDays = Math.floor(diffInHours / 24)
      return `${diffInDays}d ago`
    }
  }

  const getCreatorUrl = (creator: any) => {
    const platform = creator.platform || bundle.platform
    switch (platform) {
      case 'youtube':
        return `https://youtube.com/@${creator.handle}`
      case 'instagram':
        return `https://instagram.com/${creator.handle}`
      case 'twitter':
        return `https://twitter.com/${creator.handle}`
      case 'tiktok':
        return `https://tiktok.com/@${creator.handle}`
      case 'twitch':
        return `https://twitch.tv/${creator.handle}`
      default:
        return '#'
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 rounded-lg border border-white/20 w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b border-white/20">
          <div className="flex items-center gap-3">
            <span className="text-3xl">{getPlatformIcon(bundle.platform)}</span>
            <div>
              <h2 className="text-2xl font-bold text-white">{bundle.title}</h2>
              <p className="text-gray-300 text-sm capitalize">{bundle.platform} Bundle</p>
            </div>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={onClose}
            className="text-white border-white/30 hover:bg-white/10"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        {/* Modal Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <div className="space-y-6">
            {/* Bundle Info */}
            <div>
              {bundle.description && (
                <p className="text-gray-300 text-lg mb-4">{bundle.description}</p>
              )}
              
              {/* Stats */}
              <div className="flex items-center gap-6 text-sm text-gray-300 mb-4">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  <span>{bundle.creator_count} creators</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <span>Updated {getTimeAgo(bundle.refreshed_at)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>Created {getTimeAgo(bundle.created_at)}</span>
                </div>
              </div>

              {/* Tags */}
              {bundle.tags && bundle.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-6">
                  {bundle.tags.map((tag) => (
                    <Badge 
                      key={tag} 
                      className="bg-purple-600/20 text-purple-300 border-purple-400/30"
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}

              {/* Add Bundle Button */}
              <div className="mb-6">
                {isSubscribed ? (
                  <Button
                    disabled
                    className="bg-green-600/20 text-green-300 border-green-400/30 cursor-not-allowed"
                  >
                    <Check className="mr-2 h-4 w-4" />
                    Bundle Added
                  </Button>
                ) : (
                  <Button
                    onClick={handleAdd}
                    disabled={isAdding || loading}
                    className="bg-purple-600 hover:bg-purple-700 disabled:opacity-50"
                  >
                    {isAdding ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Adding Bundle...
                      </>
                    ) : (
                      <>
                        <Plus className="mr-2 h-4 w-4" />
                        Add All {bundle.creator_count} Creators
                      </>
                    )}
                  </Button>
                )}
              </div>
            </div>

            {/* Creators List */}
            <div>
              <h3 className="text-xl font-semibold text-white mb-4">
                Creators in this Bundle
              </h3>
              
              {detailsLoading ? (
                <div className="flex items-center justify-center py-8">
                  <Loader2 className="h-6 w-6 animate-spin text-purple-400" />
                  <span className="ml-2 text-gray-300">Loading creators...</span>
                </div>
              ) : bundleDetails?.creators && bundleDetails.creators.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {bundleDetails.creators.map((bundleCreator) => (
                    <Card 
                      key={bundleCreator.id} 
                      className="bg-gray-800/50 border-gray-600/30 text-white"
                    >
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h4 className="font-medium text-white">
                                {bundleCreator.creator?.name || 'Unknown Creator'}
                              </h4>
                              {bundleCreator.creator?.verified && (
                                <Badge className="bg-blue-600/20 text-blue-300 text-xs">
                                  ✓
                                </Badge>
                              )}
                            </div>
                            <p className="text-sm text-gray-400">
                              @{bundleCreator.creator?.handle || 'unknown'}
                            </p>
                            <div className="flex items-center gap-4 mt-2 text-xs text-gray-400">
                              <div className="flex items-center gap-1">
                                <Star className="h-3 w-3 text-yellow-400" />
                                <span>
                                  {bundleCreator.creator?.trust_score || 0}/10
                                </span>
                              </div>
                              {bundleCreator.creator?.follower_count && (
                                <div>
                                  {(bundleCreator.creator.follower_count / 1000).toFixed(0)}K followers
                                </div>
                              )}
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Badge className="bg-purple-600/20 text-purple-300 text-xs">
                              #{bundleCreator.position + 1}
                            </Badge>
                            <Button
                              size="sm"
                              variant="outline"
                              className="text-gray-300 border-gray-600 hover:bg-gray-700"
                              onClick={() => {
                                if (bundleCreator.creator) {
                                  // Track creator click
                                  // trackCreatorClick(bundle.id, bundleCreator.creator.id)
                                  window.open(getCreatorUrl(bundleCreator.creator), '_blank')
                                }
                              }}
                            >
                              <ExternalLink className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-400">
                  <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>No creators found in this bundle</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

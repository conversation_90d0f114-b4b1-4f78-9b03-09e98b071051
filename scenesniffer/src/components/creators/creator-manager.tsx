'use client'

import { useState } from 'react'
import { useCreators } from '@/hooks/use-creators'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Plus, Minus, Users, Star, ExternalLink, Loader2, AlertCircle } from 'lucide-react'

interface AddCreatorFormProps {
  onAdd: (platform: string, handle: string, name?: string) => Promise<void>
  loading: boolean
}

function AddCreatorForm({ onAdd, loading }: AddCreatorFormProps) {
  const [platform, setPlatform] = useState('youtube')
  const [handle, setHandle] = useState('')
  const [name, setName] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!handle.trim()) return
    
    await onAdd(platform, handle.trim(), name.trim() || undefined)
    setHandle('')
    setName('')
  }

  return (
    <Card className="bg-white/10 border-white/20 text-white">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Plus className="h-5 w-5" />
          Follow New Creator
        </CardTitle>
        <CardDescription className="text-gray-300">
          Add creators you trust for movie and TV recommendations
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Platform
            </label>
            <select
              value={platform}
              onChange={(e) => setPlatform(e.target.value)}
              className="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white"
            >
              <option value="youtube">YouTube</option>
              <option value="tiktok">TikTok</option>
              <option value="instagram">Instagram</option>
              <option value="twitter">Twitter</option>
              <option value="twitch">Twitch</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Handle/Username *
            </label>
            <input
              type="text"
              value={handle}
              onChange={(e) => setHandle(e.target.value)}
              placeholder="@username or channel name"
              className="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white placeholder-gray-400"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Display Name (optional)
            </label>
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Creator's display name"
              className="w-full p-2 bg-gray-800 border border-gray-600 rounded-md text-white placeholder-gray-400"
            />
          </div>
          
          <Button
            type="submit"
            disabled={!handle.trim() || loading}
            className="w-full bg-purple-600 hover:bg-purple-700"
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Following...
              </>
            ) : (
              <>
                <Plus className="mr-2 h-4 w-4" />
                Follow Creator
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}

export function CreatorManager() {
  const {
    creators,
    loading,
    error,
    addCreator,
    removeCreator,
    totalCreators,
    platformCounts
  } = useCreators()
  
  const [addingCreator, setAddingCreator] = useState(false)

  const handleAddCreator = async (platform: string, handle: string, name?: string) => {
    setAddingCreator(true)
    try {
      const result = await addCreator({ platform, handle, name })
      if (result.success) {
        console.log('Successfully followed creator:', result.creator)
      }
    } finally {
      setAddingCreator(false)
    }
  }

  const handleRemoveCreator = async (creatorId: string) => {
    await removeCreator(creatorId)
  }

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'youtube': return '📺'
      case 'tiktok': return '🎵'
      case 'instagram': return '📸'
      case 'twitter': return '🐦'
      case 'twitch': return '🎮'
      default: return '🎬'
    }
  }

  return (
    <div className="space-y-6">
      {/* Stats */}
      <Card className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 border-purple-400/30">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-white">Following {totalCreators} Creators</h3>
              <p className="text-gray-300 text-sm">
                Across {Object.keys(platformCounts).length} platforms
              </p>
            </div>
            <Users className="h-8 w-8 text-purple-400" />
          </div>
          
          {Object.keys(platformCounts).length > 0 && (
            <div className="mt-4 flex flex-wrap gap-2">
              {Object.entries(platformCounts).map(([platform, count]) => (
                <Badge key={platform} className="bg-purple-600/20 text-purple-300">
                  {getPlatformIcon(platform)} {platform}: {count}
                </Badge>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Creator Form */}
      <AddCreatorForm onAdd={handleAddCreator} loading={addingCreator} />

      {/* Error Display */}
      {error && (
        <Card className="bg-red-600/20 border-red-400/30">
          <CardContent className="p-4">
            <div className="flex items-center gap-2 text-red-300">
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Creators List */}
      <Card className="bg-white/10 border-white/20 text-white">
        <CardHeader>
          <CardTitle>Your Followed Creators</CardTitle>
          <CardDescription className="text-gray-300">
            Manage the creators you follow for recommendations
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin text-purple-400" />
              <span className="ml-2 text-gray-300">Loading creators...</span>
            </div>
          ) : creators.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No creators followed yet</p>
              <p className="text-sm">Add some creators above to get started!</p>
            </div>
          ) : (
            <div className="space-y-3">
              {creators.map((creator) => (
                <div
                  key={creator.id}
                  className="flex items-center justify-between p-4 bg-gray-800/50 rounded-lg border border-gray-600/30"
                >
                  <div className="flex items-center gap-3">
                    <div className="text-2xl">
                      {getPlatformIcon(creator.platform)}
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium text-white">{creator.name}</h4>
                        {creator.verified && (
                          <Badge className="bg-blue-600/20 text-blue-300 text-xs">
                            ✓ Verified
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-400">
                        @{creator.handle} • {creator.platform}
                      </p>
                      <div className="flex items-center gap-2 mt-1">
                        <Star className="h-3 w-3 text-yellow-400" />
                        <span className="text-xs text-gray-400">
                          Trust Score: {creator.trust_score}/10
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-gray-300 border-gray-600 hover:bg-gray-700"
                      onClick={() => {
                        const url = creator.platform === 'youtube' 
                          ? `https://youtube.com/@${creator.handle}`
                          : `https://${creator.platform}.com/${creator.handle}`
                        window.open(url, '_blank')
                      }}
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      className="text-red-300 border-red-600 hover:bg-red-700/20"
                      onClick={() => handleRemoveCreator(creator.id)}
                    >
                      <Minus className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

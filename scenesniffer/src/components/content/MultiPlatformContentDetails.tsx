'use client'

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { 
  ExternalLink, 
  Play, 
  Heart, 
  MessageCircle, 
  Share, 
  Clock,
  Eye,
  Repeat2,
  User,
  Calendar,
  Tag,
  Star
} from 'lucide-react'
import { useContentTracking } from '@/hooks/useContentTracking'

interface MultiPlatformContentDetailsProps {
  contentId: string
  onClose?: () => void
}

interface ContentDetails {
  id: string
  title: string
  description: string
  content_type: string
  platform: 'youtube' | 'instagram' | 'twitter'
  platform_url: string
  platform_id: string
  thumbnail_url?: string
  published_at: string
  ai_summary?: string
  referenced_titles: string[]
  tags: string[]
  media_type?: string
  media_urls: string[]
  duration?: number
  engagement_metrics: Record<string, any>
  content_metadata: Record<string, any>
  ai_extracted_text?: string
  ai_content_quality_score: number
  ai_topics: string[]
  creator_name: string
  creator_handle: string
  creator_avatar?: string
  creator_verified: boolean
  creator_trust_score: number
  display_type: string
  engagement_summary: string
  duration_display?: string
  platform_specific_data: Record<string, any>
}

export function MultiPlatformContentDetails({ contentId, onClose }: MultiPlatformContentDetailsProps) {
  const [content, setContent] = useState<ContentDetails | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeMediaIndex, setActiveMediaIndex] = useState(0)

  const { trackView, trackClick, trackShare } = useContentTracking({
    contentId,
    contentTitle: content?.title,
    autoTrackView: true
  })

  useEffect(() => {
    fetchContentDetails()
  }, [contentId])

  const fetchContentDetails = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/content/${contentId}/details`)
      
      if (!response.ok) {
        throw new Error('Failed to fetch content details')
      }
      
      const data = await response.json()
      setContent(data.content)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  const handleExternalClick = () => {
    trackClick({ action: 'external_link', platform: content?.platform })
    window.open(content?.platform_url, '_blank')
  }

  const handleShare = async () => {
    if (!content) return
    
    try {
      await navigator.share({
        title: content.title,
        text: content.ai_summary || content.description,
        url: content.platform_url
      })
      trackShare('native')
    } catch (err) {
      // Fallback to clipboard
      navigator.clipboard.writeText(content.platform_url)
      trackShare('clipboard')
    }
  }

  const renderPlatformSpecificContent = () => {
    if (!content) return null

    switch (content.platform) {
      case 'youtube':
        return (
          <div className="space-y-4">
            {/* YouTube Video Embed */}
            <div className="aspect-video bg-black rounded-lg overflow-hidden">
              <iframe
                src={`https://www.youtube.com/embed/${content.platform_id}`}
                title={content.title}
                className="w-full h-full"
                allowFullScreen
              />
            </div>
            
            {/* YouTube-specific metadata */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Eye className="h-4 w-4 text-gray-400" />
                <span>{content.engagement_metrics.views?.toLocaleString() || '0'} views</span>
              </div>
              <div className="flex items-center gap-2">
                <Heart className="h-4 w-4 text-gray-400" />
                <span>{content.engagement_metrics.likes?.toLocaleString() || '0'} likes</span>
              </div>
              <div className="flex items-center gap-2">
                <MessageCircle className="h-4 w-4 text-gray-400" />
                <span>{content.engagement_metrics.comments?.toLocaleString() || '0'} comments</span>
              </div>
              {content.duration_display && (
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <span>{content.duration_display}</span>
                </div>
              )}
            </div>
          </div>
        )

      case 'instagram':
        return (
          <div className="space-y-4">
            {/* Instagram Media Display */}
            {content.media_type === 'carousel' && content.media_urls.length > 1 ? (
              <div className="space-y-2">
                <div className="aspect-square bg-black rounded-lg overflow-hidden">
                  <img
                    src={content.media_urls[activeMediaIndex]}
                    alt={`${content.title} - ${activeMediaIndex + 1}`}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="flex gap-2 justify-center">
                  {content.media_urls.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setActiveMediaIndex(index)}
                      className={`w-2 h-2 rounded-full ${
                        index === activeMediaIndex ? 'bg-purple-600' : 'bg-gray-400'
                      }`}
                    />
                  ))}
                </div>
                <p className="text-sm text-gray-400 text-center">
                  {activeMediaIndex + 1} of {content.media_urls.length}
                </p>
              </div>
            ) : (
              <div className="aspect-square bg-black rounded-lg overflow-hidden">
                <img
                  src={content.thumbnail_url || content.media_urls[0]}
                  alt={content.title}
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            
            {/* Instagram-specific metadata */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Heart className="h-4 w-4 text-gray-400" />
                <span>{content.engagement_metrics.likes?.toLocaleString() || '0'} likes</span>
              </div>
              <div className="flex items-center gap-2">
                <MessageCircle className="h-4 w-4 text-gray-400" />
                <span>{content.engagement_metrics.comments?.toLocaleString() || '0'} comments</span>
              </div>
            </div>
            
            {/* Hashtags */}
            {content.content_metadata.hashtags && (
              <div className="flex flex-wrap gap-1">
                {content.content_metadata.hashtags.split(' ').map((hashtag: string, index: number) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {hashtag}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        )

      case 'twitter':
        return (
          <div className="space-y-4">
            {/* Twitter Thread Display */}
            {content.media_type === 'thread' ? (
              <div className="space-y-3">
                <Badge className="bg-blue-600">
                  Thread ({content.platform_specific_data.thread_length} tweets)
                </Badge>
                <div className="bg-gray-900 rounded-lg p-4 space-y-3">
                  {content.media_urls.map((tweetText: string, index: number) => (
                    <div key={index} className="border-l-2 border-blue-600 pl-3">
                      <p className="text-sm">{tweetText}</p>
                      {index < content.media_urls.length - 1 && (
                        <div className="h-2 w-px bg-blue-600 ml-2 mt-2" />
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="bg-gray-900 rounded-lg p-4">
                <p>{content.description}</p>
              </div>
            )}
            
            {/* Twitter-specific metadata */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Repeat2 className="h-4 w-4 text-gray-400" />
                <span>{content.engagement_metrics.retweets?.toLocaleString() || '0'} retweets</span>
              </div>
              <div className="flex items-center gap-2">
                <Heart className="h-4 w-4 text-gray-400" />
                <span>{content.engagement_metrics.likes?.toLocaleString() || '0'} likes</span>
              </div>
              <div className="flex items-center gap-2">
                <MessageCircle className="h-4 w-4 text-gray-400" />
                <span>{content.engagement_metrics.replies?.toLocaleString() || '0'} replies</span>
              </div>
              <div className="flex items-center gap-2">
                <Share className="h-4 w-4 text-gray-400" />
                <span>{content.engagement_metrics.quotes?.toLocaleString() || '0'} quotes</span>
              </div>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  if (loading) {
    return (
      <Card className="bg-gray-900 border-gray-700">
        <CardContent className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-700 rounded w-3/4" />
            <div className="h-32 bg-gray-700 rounded" />
            <div className="h-4 bg-gray-700 rounded w-1/2" />
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error || !content) {
    return (
      <Card className="bg-gray-900 border-gray-700">
        <CardContent className="p-6">
          <p className="text-red-400">Error loading content details: {error}</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="bg-gray-900 border-gray-700 text-white">
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Badge className={`
                ${content.platform === 'youtube' ? 'bg-red-600' : ''}
                ${content.platform === 'instagram' ? 'bg-pink-600' : ''}
                ${content.platform === 'twitter' ? 'bg-blue-600' : ''}
              `}>
                {content.display_type}
              </Badge>
              <Badge variant="outline">{content.content_type}</Badge>
              {content.ai_content_quality_score > 0.8 && (
                <Badge className="bg-yellow-600">
                  <Star className="h-3 w-3 mr-1" />
                  High Quality
                </Badge>
              )}
            </div>
            <CardTitle className="text-xl">{content.title}</CardTitle>
            
            {/* Creator Info */}
            <div className="flex items-center gap-3">
              <img
                src={content.creator_avatar || '/default-avatar.png'}
                alt={content.creator_name}
                className="w-8 h-8 rounded-full"
              />
              <div>
                <p className="font-medium">{content.creator_name}</p>
                <p className="text-sm text-gray-400">{content.creator_handle}</p>
              </div>
              {content.creator_verified && (
                <Badge variant="secondary" className="text-xs">Verified</Badge>
              )}
            </div>
          </div>
          
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              ×
            </Button>
          )}
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Platform-specific content */}
        {renderPlatformSpecificContent()}

        {/* AI Summary */}
        {content.ai_summary && (
          <div className="space-y-2">
            <h4 className="font-semibold flex items-center gap-2">
              <Star className="h-4 w-4" />
              AI Summary
            </h4>
            <p className="text-gray-300 bg-gray-800 rounded-lg p-3">
              {content.ai_summary}
            </p>
          </div>
        )}

        {/* Description */}
        {content.description && (
          <div className="space-y-2">
            <h4 className="font-semibold">Description</h4>
            <p className="text-gray-300">{content.description}</p>
          </div>
        )}

        {/* Referenced Titles */}
        {content.referenced_titles.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-semibold flex items-center gap-2">
              <Tag className="h-4 w-4" />
              Referenced Movies/Shows
            </h4>
            <div className="flex flex-wrap gap-2">
              {content.referenced_titles.map((title, index) => (
                <Badge key={index} variant="outline">{title}</Badge>
              ))}
            </div>
          </div>
        )}

        {/* Tags */}
        {content.tags.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-semibold">Tags</h4>
            <div className="flex flex-wrap gap-2">
              {content.tags.map((tag, index) => (
                <Badge key={index} variant="secondary">{tag}</Badge>
              ))}
            </div>
          </div>
        )}

        {/* AI Topics */}
        {content.ai_topics.length > 0 && (
          <div className="space-y-2">
            <h4 className="font-semibold">AI-Detected Topics</h4>
            <div className="flex flex-wrap gap-2">
              {content.ai_topics.map((topic, index) => (
                <Badge key={index} className="bg-purple-600">{topic}</Badge>
              ))}
            </div>
          </div>
        )}

        {/* Metadata */}
        <div className="flex items-center gap-4 text-sm text-gray-400">
          <div className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            {new Date(content.published_at).toLocaleDateString()}
          </div>
          <div className="flex items-center gap-1">
            <User className="h-4 w-4" />
            Trust Score: {content.creator_trust_score}/10
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-3 pt-4 border-t border-gray-700">
          <Button onClick={handleExternalClick} className="flex-1">
            <ExternalLink className="h-4 w-4 mr-2" />
            View on {content.platform}
          </Button>
          <Button variant="outline" onClick={handleShare}>
            <Share className="h-4 w-4 mr-2" />
            Share
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

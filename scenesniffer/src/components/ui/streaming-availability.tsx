'use client'

import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ExternalLink, Play } from 'lucide-react'

export interface StreamingProvider {
  provider_id: number
  provider_name: string
  logo_path?: string
}

export interface StreamingAvailability {
  title: string
  tmdb_id?: number
  tmdbId?: number  // Alternative naming
  content_type?: 'movie' | 'tv'
  type?: 'movie' | 'tv'  // Alternative naming
  providers?: {
    flatrate?: StreamingProvider[]  // Subscription services
    rent?: StreamingProvider[]      // Rental services
    buy?: StreamingProvider[]       // Purchase services
  }
  availableOn?: {
    subscription?: string[]  // Alternative format
    rental?: string[]
    purchase?: string[]
  }
  poster_path?: string
  posterPath?: string  // Alternative naming
  release_date?: string
  overview?: string
}

interface StreamingAvailabilityProps {
  streamingData: StreamingAvailability[]
  className?: string
  compact?: boolean
}

export function StreamingAvailabilityDisplay({ 
  streamingData, 
  className = '',
  compact = false 
}: StreamingAvailabilityProps) {
  if (!streamingData || streamingData.length === 0) {
    return null
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {streamingData.map((item, index) => (
        <div 
          key={`${item.tmdb_id}-${index}`}
          className={`
            bg-gradient-to-r from-purple-600/10 to-blue-600/10 
            border border-purple-400/20 rounded-lg p-3
            ${compact ? 'p-2' : 'p-3'}
          `}
        >
          <div className="flex items-start gap-3">
            {/* Poster */}
            {!compact && (item.poster_path || item.posterPath) && (
              <div className="flex-shrink-0">
                <img
                  src={`https://image.tmdb.org/t/p/w92${item.poster_path || item.posterPath}`}
                  alt={item.title}
                  className="w-12 h-18 object-cover rounded"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none'
                  }}
                />
              </div>
            )}

            <div className="flex-1 min-w-0">
              {/* Title and Type */}
              <div className="flex items-center gap-2 mb-2">
                <h4 className="font-medium text-white text-sm truncate">
                  {item.title}
                </h4>
                <Badge
                  variant="outline"
                  className="text-xs bg-purple-600/20 text-purple-300 border-purple-400/30"
                >
                  {(item.content_type || item.type) === 'movie' ? '🎬 Movie' : '📺 TV Show'}
                </Badge>
              </div>

              {/* Streaming Options */}
              <div className="space-y-2">
                {/* Subscription Services */}
                {((item.providers?.flatrate && item.providers.flatrate.length > 0) ||
                  (item.availableOn?.subscription && item.availableOn.subscription.length > 0)) && (
                  <div>
                    <div className="text-xs text-green-400 font-medium mb-1">
                      📺 Included with subscription:
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {/* Handle providers format */}
                      {item.providers?.flatrate?.map((provider) => (
                        <Badge
                          key={provider.provider_id}
                          className="text-xs bg-green-600/20 text-green-300 border-green-400/30"
                        >
                          {provider.provider_name}
                        </Badge>
                      ))}
                      {/* Handle availableOn format */}
                      {item.availableOn?.subscription?.map((serviceName, index) => (
                        <Badge
                          key={index}
                          className="text-xs bg-green-600/20 text-green-300 border-green-400/30"
                        >
                          {serviceName}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Rental Services */}
                {((item.providers?.rent && item.providers.rent.length > 0) ||
                  (item.availableOn?.rental && item.availableOn.rental.length > 0)) && (
                  <div>
                    <div className="text-xs text-yellow-400 font-medium mb-1">
                      💰 Available to rent:
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {/* Handle providers format */}
                      {item.providers?.rent?.map((provider) => (
                        <Badge
                          key={provider.provider_id}
                          className="text-xs bg-yellow-600/20 text-yellow-300 border-yellow-400/30"
                        >
                          {provider.provider_name}
                        </Badge>
                      ))}
                      {/* Handle availableOn format */}
                      {item.availableOn?.rental?.map((serviceName, index) => (
                        <Badge
                          key={index}
                          className="text-xs bg-yellow-600/20 text-yellow-300 border-yellow-400/30"
                        >
                          {serviceName}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}

                {/* Purchase Services */}
                {((item.providers?.buy && item.providers.buy.length > 0) ||
                  (item.availableOn?.purchase && item.availableOn.purchase.length > 0)) && (
                  <div>
                    <div className="text-xs text-blue-400 font-medium mb-1">
                      🛒 Available to buy:
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {/* Handle providers format */}
                      {item.providers?.buy?.map((provider) => (
                        <Badge
                          key={provider.provider_id}
                          className="text-xs bg-blue-600/20 text-blue-300 border-blue-400/30"
                        >
                          {provider.provider_name}
                        </Badge>
                      ))}
                      {/* Handle availableOn format */}
                      {item.availableOn?.purchase?.map((serviceName, index) => (
                        <Badge
                          key={index}
                          className="text-xs bg-blue-600/20 text-blue-300 border-blue-400/30"
                        >
                          {serviceName}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Action Button */}
              {!compact && (
                <div className="mt-3 flex justify-end">
                  <Button
                    size="sm"
                    variant="outline"
                    className="text-xs bg-purple-600/20 text-purple-300 border-purple-400/30 hover:bg-purple-600/30"
                    onClick={() => {
                      // Open TMDb page or first available streaming service
                      const contentType = item.content_type || item.type || 'movie'
                      const tmdbId = item.tmdb_id || item.tmdbId
                      const tmdbUrl = `https://www.themoviedb.org/${contentType}/${tmdbId}`
                      window.open(tmdbUrl, '_blank')
                    }}
                  >
                    <Play className="mr-1 h-3 w-3" />
                    View Options
                    <ExternalLink className="ml-1 h-3 w-3" />
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

// Compact version for feed cards
export function StreamingBadges({ streamingData }: { streamingData: StreamingAvailability[] }) {
  if (!streamingData || streamingData.length === 0) {
    return null
  }

  // Get all unique subscription services
  const subscriptionServices = new Set<string>()
  streamingData.forEach(item => {
    // Handle both data formats
    if (item.providers && item.providers.flatrate) {
      // Original format with provider objects
      item.providers.flatrate.forEach(provider => {
        subscriptionServices.add(provider.provider_name)
      })
    } else if (item.availableOn && item.availableOn.subscription) {
      // Alternative format with string arrays
      item.availableOn.subscription.forEach(serviceName => {
        subscriptionServices.add(serviceName)
      })
    }
  })

  if (subscriptionServices.size === 0) {
    return null
  }

  return (
    <div className="flex flex-wrap gap-1">
      <span className="text-xs text-green-400 mr-1">📺</span>
      {Array.from(subscriptionServices).slice(0, 3).map(service => (
        <Badge
          key={service}
          className="text-xs bg-green-600/20 text-green-300 border-green-400/30"
        >
          {service}
        </Badge>
      ))}
      {subscriptionServices.size > 3 && (
        <Badge className="text-xs bg-gray-600/20 text-gray-300 border-gray-400/30">
          +{subscriptionServices.size - 3} more
        </Badge>
      )}
    </div>
  )
}

'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import {
  Filter,
  X,
  Calendar,
  User,
  Tag,
  Play,
  ChevronDown,
  ChevronUp
} from 'lucide-react'

interface FeedFiltersProps {
  onFiltersChange: (filters: FeedFilters) => void
  availableCreators?: Array<{ id: string; name: string; handle: string }>
  className?: string
}

export interface FeedFilters {
  contentTypes: string[]
  genres: string[]
  creators: string[]
  platforms: string[]
  dateRange: 'all' | 'today' | 'week' | 'month'
  sortBy: 'relevance' | 'newest' | 'popular' | 'trending'
}

const CONTENT_TYPES = [
  { id: 'review', label: 'Reviews', icon: '⭐', color: 'bg-blue-600' },
  { id: 'recommendation', label: 'Recommendations', icon: '👍', color: 'bg-green-600' },
  { id: 'theory', label: 'Theories', icon: '🧠', color: 'bg-purple-600' },
  { id: 'news', label: 'News', icon: '📰', color: 'bg-red-600' },
  { id: 'breakdown', label: 'Breakdowns', icon: '🔍', color: 'bg-orange-600' },
  { id: 'spoiler-free', label: 'Spoiler-Free', icon: '🔒', color: 'bg-teal-600' }
]

const POPULAR_GENRES = [
  'Action', 'Comedy', 'Drama', 'Horror', 'Sci-Fi', 'Romance',
  'Thriller', 'Fantasy', 'Animation', 'Documentary', 'Crime', 'Adventure'
]

const PLATFORMS = [
  { id: 'youtube', label: 'YouTube', icon: '📺', color: 'bg-red-600' },
  { id: 'reddit', label: 'Reddit', icon: '📱', color: 'bg-orange-600' },
  { id: 'podcast', label: 'Podcasts', icon: '🎙️', color: 'bg-purple-600' },
  { id: 'instagram', label: 'Instagram', icon: '📸', color: 'bg-pink-600' },
  { id: 'twitter', label: 'Twitter', icon: '🐦', color: 'bg-blue-600' }
]

const DATE_RANGES = [
  { id: 'all', label: 'All Time' },
  { id: 'today', label: 'Today' },
  { id: 'week', label: 'This Week' },
  { id: 'month', label: 'This Month' }
]

const SORT_OPTIONS = [
  { id: 'relevance', label: 'Most Relevant', icon: '🎯' },
  { id: 'newest', label: 'Newest First', icon: '🕐' },
  { id: 'popular', label: 'Most Popular', icon: '🔥' },
  { id: 'trending', label: 'Trending', icon: '📈' }
]

export function FeedFilters({ onFiltersChange, availableCreators = [], className }: FeedFiltersProps) {
  const [filters, setFilters] = useState<FeedFilters>({
    contentTypes: [],
    genres: [],
    creators: [],
    platforms: [],
    dateRange: 'all',
    sortBy: 'relevance'
  })
  
  const [isExpanded, setIsExpanded] = useState(false)
  const [activeFilterCount, setActiveFilterCount] = useState(0)

  // Update active filter count
  useEffect(() => {
    const count = filters.contentTypes.length +
                  filters.genres.length +
                  filters.creators.length +
                  filters.platforms.length +
                  (filters.dateRange !== 'all' ? 1 : 0) +
                  (filters.sortBy !== 'relevance' ? 1 : 0)
    setActiveFilterCount(count)
  }, [filters])

  // Notify parent of filter changes
  useEffect(() => {
    onFiltersChange(filters)
  }, [filters, onFiltersChange])

  const updateFilters = (key: keyof FeedFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }


  const clearAllFilters = () => {
    setFilters({
      contentTypes: [],
      genres: [],
      creators: [],
      platforms: [],
      dateRange: 'all',
      sortBy: 'relevance'
    })
  }

  const clearFilterCategory = (category: keyof FeedFilters) => {
    if (category === 'dateRange') {
      updateFilters('dateRange', 'all')
    } else if (category === 'sortBy') {
      updateFilters('sortBy', 'relevance')
    } else {
      updateFilters(category, [])
    }
  }

  return (
    <Card className={`bg-white/10 border-white/20 text-white ${className}`}>
      <CardContent className="p-4">
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          {/* Filter Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              <span className="font-semibold">Filters</span>
              {activeFilterCount > 0 && (
                <Badge className="bg-purple-600 text-white">
                  {activeFilterCount}
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-2">
              {activeFilterCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllFilters}
                  className="text-gray-300 hover:text-white"
                >
                  <X className="h-4 w-4 mr-1" />
                  Clear All
                </Button>
              )}
              <CollapsibleTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-gray-300 hover:text-white"
                >
                  {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                </Button>
              </CollapsibleTrigger>
            </div>
          </div>

          {/* Quick Sort Options (Always Visible) */}
          <div className="mb-4">
            <ToggleGroup
              type="single"
              value={filters.sortBy}
              onValueChange={(value) => value && updateFilters('sortBy', value)}
              className="justify-start flex-wrap gap-2"
            >
              {SORT_OPTIONS.map(option => (
                <ToggleGroupItem
                  key={option.id}
                  value={option.id}
                  size="sm"
                  className="data-[state=on]:bg-purple-600 data-[state=on]:text-white border-white/30 text-white hover:bg-white/10"
                >
                  <span className="mr-1">{option.icon}</span>
                  {option.label}
                </ToggleGroupItem>
              ))}
            </ToggleGroup>
          </div>

          {/* Expanded Filters */}
          <CollapsibleContent className="space-y-6">
            {/* Content Types */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Play className="h-4 w-4" />
                  Content Types
                </h4>
                {filters.contentTypes.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => clearFilterCategory('contentTypes')}
                    className="text-xs text-gray-400 hover:text-white"
                  >
                    Clear
                  </Button>
                )}
              </div>
              <ToggleGroup
                type="multiple"
                value={filters.contentTypes}
                onValueChange={(value) => updateFilters('contentTypes', value)}
                className="grid grid-cols-2 md:grid-cols-3 gap-2"
              >
                {CONTENT_TYPES.map(type => (
                  <ToggleGroupItem
                    key={type.id}
                    value={type.id}
                    size="sm"
                    className={`
                      justify-start h-auto py-2 px-3
                      data-[state=on]:${type.color} data-[state=on]:text-white data-[state=on]:hover:opacity-90
                      data-[state=off]:bg-transparent data-[state=off]:border-white/30 data-[state=off]:text-white data-[state=off]:hover:bg-white/10
                    `}
                  >
                    <span className="mr-2">{type.icon}</span>
                    <span className="text-xs">{type.label}</span>
                  </ToggleGroupItem>
                ))}
              </ToggleGroup>
            </div>

            {/* Platforms */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Play className="h-4 w-4" />
                  Platforms
                </h4>
                {filters.platforms.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => clearFilterCategory('platforms')}
                    className="text-xs text-gray-400 hover:text-white"
                  >
                    Clear
                  </Button>
                )}
              </div>
              <ToggleGroup
                type="multiple"
                value={filters.platforms}
                onValueChange={(value) => updateFilters('platforms', value)}
                className="grid grid-cols-2 md:grid-cols-3 gap-2"
              >
                {PLATFORMS.map(platform => (
                  <ToggleGroupItem
                    key={platform.id}
                    value={platform.id}
                    size="sm"
                    className={`
                      transition-all duration-200 text-xs
                      data-[state=on]:${platform.color} data-[state=on]:text-white data-[state=on]:hover:opacity-90
                      data-[state=off]:bg-transparent data-[state=off]:border-white/30 data-[state=off]:text-white data-[state=off]:hover:bg-white/10
                    `}
                  >
                    <span className="mr-2">{platform.icon}</span>
                    <span className="text-xs">{platform.label}</span>
                  </ToggleGroupItem>
                ))}
              </ToggleGroup>
            </div>

            {/* Genres */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Tag className="h-4 w-4" />
                  Genres
                </h4>
                {filters.genres.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => clearFilterCategory('genres')}
                    className="text-xs text-gray-400 hover:text-white"
                  >
                    Clear
                  </Button>
                )}
              </div>
              <ToggleGroup
                type="multiple"
                value={filters.genres}
                onValueChange={(value) => updateFilters('genres', value)}
                className="flex flex-wrap gap-2"
              >
                {POPULAR_GENRES.map(genre => (
                  <ToggleGroupItem
                    key={genre}
                    value={genre}
                    size="sm"
                    className="text-xs data-[state=on]:bg-purple-600 data-[state=on]:text-white border-white/30 text-white hover:bg-white/10"
                  >
                    {genre}
                  </ToggleGroupItem>
                ))}
              </ToggleGroup>
            </div>

            {/* Date Range */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Date Range
                </h4>
                {filters.dateRange !== 'all' && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => clearFilterCategory('dateRange')}
                    className="text-xs text-gray-400 hover:text-white"
                  >
                    Clear
                  </Button>
                )}
              </div>
              <ToggleGroup
                type="single"
                value={filters.dateRange}
                onValueChange={(value) => value && updateFilters('dateRange', value)}
                className="flex flex-wrap gap-2"
              >
                {DATE_RANGES.map(range => (
                  <ToggleGroupItem
                    key={range.id}
                    value={range.id}
                    size="sm"
                    className="text-xs data-[state=on]:bg-purple-600 data-[state=on]:text-white border-white/30 text-white hover:bg-white/10"
                  >
                    {range.label}
                  </ToggleGroupItem>
                ))}
              </ToggleGroup>
            </div>

            {/* Creators (if available) */}
            {availableCreators.length > 0 && (
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Creators
                  </h4>
                  {filters.creators.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => clearFilterCategory('creators')}
                      className="text-xs text-gray-400 hover:text-white"
                    >
                      Clear
                    </Button>
                  )}
                </div>
                <ToggleGroup
                  type="multiple"
                  value={filters.creators}
                  onValueChange={(value) => updateFilters('creators', value)}
                  className="flex flex-wrap gap-2 max-h-32 overflow-y-auto"
                >
                  {availableCreators.slice(0, 20).map(creator => (
                    <ToggleGroupItem
                      key={creator.id}
                      value={creator.id}
                      size="sm"
                      className="text-xs data-[state=on]:bg-purple-600 data-[state=on]:text-white border-white/30 text-white hover:bg-white/10"
                    >
                      {creator.name}
                    </ToggleGroupItem>
                  ))}
                </ToggleGroup>
              </div>
            )}
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  )
}

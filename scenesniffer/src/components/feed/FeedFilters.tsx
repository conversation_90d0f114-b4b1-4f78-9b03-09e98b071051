'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Filter, 
  X, 
  Calendar, 
  User, 
  Tag, 
  Play,
  ChevronDown,
  ChevronUp
} from 'lucide-react'

interface FeedFiltersProps {
  onFiltersChange: (filters: FeedFilters) => void
  availableCreators?: Array<{ id: string; name: string; handle: string }>
  className?: string
}

export interface FeedFilters {
  contentTypes: string[]
  genres: string[]
  creators: string[]
  platforms: string[]
  dateRange: 'all' | 'today' | 'week' | 'month'
  sortBy: 'relevance' | 'newest' | 'popular' | 'trending'
}

const CONTENT_TYPES = [
  { id: 'review', label: 'Reviews', icon: '⭐', color: 'bg-blue-600' },
  { id: 'recommendation', label: 'Recommendations', icon: '👍', color: 'bg-green-600' },
  { id: 'theory', label: 'Theories', icon: '🧠', color: 'bg-purple-600' },
  { id: 'news', label: 'News', icon: '📰', color: 'bg-red-600' },
  { id: 'breakdown', label: 'Breakdowns', icon: '🔍', color: 'bg-orange-600' },
  { id: 'spoiler-free', label: 'Spoiler-Free', icon: '🔒', color: 'bg-teal-600' }
]

const POPULAR_GENRES = [
  'Action', 'Comedy', 'Drama', 'Horror', 'Sci-Fi', 'Romance',
  'Thriller', 'Fantasy', 'Animation', 'Documentary', 'Crime', 'Adventure'
]

const PLATFORMS = [
  { id: 'youtube', label: 'YouTube', icon: '📺', color: 'bg-red-600' },
  { id: 'reddit', label: 'Reddit', icon: '📱', color: 'bg-orange-600' },
  { id: 'podcast', label: 'Podcasts', icon: '🎙️', color: 'bg-purple-600' },
  { id: 'instagram', label: 'Instagram', icon: '📸', color: 'bg-pink-600' },
  { id: 'twitter', label: 'Twitter', icon: '🐦', color: 'bg-blue-600' }
]

const DATE_RANGES = [
  { id: 'all', label: 'All Time' },
  { id: 'today', label: 'Today' },
  { id: 'week', label: 'This Week' },
  { id: 'month', label: 'This Month' }
]

const SORT_OPTIONS = [
  { id: 'relevance', label: 'Most Relevant', icon: '🎯' },
  { id: 'newest', label: 'Newest First', icon: '🕐' },
  { id: 'popular', label: 'Most Popular', icon: '🔥' },
  { id: 'trending', label: 'Trending', icon: '📈' }
]

export function FeedFilters({ onFiltersChange, availableCreators = [], className }: FeedFiltersProps) {
  const [filters, setFilters] = useState<FeedFilters>({
    contentTypes: [],
    genres: [],
    creators: [],
    platforms: [],
    dateRange: 'all',
    sortBy: 'relevance'
  })
  
  const [isExpanded, setIsExpanded] = useState(false)
  const [activeFilterCount, setActiveFilterCount] = useState(0)

  // Update active filter count
  useEffect(() => {
    const count = filters.contentTypes.length +
                  filters.genres.length +
                  filters.creators.length +
                  filters.platforms.length +
                  (filters.dateRange !== 'all' ? 1 : 0) +
                  (filters.sortBy !== 'relevance' ? 1 : 0)
    setActiveFilterCount(count)
  }, [filters])

  // Notify parent of filter changes
  useEffect(() => {
    onFiltersChange(filters)
  }, [filters, onFiltersChange])

  const updateFilters = (key: keyof FeedFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const toggleArrayFilter = (key: 'contentTypes' | 'genres' | 'creators', value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: prev[key].includes(value)
        ? prev[key].filter(item => item !== value)
        : [...prev[key], value]
    }))
  }

  const clearAllFilters = () => {
    setFilters({
      contentTypes: [],
      genres: [],
      creators: [],
      dateRange: 'all',
      sortBy: 'relevance'
    })
  }

  const clearFilterCategory = (category: keyof FeedFilters) => {
    if (category === 'dateRange') {
      updateFilters('dateRange', 'all')
    } else if (category === 'sortBy') {
      updateFilters('sortBy', 'relevance')
    } else {
      updateFilters(category, [])
    }
  }

  return (
    <Card className={`bg-white/10 border-white/20 text-white ${className}`}>
      <CardContent className="p-4">
        {/* Filter Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            <span className="font-semibold">Filters</span>
            {activeFilterCount > 0 && (
              <Badge className="bg-purple-600 text-white">
                {activeFilterCount}
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            {activeFilterCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="text-gray-300 hover:text-white"
              >
                <X className="h-4 w-4 mr-1" />
                Clear All
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-gray-300 hover:text-white"
            >
              {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </div>
        </div>

        {/* Quick Sort Options (Always Visible) */}
        <div className="flex flex-wrap gap-2 mb-4">
          {SORT_OPTIONS.map(option => (
            <Button
              key={option.id}
              variant={filters.sortBy === option.id ? "default" : "outline"}
              size="sm"
              onClick={() => updateFilters('sortBy', option.id)}
              className={`
                ${filters.sortBy === option.id
                  ? 'bg-purple-600 hover:bg-purple-700 text-white'
                  : 'bg-transparent border-white/30 text-white hover:bg-white/10'
                }
              `}
            >
              <span className="mr-1">{option.icon}</span>
              {option.label}
            </Button>
          ))}
        </div>

        {/* Expanded Filters */}
        {isExpanded && (
          <div className="space-y-6">
            {/* Content Types */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Play className="h-4 w-4" />
                  Content Types
                </h4>
                {filters.contentTypes.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => clearFilterCategory('contentTypes')}
                    className="text-xs text-gray-400 hover:text-white"
                  >
                    Clear
                  </Button>
                )}
              </div>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {CONTENT_TYPES.map(type => (
                  <Button
                    key={type.id}
                    variant={filters.contentTypes.includes(type.id) ? "default" : "outline"}
                    size="sm"
                    onClick={() => toggleArrayFilter('contentTypes', type.id)}
                    className={`
                      justify-start h-auto py-2 px-3
                      ${filters.contentTypes.includes(type.id)
                        ? `${type.color} hover:opacity-90 text-white`
                        : 'bg-transparent border-white/30 text-white hover:bg-white/10'
                      }
                    `}
                  >
                    <span className="mr-2">{type.icon}</span>
                    <span className="text-xs">{type.label}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Platforms */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Play className="h-4 w-4" />
                  Platforms
                </h4>
                {filters.platforms.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => clearFilterCategory('platforms')}
                    className="text-xs text-gray-400 hover:text-white"
                  >
                    Clear
                  </Button>
                )}
              </div>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {PLATFORMS.map(platform => (
                  <Button
                    key={platform.id}
                    variant={filters.platforms.includes(platform.id) ? "default" : "outline"}
                    size="sm"
                    onClick={() => toggleArrayFilter('platforms', platform.id)}
                    className={`
                      transition-all duration-200 text-xs
                      ${filters.platforms.includes(platform.id)
                        ? `${platform.color} hover:opacity-90 text-white`
                        : 'bg-transparent border-white/30 text-white hover:bg-white/10'
                      }
                    `}
                  >
                    <span className="mr-2">{platform.icon}</span>
                    <span className="text-xs">{platform.label}</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Genres */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Tag className="h-4 w-4" />
                  Genres
                </h4>
                {filters.genres.length > 0 && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => clearFilterCategory('genres')}
                    className="text-xs text-gray-400 hover:text-white"
                  >
                    Clear
                  </Button>
                )}
              </div>
              <div className="flex flex-wrap gap-2">
                {POPULAR_GENRES.map(genre => (
                  <Button
                    key={genre}
                    variant={filters.genres.includes(genre) ? "default" : "outline"}
                    size="sm"
                    onClick={() => toggleArrayFilter('genres', genre)}
                    className={`
                      text-xs
                      ${filters.genres.includes(genre)
                        ? 'bg-purple-600 hover:bg-purple-700 text-white'
                        : 'bg-transparent border-white/30 text-white hover:bg-white/10'
                      }
                    `}
                  >
                    {genre}
                  </Button>
                ))}
              </div>
            </div>

            {/* Date Range */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-medium flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Date Range
                </h4>
                {filters.dateRange !== 'all' && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => clearFilterCategory('dateRange')}
                    className="text-xs text-gray-400 hover:text-white"
                  >
                    Clear
                  </Button>
                )}
              </div>
              <div className="flex flex-wrap gap-2">
                {DATE_RANGES.map(range => (
                  <Button
                    key={range.id}
                    variant={filters.dateRange === range.id ? "default" : "outline"}
                    size="sm"
                    onClick={() => updateFilters('dateRange', range.id)}
                    className={`
                      text-xs
                      ${filters.dateRange === range.id
                        ? 'bg-purple-600 hover:bg-purple-700 text-white'
                        : 'bg-transparent border-white/30 text-white hover:bg-white/10'
                      }
                    `}
                  >
                    {range.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* Creators (if available) */}
            {availableCreators.length > 0 && (
              <div>
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Creators
                  </h4>
                  {filters.creators.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => clearFilterCategory('creators')}
                      className="text-xs text-gray-400 hover:text-white"
                    >
                      Clear
                    </Button>
                  )}
                </div>
                <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                  {availableCreators.slice(0, 20).map(creator => (
                    <Button
                      key={creator.id}
                      variant={filters.creators.includes(creator.id) ? "default" : "outline"}
                      size="sm"
                      onClick={() => toggleArrayFilter('creators', creator.id)}
                      className={`
                        text-xs
                        ${filters.creators.includes(creator.id)
                          ? 'bg-purple-600 hover:bg-purple-700 text-white'
                          : 'bg-transparent border-white/30 text-white hover:bg-white/10'
                        }
                      `}
                    >
                      {creator.name}
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { CheckCircle, Film, Users, Tv, ArrowRight } from 'lucide-react'
import { OnboardingData } from '@/types'
import { STREAMING_SERVICES } from '@/utils/constants'

interface OnboardingCompleteProps {
  onboardingData: OnboardingData
  onFinish: () => void
}

export function OnboardingComplete({ onboardingData, onFinish }: OnboardingCompleteProps) {
  const getServiceName = (serviceId: string) => {
    return STREAMING_SERVICES.find(s => s.id === serviceId)?.name || serviceId
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <CheckCircle className="h-16 w-16 text-green-400 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-white mb-2">You're All Set!</h2>
        <p className="text-gray-300 text-lg">
          Your personalized SceneSniffer experience is ready. Here's what we've set up for you:
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-6">
        {/* Genres Summary */}
        <Card className="bg-white/5 border-white/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Film className="h-5 w-5 text-purple-400" />
              Genres
            </CardTitle>
            <CardDescription className="text-gray-300">
              {onboardingData.genres.length} selected
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-1">
              {onboardingData.genres.slice(0, 6).map((genre) => (
                <span
                  key={genre}
                  className="px-2 py-1 bg-purple-600/20 text-purple-300 text-xs rounded"
                >
                  {genre}
                </span>
              ))}
              {onboardingData.genres.length > 6 && (
                <span className="px-2 py-1 bg-gray-600/20 text-gray-300 text-xs rounded">
                  +{onboardingData.genres.length - 6} more
                </span>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Creators Summary */}
        <Card className="bg-white/5 border-white/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Users className="h-5 w-5 text-purple-400" />
              Creators
            </CardTitle>
            <CardDescription className="text-gray-300">
              {onboardingData.creators.length} followed
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {onboardingData.creators.slice(0, 3).map((creator, index) => (
                <div key={index} className="flex items-center gap-2">
                  <span className="text-xs text-gray-400 capitalize">{creator.platform}:</span>
                  <span className="text-white text-sm">{creator.handle}</span>
                </div>
              ))}
              {onboardingData.creators.length > 3 && (
                <p className="text-gray-400 text-xs">
                  +{onboardingData.creators.length - 3} more creators
                </p>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Streaming Services Summary */}
        <Card className="bg-white/5 border-white/20">
          <CardHeader>
            <CardTitle className="text-white flex items-center gap-2">
              <Tv className="h-5 w-5 text-purple-400" />
              Streaming
            </CardTitle>
            <CardDescription className="text-gray-300">
              {onboardingData.streaming_services.length} services
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-1">
              {onboardingData.streaming_services.slice(0, 4).map((serviceId) => (
                <span
                  key={serviceId}
                  className="px-2 py-1 bg-blue-600/20 text-blue-300 text-xs rounded"
                >
                  {getServiceName(serviceId)}
                </span>
              ))}
              {onboardingData.streaming_services.length > 4 && (
                <span className="px-2 py-1 bg-gray-600/20 text-gray-300 text-xs rounded">
                  +{onboardingData.streaming_services.length - 4} more
                </span>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* What's Next */}
      <Card className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 border-purple-400/30">
        <CardHeader>
          <CardTitle className="text-white">What's Next?</CardTitle>
          <CardDescription className="text-gray-300">
            Here's what you can expect from your personalized SceneSniffer experience:
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-400 mt-0.5" />
                <div>
                  <h4 className="text-white font-medium">Personalized Feed</h4>
                  <p className="text-gray-300 text-sm">
                    Content from your favorite creators, filtered by your genres
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-400 mt-0.5" />
                <div>
                  <h4 className="text-white font-medium">AI Summaries</h4>
                  <p className="text-gray-300 text-sm">
                    Quick insights and spoiler-free summaries of content
                  </p>
                </div>
              </div>
            </div>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-400 mt-0.5" />
                <div>
                  <h4 className="text-white font-medium">Streaming Availability</h4>
                  <p className="text-gray-300 text-sm">
                    See where to watch on your selected platforms
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <CheckCircle className="h-5 w-5 text-green-400 mt-0.5" />
                <div>
                  <h4 className="text-white font-medium">Smart Watchlist</h4>
                  <p className="text-gray-300 text-sm">
                    Save content and get notified when it's available
                  </p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Finish Button */}
      <div className="text-center">
        <Button
          onClick={onFinish}
          size="lg"
          className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-3"
        >
          Enter SceneSniffer
          <ArrowRight className="ml-2 h-5 w-5" />
        </Button>
        <p className="text-gray-400 text-sm mt-2">
          You can always update your preferences later in settings
        </p>
      </div>
    </div>
  )
}

'use client'

import { Button } from '@/components/ui/button'
import { STREAMING_SERVICES } from '@/utils/constants'
import { Check } from 'lucide-react'

interface StreamingSelectionProps {
  selectedServices: string[]
  onServicesChange: (services: string[]) => void
}

export function StreamingSelection({ selectedServices, onServicesChange }: StreamingSelectionProps) {
  const toggleService = (serviceId: string) => {
    if (selectedServices.includes(serviceId)) {
      onServicesChange(selectedServices.filter(s => s !== serviceId))
    } else {
      onServicesChange([...selectedServices, serviceId])
    }
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <p className="text-gray-300 mb-4">
          Select the streaming services you have access to. This helps us show you where to watch recommended content.
        </p>
        <p className="text-sm text-gray-400">
          Selected: {selectedServices.length} {selectedServices.length === 1 ? 'service' : 'services'}
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {STREAMING_SERVICES.map((service) => {
          const isSelected = selectedServices.includes(service.id)
          return (
            <Button
              key={service.id}
              variant="outline"
              onClick={() => toggleService(service.id)}
              className={`
                relative h-16 p-4 transition-all duration-200 flex items-center justify-start gap-3
                ${isSelected 
                  ? 'bg-purple-600 hover:bg-purple-700 text-white border-purple-600' 
                  : 'bg-white/5 border-white/20 text-white hover:bg-white/10 hover:border-white/40'
                }
              `}
            >
              {isSelected && (
                <Check className="absolute top-2 right-2 h-4 w-4" />
              )}
              
              {/* Service Logo Placeholder */}
              <div className="w-8 h-8 bg-white/20 rounded flex items-center justify-center text-xs font-bold">
                {service.name.charAt(0)}
              </div>
              
              <div className="flex-1 text-left">
                <p className="font-medium">{service.name}</p>
              </div>
            </Button>
          )
        })}
      </div>

      {selectedServices.length > 0 && (
        <div className="mt-6 p-4 bg-white/5 rounded-lg border border-white/10">
          <h4 className="font-semibold text-white mb-2">Your Streaming Services:</h4>
          <div className="flex flex-wrap gap-2">
            {selectedServices.map((serviceId) => {
              const service = STREAMING_SERVICES.find(s => s.id === serviceId)
              return (
                <span
                  key={serviceId}
                  className="px-3 py-1 bg-purple-600 text-white text-sm rounded-full"
                >
                  {service?.name}
                </span>
              )
            })}
          </div>
        </div>
      )}

      <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
        <div className="flex items-start gap-3">
          <div className="text-blue-400 text-xl">💡</div>
          <div>
            <h4 className="text-blue-400 font-medium mb-1">Pro Tip</h4>
            <p className="text-blue-300 text-sm">
              Don't worry if you don't see your service listed. You can always update your preferences later, 
              and we'll show availability across all major platforms.
            </p>
          </div>
        </div>
      </div>

      {selectedServices.length === 0 && (
        <div className="text-center">
          <p className="text-yellow-400 text-sm">
            💡 Select at least one streaming service to see personalized availability
          </p>
        </div>
      )}
    </div>
  )
}

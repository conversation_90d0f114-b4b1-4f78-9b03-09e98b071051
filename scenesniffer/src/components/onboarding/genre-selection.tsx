'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { GENRES } from '@/utils/constants'
import { Check } from 'lucide-react'

interface GenreSelectionProps {
  selectedGenres: string[]
  onGenresChange: (genres: string[]) => void
}

export function GenreSelection({ selectedGenres, onGenresChange }: GenreSelectionProps) {
  const toggleGenre = (genre: string) => {
    if (selectedGenres.includes(genre)) {
      onGenresChange(selectedGenres.filter(g => g !== genre))
    } else {
      onGenresChange([...selectedGenres, genre])
    }
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <p className="text-gray-300 mb-4">
          Select at least 3 genres you enjoy. This helps us curate content that matches your taste.
        </p>
        <p className="text-sm text-gray-400">
          Selected: {selectedGenres.length} {selectedGenres.length === 1 ? 'genre' : 'genres'}
        </p>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
        {GENRES.map((genre) => {
          const isSelected = selectedGenres.includes(genre)
          return (
            <Button
              key={genre}
              variant={isSelected ? "default" : "outline"}
              onClick={() => toggleGenre(genre)}
              className={`
                relative h-12 text-sm transition-all duration-200
                ${isSelected 
                  ? 'bg-purple-600 hover:bg-purple-700 text-white border-purple-600' 
                  : 'bg-transparent border-white/30 text-white hover:bg-white/10 hover:border-white/50'
                }
              `}
            >
              {isSelected && (
                <Check className="absolute left-2 h-4 w-4" />
              )}
              <span className={isSelected ? 'ml-6' : ''}>
                {genre}
              </span>
            </Button>
          )
        })}
      </div>

      {selectedGenres.length > 0 && (
        <div className="mt-6 p-4 bg-white/5 rounded-lg border border-white/10">
          <h4 className="font-semibold text-white mb-2">Your Selected Genres:</h4>
          <div className="flex flex-wrap gap-2">
            {selectedGenres.map((genre) => (
              <span
                key={genre}
                className="px-3 py-1 bg-purple-600 text-white text-sm rounded-full"
              >
                {genre}
              </span>
            ))}
          </div>
        </div>
      )}

      {selectedGenres.length < 3 && (
        <div className="text-center">
          <p className="text-yellow-400 text-sm">
            💡 Select at least 3 genres to get the best recommendations
          </p>
        </div>
      )}
    </div>
  )
}

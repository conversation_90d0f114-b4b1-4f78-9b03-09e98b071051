'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { PLATFORMS } from '@/utils/constants'
import { Plus, X, Search, Loader2 } from 'lucide-react'

interface Creator {
  platform: string
  handle: string
}

interface CreatorSelectionProps {
  selectedCreators: Creator[]
  onCreatorsChange: (creators: Creator[]) => void
}

export function CreatorSelection({ selectedCreators, onCreatorsChange }: CreatorSelectionProps) {
  const [selectedPlatform, setSelectedPlatform] = useState('youtube')
  const [handle, setHandle] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const addCreator = async () => {
    if (!handle.trim()) return

    setLoading(true)
    setError(null)

    try {
      // Clean up the handle
      let cleanHandle = handle.trim()
      if (selectedPlatform === 'youtube' && !cleanHandle.startsWith('@')) {
        cleanHandle = `@${cleanHandle}`
      }
      if (selectedPlatform === 'twitter' && !cleanHandle.startsWith('@')) {
        cleanHandle = `@${cleanHandle}`
      }
      if (selectedPlatform === 'instagram' && cleanHandle.startsWith('@')) {
        cleanHandle = cleanHandle.substring(1)
      }

      // Check if creator already exists
      const exists = selectedCreators.some(
        c => c.platform === selectedPlatform && c.handle.toLowerCase() === cleanHandle.toLowerCase()
      )

      if (exists) {
        setError('You\'ve already added this creator')
        return
      }

      // Add the creator
      const newCreator = {
        platform: selectedPlatform,
        handle: cleanHandle
      }

      onCreatorsChange([...selectedCreators, newCreator])
      setHandle('')
      setError(null)
    } catch (error) {
      setError('Failed to add creator. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const removeCreator = (index: number) => {
    const newCreators = selectedCreators.filter((_, i) => i !== index)
    onCreatorsChange(newCreators)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      addCreator()
    }
  }

  const getPlaceholder = () => {
    switch (selectedPlatform) {
      case 'youtube':
        return '@mkbhd or MKBHD'
      case 'instagram':
        return 'username (without @)'
      case 'twitter':
        return '@username'
      case 'tiktok':
        return '@username'
      default:
        return 'Enter handle'
    }
  }

  const getPlatformIcon = (platform: string) => {
    const platformData = PLATFORMS.find(p => p.id === platform)
    return platformData?.icon || '📺'
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <p className="text-gray-300 mb-4">
          Add your favorite movie and TV content creators. We'll aggregate their content to create your personalized feed.
        </p>
        <p className="text-sm text-gray-400">
          Added: {selectedCreators.length} {selectedCreators.length === 1 ? 'creator' : 'creators'}
        </p>
      </div>

      {/* Add Creator Form */}
      <Card className="bg-white/5 border-white/20">
        <CardHeader>
          <CardTitle className="text-white text-lg">Add a Creator</CardTitle>
          <CardDescription className="text-gray-300">
            Choose a platform and enter the creator's handle
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Platform Selection */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Platform</label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              {PLATFORMS.map((platform) => (
                <Button
                  key={platform.id}
                  variant={selectedPlatform === platform.id ? "default" : "outline"}
                  onClick={() => setSelectedPlatform(platform.id)}
                  className={`
                    ${selectedPlatform === platform.id
                      ? 'bg-purple-600 hover:bg-purple-700 text-white'
                      : 'bg-transparent border-white/30 text-white hover:bg-white/10'
                    }
                  `}
                >
                  <span className="mr-2">{platform.icon}</span>
                  {platform.name}
                </Button>
              ))}
            </div>
          </div>

          {/* Handle Input */}
          <div>
            <label className="block text-sm font-medium text-white mb-2">Creator Handle</label>
            <div className="flex gap-2">
              <input
                type="text"
                value={handle}
                onChange={(e) => setHandle(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder={getPlaceholder()}
                className="flex-1 px-3 py-2 bg-white/10 border border-white/30 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
              />
              <Button
                onClick={addCreator}
                disabled={!handle.trim() || loading}
                className="bg-purple-600 hover:bg-purple-700"
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Plus className="h-4 w-4" />
                )}
              </Button>
            </div>
            {error && (
              <p className="text-red-400 text-sm mt-1">{error}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Selected Creators */}
      {selectedCreators.length > 0 && (
        <div>
          <h4 className="font-semibold text-white mb-3">Your Selected Creators:</h4>
          <div className="grid gap-3">
            {selectedCreators.map((creator, index) => (
              <div
                key={`${creator.platform}-${creator.handle}`}
                className="flex items-center justify-between p-3 bg-white/5 rounded-lg border border-white/10"
              >
                <div className="flex items-center gap-3">
                  <span className="text-2xl">{getPlatformIcon(creator.platform)}</span>
                  <div>
                    <p className="text-white font-medium">{creator.handle}</p>
                    <p className="text-gray-400 text-sm capitalize">{creator.platform}</p>
                  </div>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => removeCreator(index)}
                  className="border-red-400 text-red-400 hover:bg-red-400 hover:text-white"
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </div>
      )}

      {selectedCreators.length === 0 && (
        <div className="text-center py-8">
          <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-400">
            No creators added yet. Add some creators to get personalized recommendations!
          </p>
        </div>
      )}

      {selectedCreators.length > 0 && selectedCreators.length < 3 && (
        <div className="text-center">
          <p className="text-yellow-400 text-sm">
            💡 Add at least 3 creators for the best personalized experience
          </p>
        </div>
      )}
    </div>
  )
}

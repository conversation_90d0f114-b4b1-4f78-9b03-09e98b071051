'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Loader2, Plus, Edit, Trash2, Merge, Users, BarChart3, AlertCircle, CheckCircle } from 'lucide-react'

interface Genre {
  name: string
  count: number
  creators: string[]
  percentage: number
}

interface GenreManagerProps {
  onClose?: () => void
}

export default function GenreManager({ onClose }: GenreManagerProps) {
  const [genres, setGenres] = useState<Genre[]>([])
  const [loading, setLoading] = useState(true)
  const [actionLoading, setActionLoading] = useState<string | null>(null)
  const [newGenreName, setNewGenreName] = useState('')
  const [editingGenre, setEditingGenre] = useState<Genre | null>(null)
  const [editGenreName, setEditGenreName] = useState('')
  const [deletingGenre, setDeletingGenre] = useState<Genre | null>(null)
  const [replacementGenre, setReplacementGenre] = useState('')
  const [mergingGenres, setMergingGenres] = useState<string[]>([])
  const [mergeTarget, setMergeTarget] = useState('')
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null)

  const loadGenres = async () => {
    try {
      const response = await fetch('/api/admin/genres')
      const data = await response.json()
      if (data.success) {
        setGenres(data.genres)
      }
    } catch (error) {
      console.error('Failed to load genres:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    loadGenres()
  }, [])

  const handleAddGenre = async () => {
    if (!newGenreName.trim()) return
    
    setActionLoading('add')
    try {
      const response = await fetch('/api/admin/genres', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ genreName: newGenreName.trim() })
      })
      const data = await response.json()
      
      setResult(data)
      if (data.success) {
        setNewGenreName('')
        await loadGenres()
      }
    } catch (error) {
      setResult({ success: false, message: 'Failed to add genre' })
    } finally {
      setActionLoading(null)
    }
  }

  const handleEditGenre = async () => {
    if (!editingGenre || !editGenreName.trim()) return
    
    setActionLoading('edit')
    try {
      const response = await fetch('/api/admin/genres', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          oldGenre: editingGenre.name, 
          newGenre: editGenreName.trim() 
        })
      })
      const data = await response.json()
      
      setResult(data)
      if (data.success) {
        setEditingGenre(null)
        setEditGenreName('')
        await loadGenres()
      }
    } catch (error) {
      setResult({ success: false, message: 'Failed to edit genre' })
    } finally {
      setActionLoading(null)
    }
  }

  const handleDeleteGenre = async () => {
    if (!deletingGenre) return
    
    setActionLoading('delete')
    try {
      const params = new URLSearchParams({
        genre: deletingGenre.name,
        ...(replacementGenre && { replacement: replacementGenre })
      })
      
      const response = await fetch(`/api/admin/genres?${params}`, {
        method: 'DELETE'
      })
      const data = await response.json()
      
      setResult(data)
      if (data.success) {
        setDeletingGenre(null)
        setReplacementGenre('')
        await loadGenres()
      }
    } catch (error) {
      setResult({ success: false, message: 'Failed to delete genre' })
    } finally {
      setActionLoading(null)
    }
  }

  const handleMergeGenres = async () => {
    if (mergingGenres.length < 2 || !mergeTarget.trim()) return
    
    setActionLoading('merge')
    try {
      const response = await fetch('/api/admin/genres/merge', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          genresToMerge: mergingGenres, 
          targetGenre: mergeTarget.trim() 
        })
      })
      const data = await response.json()
      
      setResult(data)
      if (data.success) {
        setMergingGenres([])
        setMergeTarget('')
        await loadGenres()
      }
    } catch (error) {
      setResult({ success: false, message: 'Failed to merge genres' })
    } finally {
      setActionLoading(null)
    }
  }

  const toggleGenreForMerge = (genreName: string) => {
    setMergingGenres(prev => 
      prev.includes(genreName) 
        ? prev.filter(g => g !== genreName)
        : [...prev, genreName]
    )
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-purple-400" />
        <span className="ml-2 text-white">Loading genres...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">Genre Management</h2>
          <p className="text-gray-300">Manage genres used across creators and content</p>
        </div>
        {onClose && (
          <Button onClick={onClose} variant="outline" className="text-white border-white/30">
            Close
          </Button>
        )}
      </div>

      {/* Result Message */}
      {result && (
        <Card className={`border-2 ${result.success ? 'border-green-500 bg-green-500/10' : 'border-red-500 bg-red-500/10'}`}>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              {result.success ? (
                <CheckCircle className="h-5 w-5 text-green-400" />
              ) : (
                <AlertCircle className="h-5 w-5 text-red-400" />
              )}
              <span className="text-white">{result.message}</span>
              <Button
                onClick={() => setResult(null)}
                size="sm"
                variant="ghost"
                className="ml-auto text-white hover:bg-white/10"
              >
                ×
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-white/10 border-white/20 text-white">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <BarChart3 className="h-8 w-8 text-purple-400" />
              <div>
                <div className="text-2xl font-bold">{genres.length}</div>
                <div className="text-sm text-gray-300">Total Genres</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-white/10 border-white/20 text-white">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Users className="h-8 w-8 text-blue-400" />
              <div>
                <div className="text-2xl font-bold">
                  {genres.reduce((sum, genre) => sum + genre.count, 0)}
                </div>
                <div className="text-sm text-gray-300">Total Assignments</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-white/10 border-white/20 text-white">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Merge className="h-8 w-8 text-green-400" />
              <div>
                <div className="text-2xl font-bold">{mergingGenres.length}</div>
                <div className="text-sm text-gray-300">Selected for Merge</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Add New Genre */}
      <Card className="bg-white/10 border-white/20 text-white">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Add New Genre
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Input
              value={newGenreName}
              onChange={(e) => setNewGenreName(e.target.value)}
              placeholder="Enter genre name..."
              className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
              onKeyPress={(e) => e.key === 'Enter' && handleAddGenre()}
            />
            <Button
              onClick={handleAddGenre}
              disabled={!newGenreName.trim() || actionLoading === 'add'}
              className="bg-purple-600 hover:bg-purple-700"
            >
              {actionLoading === 'add' ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Plus className="h-4 w-4" />
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Genre List */}
      <Card className="bg-white/10 border-white/20 text-white">
        <CardHeader>
          <CardTitle>Current Genres ({genres.length})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {genres.map((genre) => (
              <div
                key={genre.name}
                className={`flex items-center justify-between p-3 rounded-lg border transition-colors ${
                  mergingGenres.includes(genre.name)
                    ? 'border-purple-400 bg-purple-400/20'
                    : 'border-white/20 bg-white/5'
                }`}
              >
                <div className="flex items-center gap-3">
                  <input
                    type="checkbox"
                    checked={mergingGenres.includes(genre.name)}
                    onChange={() => toggleGenreForMerge(genre.name)}
                    className="rounded"
                  />
                  <div>
                    <div className="font-semibold">{genre.name}</div>
                    <div className="text-sm text-gray-400">
                      {genre.count} creators ({genre.percentage}%)
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-gray-300 border-gray-500">
                    {genre.count}
                  </Badge>

                  {/* Edit Button */}
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="text-blue-400 hover:bg-blue-400/20"
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="bg-gray-900 border-gray-700 text-white">
                      <DialogHeader>
                        <DialogTitle>Edit Genre: {genre.name}</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div>
                          <label className="text-sm text-gray-300">New Genre Name</label>
                          <Input
                            value={editingGenre?.name === genre.name ? editGenreName : genre.name}
                            onChange={(e) => {
                              setEditingGenre(genre)
                              setEditGenreName(e.target.value)
                            }}
                            className="bg-white/10 border-white/20 text-white"
                            placeholder={genre.name}
                          />
                        </div>
                        <div className="text-sm text-gray-400">
                          This will affect {genre.count} creators: {genre.creators.slice(0, 3).join(', ')}
                          {genre.creators.length > 3 && ` and ${genre.creators.length - 3} more`}
                        </div>
                        <div className="flex gap-2">
                          <Button
                            onClick={() => {
                              setEditingGenre(genre)
                              handleEditGenre()
                            }}
                            disabled={!editGenreName.trim() || actionLoading === 'edit'}
                            className="bg-blue-600 hover:bg-blue-700"
                          >
                            {actionLoading === 'edit' ? (
                              <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            ) : null}
                            Update Genre
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>

                  {/* Delete Button */}
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button
                        size="sm"
                        variant="ghost"
                        className="text-red-400 hover:bg-red-400/20"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="bg-gray-900 border-gray-700 text-white">
                      <DialogHeader>
                        <DialogTitle>Delete Genre: {genre.name}</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <div className="text-sm text-gray-300">
                          This genre is used by {genre.count} creators. What would you like to do?
                        </div>
                        <div>
                          <label className="text-sm text-gray-300">Replacement Genre (optional)</label>
                          <Select
                            value={deletingGenre?.name === genre.name ? replacementGenre : ''}
                            onValueChange={(value) => {
                              setDeletingGenre(genre)
                              setReplacementGenre(value)
                            }}
                          >
                            <SelectTrigger className="bg-white/10 border-white/20 text-white">
                              <SelectValue placeholder="Select replacement genre..." />
                            </SelectTrigger>
                            <SelectContent className="bg-gray-800 border-gray-700">
                              {genres
                                .filter(g => g.name !== genre.name)
                                .map(g => (
                                  <SelectItem key={g.name} value={g.name} className="text-white">
                                    {g.name} ({g.count} creators)
                                  </SelectItem>
                                ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="text-xs text-gray-400">
                          {(deletingGenre?.name === genre.name && replacementGenre)
                            ? `Creators will be reassigned to "${replacementGenre}"`
                            : 'Genre will be removed from all creators'
                          }
                        </div>
                        <div className="flex gap-2">
                          <Button
                            onClick={() => {
                              setDeletingGenre(genre)
                              handleDeleteGenre()
                            }}
                            disabled={actionLoading === 'delete'}
                            className="bg-red-600 hover:bg-red-700"
                          >
                            {actionLoading === 'delete' ? (
                              <Loader2 className="h-4 w-4 animate-spin mr-2" />
                            ) : null}
                            Delete Genre
                          </Button>
                        </div>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Merge Genres */}
      {mergingGenres.length >= 2 && (
        <Card className="bg-purple-600/20 border-purple-400/30 text-white">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Merge className="h-5 w-5" />
              Merge Selected Genres
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="text-sm text-gray-300 mb-2">Selected genres to merge:</div>
                <div className="flex flex-wrap gap-2">
                  {mergingGenres.map(genre => (
                    <Badge key={genre} className="bg-purple-600 text-white">
                      {genre}
                    </Badge>
                  ))}
                </div>
              </div>
              <div>
                <label className="text-sm text-gray-300">Target Genre Name</label>
                <Input
                  value={mergeTarget}
                  onChange={(e) => setMergeTarget(e.target.value)}
                  placeholder="Enter the name for the merged genre..."
                  className="bg-white/10 border-white/20 text-white placeholder:text-gray-400"
                />
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={handleMergeGenres}
                  disabled={!mergeTarget.trim() || actionLoading === 'merge'}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  {actionLoading === 'merge' ? (
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  ) : null}
                  Merge Genres
                </Button>
                <Button
                  onClick={() => setMergingGenres([])}
                  variant="outline"
                  className="text-white border-white/30"
                >
                  Cancel
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

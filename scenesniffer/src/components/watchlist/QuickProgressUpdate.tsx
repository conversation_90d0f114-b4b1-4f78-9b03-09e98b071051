'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { useWatchlistActions } from '@/hooks/useWatchlistActions'
import { 
  Play, 
  SkipForward, 
  CheckCircle, 
  Plus,
  Minus,
  Clock
} from 'lucide-react'

interface QuickProgressUpdateProps {
  item: any
  onUpdate?: () => void
  compact?: boolean
}

export function QuickProgressUpdate({ item, onUpdate, compact = false }: QuickProgressUpdateProps) {
  const [localProgress, setLocalProgress] = useState(item.progress_percentage || 0)
  
  const { 
    updateProgress, 
    markEpisodeWatched, 
    nextSeason, 
    markCompleted,
    startWatching,
    isActionLoading 
  } = useWatchlistActions()

  const isMovie = item.content_type === 'movie'
  const currentSeason = item.current_season || 1
  const currentEpisode = item.current_episode || 1
  const progress = item.progress_percentage || 0

  const handleQuickAction = async (action: string) => {
    switch (action) {
      case 'start_watching':
        await startWatching(item.id, item.title)
        break
      case 'mark_episode_watched':
        await markEpisodeWatched(item.id, item.title)
        break
      case 'next_season':
        await nextSeason(item.id, item.title)
        break
      case 'mark_completed':
        await markCompleted(item.id, item.title)
        break
      case 'progress_25':
        await updateProgress(item.id, 25, item.title)
        break
      case 'progress_50':
        await updateProgress(item.id, 50, item.title)
        break
      case 'progress_75':
        await updateProgress(item.id, 75, item.title)
        break
    }
    onUpdate?.()
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'watching': return 'bg-green-600'
      case 'completed': return 'bg-blue-600'
      case 'on_hold': return 'bg-yellow-600'
      case 'dropped': return 'bg-red-600'
      default: return 'bg-gray-600'
    }
  }

  if (compact) {
    return (
      <div className="space-y-2">
        {/* Progress bar */}
        {item.status === 'watching' && (
          <div>
            <div className="flex items-center justify-between text-xs mb-1">
              <span>{Math.round(progress)}% complete</span>
              {!isMovie && (
                <span>S{currentSeason.toString().padStart(2, '0')}E{currentEpisode.toString().padStart(2, '0')}</span>
              )}
            </div>
            <Progress value={progress} className="h-1" />
          </div>
        )}

        {/* Quick action buttons */}
        <div className="flex gap-1">
          {item.status === 'want_to_watch' && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuickAction('start_watching')}
              disabled={isActionLoading(`start-${item.id}`)}
              className="flex-1 border-white/20 text-white hover:bg-green-600/20 text-xs"
            >
              <Play className="h-3 w-3 mr-1" />
              Start
            </Button>
          )}

          {item.status === 'watching' && !isMovie && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleQuickAction('mark_episode_watched')}
              disabled={isActionLoading(`episode-${item.id}`)}
              className="flex-1 border-white/20 text-white hover:bg-blue-600/20 text-xs"
            >
              <SkipForward className="h-3 w-3 mr-1" />
              Next Ep
            </Button>
          )}

          {item.status === 'watching' && isMovie && progress < 100 && (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickAction('progress_50')}
                disabled={isActionLoading(`progress-${item.id}`)}
                className="border-white/20 text-white hover:bg-blue-600/20 text-xs px-2"
              >
                50%
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickAction('progress_75')}
                disabled={isActionLoading(`progress-${item.id}`)}
                className="border-white/20 text-white hover:bg-blue-600/20 text-xs px-2"
              >
                75%
              </Button>
            </>
          )}

          <Button
            variant="outline"
            size="sm"
            onClick={() => handleQuickAction('mark_completed')}
            disabled={isActionLoading(`complete-${item.id}`)}
            className="border-white/20 text-white hover:bg-green-600/20 text-xs px-2"
          >
            <CheckCircle className="h-3 w-3" />
          </Button>
        </div>
      </div>
    )
  }

  // Full version
  return (
    <div className="space-y-4 p-4 bg-white/5 rounded-lg">
      {/* Item header */}
      <div className="flex items-center justify-between">
        <div>
          <h4 className="font-medium text-white">{item.title}</h4>
          <div className="flex items-center gap-2 mt-1">
            <Badge className={`${getStatusColor(item.status)} text-white text-xs`}>
              {item.status.replace('_', ' ')}
            </Badge>
            <Badge variant="outline" className="border-white/20 text-white text-xs">
              {isMovie ? 'Movie' : 'TV Show'}
            </Badge>
          </div>
        </div>
      </div>

      {/* Progress display */}
      {item.status === 'watching' && (
        <div>
          <div className="flex items-center justify-between text-sm mb-2">
            <span>Progress: {Math.round(progress)}%</span>
            {!isMovie && (
              <span>Season {currentSeason}, Episode {currentEpisode}</span>
            )}
          </div>
          <Progress value={progress} className="h-2 mb-3" />
        </div>
      )}

      {/* Action buttons */}
      <div className="space-y-2">
        {item.status === 'want_to_watch' && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleQuickAction('start_watching')}
            disabled={isActionLoading(`start-${item.id}`)}
            className="w-full border-white/20 text-white hover:bg-green-600/20"
          >
            <Play className="h-4 w-4 mr-2" />
            Start Watching
          </Button>
        )}

        {item.status === 'watching' && (
          <div className="grid grid-cols-2 gap-2">
            {isMovie ? (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction('progress_25')}
                  disabled={isActionLoading(`progress-${item.id}`)}
                  className="border-white/20 text-white hover:bg-blue-600/20"
                >
                  25%
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction('progress_50')}
                  disabled={isActionLoading(`progress-${item.id}`)}
                  className="border-white/20 text-white hover:bg-blue-600/20"
                >
                  50%
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction('progress_75')}
                  disabled={isActionLoading(`progress-${item.id}`)}
                  className="border-white/20 text-white hover:bg-blue-600/20"
                >
                  75%
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction('mark_completed')}
                  disabled={isActionLoading(`complete-${item.id}`)}
                  className="border-white/20 text-white hover:bg-green-600/20"
                >
                  <CheckCircle className="h-4 w-4 mr-1" />
                  Done
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction('mark_episode_watched')}
                  disabled={isActionLoading(`episode-${item.id}`)}
                  className="border-white/20 text-white hover:bg-blue-600/20"
                >
                  <SkipForward className="h-4 w-4 mr-1" />
                  Next Episode
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction('next_season')}
                  disabled={isActionLoading(`season-${item.id}`)}
                  className="border-white/20 text-white hover:bg-purple-600/20"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Next Season
                </Button>
              </>
            )}
          </div>
        )}

        {item.status !== 'completed' && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleQuickAction('mark_completed')}
            disabled={isActionLoading(`complete-${item.id}`)}
            className="w-full border-white/20 text-white hover:bg-green-600/20"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Mark as Completed
          </Button>
        )}
      </div>
    </div>
  )
}

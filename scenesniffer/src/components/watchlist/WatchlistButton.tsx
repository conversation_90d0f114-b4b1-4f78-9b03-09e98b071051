'use client'

import React, { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useSingleWatchlistStatus } from '@/hooks/useWatchlistStatus'
import { useWatchlistActions } from '@/hooks/useWatchlistActions'
import { 
  Bookmark, 
  BookmarkCheck, 
  Play, 
  CheckCircle, 
  Clock, 
  Pause,
  Loader2
} from 'lucide-react'

interface WatchlistButtonProps {
  tmdbId: number
  contentType: 'movie' | 'tv'
  title: string
  posterPath?: string
  totalRuntime?: number
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'sm' | 'default' | 'lg'
  showStatus?: boolean
  className?: string
}

export function WatchlistButton({
  tmdbId,
  contentType,
  title,
  posterPath,
  totalRuntime,
  variant = 'outline',
  size = 'default',
  showStatus = false,
  className = ''
}: WatchlistButtonProps) {
  const { 
    inWatchlist, 
    status, 
    watchlistId, 
    loading: statusLoading,
    markAsAdded,
    markAsRemoved 
  } = useSingleWatchlistStatus(tmdbId, contentType)

  const { 
    addToWatchlist, 
    removeFromWatchlist, 
    isActionLoading 
  } = useWatchlistActions()

  const isLoading = statusLoading || 
                   isActionLoading(`add-${tmdbId}-${contentType}`) || 
                   isActionLoading(`remove-${watchlistId}`)

  const handleToggleWatchlist = async () => {
    if (inWatchlist && watchlistId) {
      const success = await removeFromWatchlist(watchlistId, title)
      if (success) {
        markAsRemoved()
      }
    } else {
      const success = await addToWatchlist({
        tmdb_id: tmdbId,
        content_type: contentType,
        title,
        poster_path: posterPath,
        total_runtime: totalRuntime
      })
      if (success) {
        // The success response should include the new watchlist item
        // For now, we'll mark as added with default status
        markAsAdded('temp-id', 'want_to_watch')
      }
    }
  }

  const getStatusIcon = () => {
    if (isLoading) {
      return <Loader2 className="h-4 w-4 animate-spin" />
    }

    if (!inWatchlist) {
      return <Bookmark className="h-4 w-4" />
    }

    switch (status) {
      case 'watching':
        return <Play className="h-4 w-4" />
      case 'completed':
        return <CheckCircle className="h-4 w-4" />
      case 'on_hold':
        return <Pause className="h-4 w-4" />
      case 'dropped':
        return <BookmarkCheck className="h-4 w-4 opacity-50" />
      default:
        return <BookmarkCheck className="h-4 w-4" />
    }
  }

  const getStatusText = () => {
    if (isLoading) {
      return inWatchlist ? 'Removing...' : 'Adding...'
    }

    if (!inWatchlist) {
      return 'Add to Watchlist'
    }

    switch (status) {
      case 'want_to_watch':
        return 'In Watchlist'
      case 'watching':
        return 'Currently Watching'
      case 'completed':
        return 'Completed'
      case 'on_hold':
        return 'On Hold'
      case 'dropped':
        return 'Dropped'
      default:
        return 'In Watchlist'
    }
  }

  const getStatusColor = () => {
    if (!inWatchlist) {
      return 'border-white/20 text-white hover:bg-white/10'
    }

    switch (status) {
      case 'watching':
        return 'border-green-500 text-green-400 hover:bg-green-500/20'
      case 'completed':
        return 'border-blue-500 text-blue-400 hover:bg-blue-500/20'
      case 'on_hold':
        return 'border-yellow-500 text-yellow-400 hover:bg-yellow-500/20'
      case 'dropped':
        return 'border-red-500 text-red-400 hover:bg-red-500/20'
      default:
        return 'border-purple-500 text-purple-400 hover:bg-purple-500/20'
    }
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <Button
        variant={variant}
        size={size}
        onClick={handleToggleWatchlist}
        disabled={isLoading}
        className={`${getStatusColor()} transition-colors`}
      >
        {getStatusIcon()}
        <span className="ml-2">{getStatusText()}</span>
      </Button>
      
      {showStatus && inWatchlist && status && (
        <Badge 
          variant="outline" 
          className={`text-xs ${getStatusColor()}`}
        >
          {status.replace('_', ' ')}
        </Badge>
      )}
    </div>
  )
}

// Compact version for use in cards and lists
export function WatchlistButtonCompact({
  tmdbId,
  contentType,
  title,
  posterPath,
  totalRuntime,
  className = ''
}: Omit<WatchlistButtonProps, 'variant' | 'size' | 'showStatus'>) {
  const { 
    inWatchlist, 
    status, 
    watchlistId, 
    loading: statusLoading,
    markAsAdded,
    markAsRemoved 
  } = useSingleWatchlistStatus(tmdbId, contentType)

  const { 
    addToWatchlist, 
    removeFromWatchlist, 
    isActionLoading 
  } = useWatchlistActions()

  const isLoading = statusLoading || 
                   isActionLoading(`add-${tmdbId}-${contentType}`) || 
                   isActionLoading(`remove-${watchlistId}`)

  const handleToggleWatchlist = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (inWatchlist && watchlistId) {
      const success = await removeFromWatchlist(watchlistId, title)
      if (success) {
        markAsRemoved()
      }
    } else {
      const success = await addToWatchlist({
        tmdb_id: tmdbId,
        content_type: contentType,
        title,
        poster_path: posterPath,
        total_runtime: totalRuntime
      })
      if (success) {
        markAsAdded('temp-id', 'want_to_watch')
      }
    }
  }

  const getIcon = () => {
    if (isLoading) {
      return <Loader2 className="h-4 w-4 animate-spin" />
    }

    if (!inWatchlist) {
      return <Bookmark className="h-4 w-4" />
    }

    switch (status) {
      case 'watching':
        return <Play className="h-4 w-4 text-green-400" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-blue-400" />
      case 'on_hold':
        return <Pause className="h-4 w-4 text-yellow-400" />
      case 'dropped':
        return <BookmarkCheck className="h-4 w-4 text-red-400 opacity-50" />
      default:
        return <BookmarkCheck className="h-4 w-4 text-purple-400" />
    }
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleToggleWatchlist}
      disabled={isLoading}
      className={`p-2 hover:bg-white/10 ${className}`}
      title={inWatchlist ? `Remove from watchlist (${status})` : 'Add to watchlist'}
    >
      {getIcon()}
    </Button>
  )
}

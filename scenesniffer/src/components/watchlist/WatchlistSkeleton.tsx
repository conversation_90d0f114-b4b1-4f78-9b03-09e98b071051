'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'

interface WatchlistSkeletonProps {
  count?: number
  variant?: 'grid' | 'list' | 'compact'
}

export function WatchlistSkeleton({ count = 3, variant = 'grid' }: WatchlistSkeletonProps) {
  if (variant === 'compact') {
    return (
      <div className="space-y-3">
        {Array.from({ length: count }).map((_, i) => (
          <div key={i} className="flex items-center gap-3 p-3 bg-white/5 rounded-lg animate-pulse">
            <div className="w-12 h-16 bg-white/20 rounded"></div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-white/20 rounded w-3/4"></div>
              <div className="h-3 bg-white/20 rounded w-1/2"></div>
            </div>
            <div className="w-8 h-8 bg-white/20 rounded"></div>
          </div>
        ))}
      </div>
    )
  }

  if (variant === 'list') {
    return (
      <div className="space-y-4">
        {Array.from({ length: count }).map((_, i) => (
          <Card key={i} className="bg-white/10 border-white/20 animate-pulse">
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                <div className="w-16 h-24 bg-white/20 rounded-lg"></div>
                <div className="flex-1 space-y-3">
                  <div className="h-5 bg-white/20 rounded w-3/4"></div>
                  <div className="flex gap-2">
                    <div className="h-6 bg-white/20 rounded w-20"></div>
                    <div className="h-6 bg-white/20 rounded w-16"></div>
                  </div>
                  <div className="h-2 bg-white/20 rounded w-full"></div>
                </div>
                <div className="flex gap-2">
                  <div className="w-8 h-8 bg-white/20 rounded"></div>
                  <div className="w-8 h-8 bg-white/20 rounded"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  // Grid variant (default)
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      {Array.from({ length: count }).map((_, i) => (
        <Card key={i} className="bg-white/10 border-white/20 animate-pulse">
          <CardContent className="p-4">
            <div className="aspect-[2/3] bg-white/20 rounded-lg mb-3"></div>
            <div className="space-y-2">
              <div className="h-4 bg-white/20 rounded w-3/4"></div>
              <div className="flex justify-between items-center">
                <div className="h-6 bg-white/20 rounded w-16"></div>
                <div className="h-4 bg-white/20 rounded w-8"></div>
              </div>
              <div className="flex gap-1 pt-2">
                <div className="h-8 bg-white/20 rounded flex-1"></div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

export function CurrentlyWatchingSkeleton({ count = 3 }: { count?: number }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {Array.from({ length: count }).map((_, i) => (
        <Card key={i} className="bg-white/10 border-white/20 animate-pulse">
          <CardContent className="p-4">
            <div className="relative aspect-video bg-white/20 rounded-lg mb-3">
              <div className="absolute bottom-0 left-0 right-0 bg-black/60 p-2">
                <div className="h-2 bg-white/20 rounded mb-1"></div>
                <div className="h-1 bg-white/20 rounded"></div>
              </div>
            </div>
            <div className="space-y-2">
              <div className="h-4 bg-white/20 rounded w-3/4"></div>
              <div className="h-3 bg-white/20 rounded w-1/2"></div>
              <div className="flex gap-1 pt-2">
                <div className="h-8 bg-white/20 rounded flex-1"></div>
                <div className="h-8 bg-white/20 rounded w-12"></div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

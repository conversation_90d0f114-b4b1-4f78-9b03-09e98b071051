'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Slider } from '@/components/ui/slider'
import { useWatchlistActions } from '@/hooks/useWatchlistActions'
import { 
  Play, 
  Pause, 
  SkipForward, 
  CheckCircle, 
  Clock, 
  Star,
  Plus,
  Minus,
  RotateCcw
} from 'lucide-react'

interface ProgressTrackerProps {
  item: any
  trigger?: React.ReactNode
  onUpdate?: () => void
}

export function ProgressTracker({ item, trigger, onUpdate }: ProgressTrackerProps) {
  const [open, setOpen] = useState(false)
  const [progress, setProgress] = useState(item.progress_percentage || 0)
  const [currentSeason, setCurrentSeason] = useState(item.current_season || 1)
  const [currentEpisode, setCurrentEpisode] = useState(item.current_episode || 1)
  const [rating, setRating] = useState(item.user_rating || 0)
  const [notes, setNotes] = useState(item.personal_notes || '')
  const [status, setStatus] = useState(item.status || 'want_to_watch')

  const { 
    updateProgress, 
    markEpisodeWatched, 
    nextSeason, 
    markCompleted,
    updateRating,
    updateStatus,
    updateNotes,
    isActionLoading 
  } = useWatchlistActions()

  const isMovie = item.content_type === 'movie'
  const isLoading = isActionLoading(`progress-${item.id}`) || 
                   isActionLoading(`episode-${item.id}`) || 
                   isActionLoading(`season-${item.id}`)

  // Reset form when item changes
  useEffect(() => {
    setProgress(item.progress_percentage || 0)
    setCurrentSeason(item.current_season || 1)
    setCurrentEpisode(item.current_episode || 1)
    setRating(item.user_rating || 0)
    setNotes(item.personal_notes || '')
    setStatus(item.status || 'want_to_watch')
  }, [item])

  const handleProgressUpdate = async () => {
    if (isMovie) {
      await updateProgress(item.id, progress, item.title)
    } else {
      await updateProgress(item.id, progress, item.title)
    }
    onUpdate?.()
  }

  const handleEpisodeUpdate = async () => {
    await updateProgress(item.id, { 
      current_season: currentSeason, 
      current_episode: currentEpisode 
    })
    onUpdate?.()
  }

  const handleQuickAction = async (action: string) => {
    switch (action) {
      case 'start_watching':
        await updateStatus(item.id, 'watching', item.title)
        break
      case 'mark_episode_watched':
        await markEpisodeWatched(item.id, item.title)
        break
      case 'next_season':
        await nextSeason(item.id, item.title)
        break
      case 'mark_completed':
        await markCompleted(item.id, item.title)
        break
    }
    onUpdate?.()
  }

  const handleRatingUpdate = async () => {
    if (rating > 0) {
      await updateRating(item.id, rating, item.title)
      onUpdate?.()
    }
  }

  const handleNotesUpdate = async () => {
    await updateNotes(item.id, notes, item.title)
    onUpdate?.()
  }

  const getStatusColor = (statusValue: string) => {
    switch (statusValue) {
      case 'watching': return 'bg-green-600'
      case 'completed': return 'bg-blue-600'
      case 'on_hold': return 'bg-yellow-600'
      case 'dropped': return 'bg-red-600'
      default: return 'bg-gray-600'
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm" className="border-white/20 text-white hover:bg-white/10">
            <Play className="h-4 w-4 mr-2" />
            Update Progress
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="bg-gray-900 border-gray-700 text-white max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Play className="h-5 w-5 text-purple-400" />
            Update Progress
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Item Info */}
          <div className="flex items-center gap-3 p-3 bg-white/5 rounded-lg">
            <div className="w-12 h-16 bg-gradient-to-br from-purple-600 to-purple-800 rounded overflow-hidden">
              {item.poster_path && (
                <img
                  src={`https://image.tmdb.org/t/p/w200${item.poster_path}`}
                  alt={item.title}
                  className="w-full h-full object-cover"
                />
              )}
            </div>
            <div className="flex-1">
              <h3 className="font-semibold truncate">{item.title}</h3>
              <div className="flex items-center gap-2 mt-1">
                <Badge className={`${getStatusColor(status)} text-white text-xs`}>
                  {status.replace('_', ' ')}
                </Badge>
                <Badge variant="outline" className="border-white/20 text-white text-xs">
                  {isMovie ? 'Movie' : 'TV Show'}
                </Badge>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Quick Actions</Label>
            <div className="grid grid-cols-2 gap-2">
              {status === 'want_to_watch' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction('start_watching')}
                  disabled={isLoading}
                  className="border-white/20 text-white hover:bg-green-600/20"
                >
                  <Play className="h-4 w-4 mr-1" />
                  Start Watching
                </Button>
              )}
              
              {!isMovie && status === 'watching' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction('mark_episode_watched')}
                  disabled={isLoading}
                  className="border-white/20 text-white hover:bg-blue-600/20"
                >
                  <SkipForward className="h-4 w-4 mr-1" />
                  Episode Done
                </Button>
              )}

              {!isMovie && status === 'watching' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction('next_season')}
                  disabled={isLoading}
                  className="border-white/20 text-white hover:bg-purple-600/20"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Next Season
                </Button>
              )}

              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickAction('mark_completed')}
                disabled={isLoading}
                className="border-white/20 text-white hover:bg-green-600/20"
              >
                <CheckCircle className="h-4 w-4 mr-1" />
                Mark Complete
              </Button>
            </div>
          </div>

          {/* Progress Tracking */}
          {isMovie ? (
            <div className="space-y-3">
              <Label className="text-sm font-medium">Movie Progress</Label>
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span>Progress: {Math.round(progress)}%</span>
                  <span>{progress >= 100 ? 'Completed' : 'In Progress'}</span>
                </div>
                <Slider
                  value={[progress]}
                  onValueChange={(value) => setProgress(value[0])}
                  max={100}
                  step={5}
                  className="w-full"
                />
                <Progress value={progress} className="h-2" />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleProgressUpdate}
                  disabled={isLoading}
                  className="w-full border-white/20 text-white hover:bg-white/10"
                >
                  Update Progress
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <Label className="text-sm font-medium">TV Show Progress</Label>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label className="text-xs text-gray-400">Season</Label>
                  <div className="flex items-center gap-1 mt-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentSeason(Math.max(1, currentSeason - 1))}
                      disabled={currentSeason <= 1}
                      className="border-white/20 text-white hover:bg-white/10 px-2"
                    >
                      <Minus className="h-3 w-3" />
                    </Button>
                    <Input
                      type="number"
                      value={currentSeason}
                      onChange={(e) => setCurrentSeason(Math.max(1, parseInt(e.target.value) || 1))}
                      className="text-center bg-white/10 border-white/20 text-white"
                      min="1"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentSeason(currentSeason + 1)}
                      className="border-white/20 text-white hover:bg-white/10 px-2"
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
                <div>
                  <Label className="text-xs text-gray-400">Episode</Label>
                  <div className="flex items-center gap-1 mt-1">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentEpisode(Math.max(1, currentEpisode - 1))}
                      disabled={currentEpisode <= 1}
                      className="border-white/20 text-white hover:bg-white/10 px-2"
                    >
                      <Minus className="h-3 w-3" />
                    </Button>
                    <Input
                      type="number"
                      value={currentEpisode}
                      onChange={(e) => setCurrentEpisode(Math.max(1, parseInt(e.target.value) || 1))}
                      className="text-center bg-white/10 border-white/20 text-white"
                      min="1"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentEpisode(currentEpisode + 1)}
                      className="border-white/20 text-white hover:bg-white/10 px-2"
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleEpisodeUpdate}
                disabled={isLoading}
                className="w-full border-white/20 text-white hover:bg-white/10"
              >
                Update Episode
              </Button>
            </div>
          )}

          {/* Rating */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Your Rating</Label>
            <div className="flex items-center gap-2">
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((star) => (
                <button
                  key={star}
                  onClick={() => setRating(star)}
                  className={`p-1 rounded transition-colors ${
                    star <= rating 
                      ? 'text-yellow-400' 
                      : 'text-gray-600 hover:text-yellow-400'
                  }`}
                >
                  <Star className={`h-4 w-4 ${star <= rating ? 'fill-current' : ''}`} />
                </button>
              ))}
              <span className="text-sm ml-2">{rating}/10</span>
              {rating > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleRatingUpdate}
                  disabled={isLoading}
                  className="text-white hover:bg-white/10 ml-2"
                >
                  Save
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

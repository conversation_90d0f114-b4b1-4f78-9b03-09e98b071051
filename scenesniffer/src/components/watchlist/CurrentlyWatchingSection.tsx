'use client'

import React, { useEffect, useRef } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { useWatchlist } from '@/contexts/WatchlistContext'
import { useWatchlistActions } from '@/hooks/useWatchlistActions'
import { 
  Play, 
  Pause, 
  SkipForward, 
  CheckCircle, 
  Clock, 
  Calendar,
  TrendingUp,
  Eye
} from 'lucide-react'
import Link from 'next/link'

interface CurrentlyWatchingSectionProps {
  limit?: number
  showHeader?: boolean
  className?: string
}

export function CurrentlyWatchingSection({
  limit = 6,
  showHeader = true,
  className = ''
}: CurrentlyWatchingSectionProps) {
  const { state, actions } = useWatchlist()
  const { markEpisodeWatched, nextSeason, markCompleted, isActionLoading } = useWatchlistActions()

  // Re-enable automatic fetching to test infinite loop fix
  useEffect(() => {
    // Only fetch if we don't have data and we're not already loading
    if (state.currentlyWatching.length === 0 && !state.loading) {
      console.log('🔍 CurrentlyWatchingSection: Fetching currently watching items')
      actions.fetchCurrentlyWatching()
    }
  }, []) // Empty dependency array - only run on mount

  const currentlyWatching = state.currentlyWatching.slice(0, limit)

  if (state.loading) {
    return (
      <div className={className}>
        {showHeader && (
          <div className="flex items-center gap-2 mb-6">
            <Play className="h-6 w-6 text-purple-400" />
            <h2 className="text-2xl font-bold text-white">Continue Watching</h2>
          </div>
        )}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <Card key={i} className="bg-white/10 border-white/20 animate-pulse">
              <CardContent className="p-4">
                <div className="aspect-video bg-white/20 rounded-lg mb-3"></div>
                <div className="h-4 bg-white/20 rounded mb-2"></div>
                <div className="h-3 bg-white/20 rounded w-2/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (currentlyWatching.length === 0) {
    return (
      <div className={className}>
        {showHeader && (
          <div className="flex items-center gap-2 mb-6">
            <Play className="h-6 w-6 text-purple-400" />
            <h2 className="text-2xl font-bold text-white">Continue Watching</h2>
          </div>
        )}
        <Card className="bg-white/10 border-white/20 text-white">
          <CardContent className="p-8 text-center">
            <Eye className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Nothing Currently Watching</h3>
            <p className="text-gray-300 mb-4">
              Start watching something from your watchlist or discover new content!
            </p>
            <div className="flex gap-2 justify-center">
              <Button
                variant="outline"
                onClick={() => actions.fetchCurrentlyWatching()}
                className="border-white/20 text-white hover:bg-white/10"
              >
                Load Currently Watching
              </Button>
              <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
                <Link href="/watchlist">View Watchlist</Link>
              </Button>
              <Button variant="outline" className="border-white/20 text-white hover:bg-white/10">
                <Link href="/feed">Discover Content</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={className}>
      {showHeader && (
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Play className="h-6 w-6 text-purple-400" />
            <h2 className="text-2xl font-bold text-white">Continue Watching</h2>
            <Badge variant="secondary" className="bg-purple-600 text-white">
              {currentlyWatching.length}
            </Badge>
          </div>
          {currentlyWatching.length > 0 && (
            <Button 
              variant="outline" 
              size="sm"
              className="border-white/20 text-white hover:bg-white/10"
            >
              <Link href="/watchlist?status=watching">View All</Link>
            </Button>
          )}
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {currentlyWatching.map((item) => (
          <CurrentlyWatchingCard 
            key={item.id} 
            item={item}
            onEpisodeWatched={markEpisodeWatched}
            onNextSeason={nextSeason}
            onMarkCompleted={markCompleted}
            isActionLoading={isActionLoading}
          />
        ))}
      </div>
    </div>
  )
}

interface CurrentlyWatchingCardProps {
  item: any
  onEpisodeWatched: (id: string, title?: string) => Promise<boolean>
  onNextSeason: (id: string, title?: string) => Promise<boolean>
  onMarkCompleted: (id: string, title: string) => Promise<boolean>
  isActionLoading: (key: string) => boolean
}

function CurrentlyWatchingCard({ 
  item, 
  onEpisodeWatched, 
  onNextSeason, 
  onMarkCompleted, 
  isActionLoading 
}: CurrentlyWatchingCardProps) {
  const progress = item.progress_percentage || 0
  const isMovie = item.content_type === 'movie'
  const currentSeason = item.current_season || 1
  const currentEpisode = item.current_episode || 1

  // Calculate time since last watched
  const daysSinceWatched = item.last_watched_at 
    ? Math.floor((new Date().getTime() - new Date(item.last_watched_at).getTime()) / (1000 * 60 * 60 * 24))
    : null

  const getStatusColor = () => {
    if (!daysSinceWatched) return 'text-green-400'
    if (daysSinceWatched <= 1) return 'text-green-400'
    if (daysSinceWatched <= 7) return 'text-yellow-400'
    return 'text-red-400'
  }

  const getStatusText = () => {
    if (!daysSinceWatched) return 'Recently started'
    if (daysSinceWatched === 0) return 'Watched today'
    if (daysSinceWatched === 1) return 'Watched yesterday'
    if (daysSinceWatched <= 7) return `${daysSinceWatched} days ago`
    return `${daysSinceWatched} days ago`
  }

  return (
    <Card className="bg-white/10 border-white/20 text-white hover:bg-white/15 transition-colors group">
      <CardContent className="p-4">
        {/* Poster/Thumbnail */}
        <div className="relative aspect-video bg-gradient-to-br from-purple-600 to-purple-800 rounded-lg mb-3 overflow-hidden">
          {item.display_poster && (
            <img
              src={`https://image.tmdb.org/t/p/w500${item.display_poster}`}
              alt={item.display_title}
              className="w-full h-full object-cover"
              onError={(e) => {
                e.currentTarget.style.display = 'none'
              }}
            />
          )}
          
          {/* Progress overlay */}
          <div className="absolute bottom-0 left-0 right-0 bg-black/60 p-2">
            <div className="flex items-center justify-between text-xs mb-1">
              <span>{Math.round(progress)}% complete</span>
              {!isMovie && (
                <span>S{currentSeason.toString().padStart(2, '0')}E{currentEpisode.toString().padStart(2, '0')}</span>
              )}
            </div>
            <Progress value={progress} className="h-1" />
          </div>

          {/* Play button overlay */}
          <div className="absolute inset-0 bg-black/40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity">
            <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
              <Play className="h-4 w-4 mr-1" />
              Continue
            </Button>
          </div>
        </div>

        {/* Content Info */}
        <div className="space-y-2">
          <h3 className="font-semibold text-white truncate" title={item.display_title}>
            {item.display_title}
          </h3>

          {/* Status and time info */}
          <div className="flex items-center gap-2 text-xs">
            <div className={`flex items-center gap-1 ${getStatusColor()}`}>
              <Clock className="h-3 w-3" />
              <span>{getStatusText()}</span>
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex gap-1 pt-2">
            {isMovie ? (
              <Button
                size="sm"
                variant="outline"
                className="flex-1 border-white/20 text-white hover:bg-white/10 text-xs"
                onClick={() => onMarkCompleted(item.id, item.display_title)}
                disabled={isActionLoading(`complete-${item.id}`)}
              >
                <CheckCircle className="h-3 w-3 mr-1" />
                Complete
              </Button>
            ) : (
              <>
                <Button
                  size="sm"
                  variant="outline"
                  className="flex-1 border-white/20 text-white hover:bg-white/10 text-xs"
                  onClick={() => onEpisodeWatched(item.id, item.display_title)}
                  disabled={isActionLoading(`episode-${item.id}`)}
                >
                  <SkipForward className="h-3 w-3 mr-1" />
                  Next Ep
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  className="border-white/20 text-white hover:bg-white/10 text-xs px-2"
                  onClick={() => onNextSeason(item.id, item.display_title)}
                  disabled={isActionLoading(`season-${item.id}`)}
                >
                  S+
                </Button>
              </>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

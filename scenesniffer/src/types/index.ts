// User and Authentication Types
export interface User {
  id: string
  email: string
  username?: string
  avatar_url?: string
  created_at: string
  updated_at: string
}

export interface UserPreferences {
  id: string
  user_id: string
  genres: string[]
  streaming_services: string[]
  created_at: string
  updated_at: string
}

// Creator Types
export interface Creator {
  id: string
  name: string
  platform: 'youtube' | 'instagram' | 'twitter' | 'tiktok'
  handle: string
  avatar_url?: string
  follower_count?: number
  verified: boolean
  trust_score: number
  genres: string[]
  created_at: string
  updated_at: string
}

export interface UserCreator {
  id: string
  user_id: string
  creator_id: string
  created_at: string
}

// Content Types
export interface Content {
  id: string
  creator_id: string
  title: string
  description?: string
  content_type: 'review' | 'theory' | 'news' | 'spoiler-free' | 'breakdown' | 'recommendation'
  platform_url: string
  platform_id: string
  thumbnail_url?: string
  published_at: string
  ai_summary?: string
  referenced_titles: string[]
  tags: string[]
  created_at: string
  updated_at: string
}

// Movie/Show Types
export interface Movie {
  id: string
  tmdb_id: number
  title: string
  overview?: string
  poster_path?: string
  backdrop_path?: string
  release_date?: string
  genres: string[]
  runtime?: number
  vote_average?: number
  vote_count?: number
  imdb_id?: string
  created_at: string
  updated_at: string
}

export interface TVShow {
  id: string
  tmdb_id: number
  name: string
  overview?: string
  poster_path?: string
  backdrop_path?: string
  first_air_date?: string
  last_air_date?: string
  genres: string[]
  episode_run_time?: number[]
  vote_average?: number
  vote_count?: number
  number_of_seasons?: number
  number_of_episodes?: number
  status?: string
  created_at: string
  updated_at: string
}

// Watchlist Types
export interface WatchlistItem {
  id: string
  user_id: string
  content_type: 'movie' | 'tv'
  tmdb_id: number
  title: string
  poster_path?: string
  added_at: string
  watched: boolean
  watched_at?: string
}

// Streaming Availability Types
export interface StreamingProvider {
  id: number
  name: string
  logo_path: string
  display_priority: number
}

export interface StreamingAvailability {
  id: string
  tmdb_id: number
  content_type: 'movie' | 'tv'
  provider_id: number
  region: string
  link?: string
  created_at: string
  updated_at: string
}

// Feed Types
export interface FeedItem {
  id: string
  content: Content
  creator: Creator
  referenced_movie?: Movie
  referenced_tv_show?: TVShow
  streaming_availability?: StreamingAvailability[]
  relevance_score: number
}

// API Response Types
export interface YouTubeVideo {
  id: string
  snippet: {
    title: string
    description: string
    thumbnails: {
      default: { url: string }
      medium: { url: string }
      high: { url: string }
    }
    publishedAt: string
    channelId: string
    channelTitle: string
  }
}

export interface TMDbMovie {
  id: number
  title: string
  overview: string
  poster_path: string
  backdrop_path: string
  release_date: string
  genre_ids: number[]
  runtime: number
  vote_average: number
  vote_count: number
  imdb_id: string
}

export interface TMDbTVShow {
  id: number
  name: string
  overview: string
  poster_path: string
  backdrop_path: string
  first_air_date: string
  genre_ids: number[]
  vote_average: number
  vote_count: number
  number_of_seasons: number
  number_of_episodes: number
}

// Onboarding Types
export interface OnboardingData {
  genres: string[]
  creators: {
    platform: string
    handle: string
  }[]
  streaming_services: string[]
}

// Creator Bundle Types
export interface CreatorBundle {
  id: string
  title: string
  description?: string
  tags: string[]
  platform: 'youtube' | 'instagram' | 'twitter' | 'tiktok' | 'twitch'
  creator_count: number
  refreshed_at: string
  ttl: number
  status: 'active' | 'inactive' | 'draft'
  pinned_creator_ids: string[]
  suppressed_creator_ids: string[]
  created_at: string
  updated_at: string
  creators?: BundleCreator[]
}

export interface BundleCreator {
  id: string
  bundle_id: string
  creator_id: string
  position: number
  score: number
  created_at: string
  creator?: Creator
}

export interface UserBundle {
  id: string
  user_id: string
  bundle_id: string
  added_at: string
  included_creator_ids: string[]
  bundle?: CreatorBundle
}

export interface BundleAnalytics {
  id: string
  bundle_id: string
  user_id?: string
  event_type: 'impression' | 'view' | 'add' | 'remove'
  metadata: Record<string, any>
  created_at: string
}

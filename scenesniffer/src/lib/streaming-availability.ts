import { tmdbAPI } from './tmdb'

export interface StreamingProvider {
  provider_id: number
  provider_name: string
  logo_path: string
  display_priority: number
}

export interface StreamingAvailability {
  title: string
  tmdb_id: number
  content_type: 'movie' | 'tv'
  providers: {
    flatrate?: StreamingProvider[]  // Subscription services
    rent?: StreamingProvider[]      // Rental services
    buy?: StreamingProvider[]       // Purchase services
  }
  poster_path?: string
  release_date?: string
  overview?: string
}

export interface ContentWithStreaming {
  id: string
  title: string
  referenced_titles: string[]
  streaming_availability: StreamingAvailability[]
}

export class StreamingAvailabilityService {
  private cache: Map<string, StreamingAvailability> = new Map()
  private cacheExpiry: Map<string, number> = new Map()
  private readonly CACHE_DURATION = 24 * 60 * 60 * 1000 // 24 hours

  /**
   * Get streaming availability for AI-referenced titles in content
   */
  async getStreamingForContent(
    referencedTitles: string[], 
    userStreamingServices: string[] = []
  ): Promise<StreamingAvailability[]> {
    if (!referencedTitles || referencedTitles.length === 0) {
      return []
    }

    console.log(`🎬 Looking up streaming for titles: ${referencedTitles.join(', ')}`)
    
    const results: StreamingAvailability[] = []
    
    for (const title of referencedTitles) {
      try {
        // Check cache first
        const cacheKey = title.toLowerCase().trim()
        const cached = this.getCachedResult(cacheKey)
        
        if (cached) {
          console.log(`📦 Cache hit for: ${title}`)
          results.push(cached)
          continue
        }

        // Search TMDb for the title
        const streamingData = await this.searchAndGetStreaming(title)
        
        if (streamingData) {
          // Cache the result
          this.setCachedResult(cacheKey, streamingData)
          results.push(streamingData)
          console.log(`✅ Found streaming for: ${title}`)
        } else {
          console.log(`❌ No streaming found for: ${title}`)
        }
        
        // Rate limiting - small delay between requests
        await new Promise(resolve => setTimeout(resolve, 100))
        
      } catch (error) {
        console.error(`Error getting streaming for ${title}:`, error)
      }
    }

    // Filter by user's preferred streaming services if provided
    if (userStreamingServices.length > 0) {
      return this.filterByUserServices(results, userStreamingServices)
    }

    return results
  }

  /**
   * Search TMDb and get streaming availability for a title
   */
  private async searchAndGetStreaming(title: string): Promise<StreamingAvailability | null> {
    try {
      // Search for both movies and TV shows
      const { movies, tvShows } = await tmdbAPI.searchMulti(title)
      
      // Try movies first (usually more popular)
      if (movies && movies.length > 0) {
        const movie = movies[0] // Take the first (most relevant) result
        const providers = await tmdbAPI.getMovieProviders(movie.id)
        
        if (providers && (providers.flatrate || providers.rent || providers.buy)) {
          return {
            title: movie.title,
            tmdb_id: movie.id,
            content_type: 'movie',
            providers: {
              flatrate: providers.flatrate || [],
              rent: providers.rent || [],
              buy: providers.buy || []
            },
            poster_path: movie.poster_path,
            release_date: movie.release_date,
            overview: movie.overview
          }
        }
      }

      // Try TV shows if no movie found or no streaming available
      if (tvShows && tvShows.length > 0) {
        const tvShow = tvShows[0]
        const providers = await tmdbAPI.getTVShowProviders(tvShow.id)
        
        if (providers && (providers.flatrate || providers.rent || providers.buy)) {
          return {
            title: tvShow.name,
            tmdb_id: tvShow.id,
            content_type: 'tv',
            providers: {
              flatrate: providers.flatrate || [],
              rent: providers.rent || [],
              buy: providers.buy || []
            },
            poster_path: tvShow.poster_path,
            release_date: tvShow.first_air_date,
            overview: tvShow.overview
          }
        }
      }

      return null
    } catch (error) {
      console.error(`Error searching streaming for ${title}:`, error)
      return null
    }
  }

  /**
   * Filter streaming results by user's preferred services
   */
  private filterByUserServices(
    results: StreamingAvailability[], 
    userServices: string[]
  ): StreamingAvailability[] {
    return results.map(result => {
      const filteredProviders: any = {}
      
      // Map common service names to TMDb provider IDs
      const serviceMapping: { [key: string]: number[] } = {
        'netflix': [8],
        'disney': [337, 390], // Disney+ and Disney Plus
        'hbo': [384, 31], // HBO Max and HBO
        'amazon': [9, 10], // Amazon Prime and Amazon Video
        'hulu': [15],
        'apple': [350], // Apple TV+
        'paramount': [531], // Paramount+
        'peacock': [386],
        'youtube': [188], // YouTube Premium
        'crunchyroll': [283]
      }
      
      // Get provider IDs for user's services
      const userProviderIds = userServices.flatMap(service => 
        serviceMapping[service.toLowerCase()] || []
      )
      
      // Filter each provider type
      Object.keys(result.providers).forEach(providerType => {
        const providers = result.providers[providerType as keyof typeof result.providers]
        if (providers) {
          const filtered = providers.filter(provider => 
            userProviderIds.includes(provider.provider_id)
          )
          if (filtered.length > 0) {
            filteredProviders[providerType] = filtered
          }
        }
      })
      
      return {
        ...result,
        providers: filteredProviders
      }
    }).filter(result => 
      // Only include results that have at least one matching provider
      Object.keys(result.providers).length > 0
    )
  }

  /**
   * Cache management
   */
  private getCachedResult(key: string): StreamingAvailability | null {
    const expiry = this.cacheExpiry.get(key)
    if (expiry && Date.now() < expiry) {
      return this.cache.get(key) || null
    }
    
    // Clean up expired cache
    this.cache.delete(key)
    this.cacheExpiry.delete(key)
    return null
  }

  private setCachedResult(key: string, data: StreamingAvailability): void {
    this.cache.set(key, data)
    this.cacheExpiry.set(key, Date.now() + this.CACHE_DURATION)
  }

  /**
   * Get popular streaming services for display
   */
  getPopularServices(): { id: string, name: string, logo?: string }[] {
    return [
      { id: 'netflix', name: 'Netflix' },
      { id: 'disney', name: 'Disney+' },
      { id: 'hbo', name: 'HBO Max' },
      { id: 'amazon', name: 'Prime Video' },
      { id: 'hulu', name: 'Hulu' },
      { id: 'apple', name: 'Apple TV+' },
      { id: 'paramount', name: 'Paramount+' },
      { id: 'peacock', name: 'Peacock' }
    ]
  }
}

// Export singleton instance
export const streamingService = new StreamingAvailabilityService()

// Reddit API integration for movie/TV content aggregation
import { createServiceRoleClient } from '@/lib/supabase-server'

interface RedditPost {
  id: string
  title: string
  selftext: string
  author: string
  subreddit: string
  permalink: string
  url: string
  thumbnail: string
  created_utc: number
  ups: number
  num_comments: number
  total_awards_received: number
  link_flair_text: string | null
  gilded: number
  over_18: boolean
}

interface RedditResponse {
  data: {
    children: Array<{
      data: RedditPost
    }>
  }
}

export class RedditAggregator {
  private supabase = createServiceRoleClient()
  
  // Top movie/TV subreddits with their focus areas
  private readonly MOVIE_TV_SUBREDDITS = [
    { name: 'movies', focus: 'general', members: '31M' },
    { name: 'television', focus: 'tv_shows', members: '17M' },
    { name: 'MovieDetails', focus: 'analysis', members: '1.8M' },
    { name: 'FanTheories', focus: 'theory', members: '1.5M' },
    { name: 'netflix', focus: 'streaming', members: '5.2M' },
    { name: 'horror', focus: 'genre', members: '2.1M' },
    { name: 'marvelstudios', focus: 'franchise', members: '2.8M' },
    { name: 'DC_Cinematic', focus: 'franchise', members: '400K' },
    { name: 'criterion', focus: 'arthouse', members: '200K' },
    { name: 'TrueFilm', focus: 'discussion', members: '500K' }
  ]

  /**
   * Aggregate content from all movie/TV subreddits
   */
  async aggregateAllSubreddits(): Promise<{
    success: boolean
    contentAdded: number
    errors: string[]
    subredditResults: Record<string, any>
  }> {
    console.log('🔥 Starting Reddit aggregation across all movie/TV subreddits...')
    
    let totalContentAdded = 0
    const errors: string[] = []
    const subredditResults: Record<string, any> = {}

    for (const subreddit of this.MOVIE_TV_SUBREDDITS) {
      try {
        console.log(`📱 Processing r/${subreddit.name} (${subreddit.members} members)...`)
        
        const result = await this.aggregateSubreddit(subreddit.name, subreddit.focus)
        subredditResults[subreddit.name] = result
        totalContentAdded += result.contentAdded
        
        if (result.errors.length > 0) {
          errors.push(...result.errors)
        }

        // Rate limiting: Reddit allows 60 requests per minute
        await this.delay(1000) // 1 second between subreddits
        
      } catch (error) {
        const errorMsg = `Failed to process r/${subreddit.name}: ${error}`
        console.error(errorMsg)
        errors.push(errorMsg)
      }
    }

    return {
      success: errors.length === 0,
      contentAdded: totalContentAdded,
      errors,
      subredditResults
    }
  }

  /**
   * Aggregate content from a specific subreddit
   */
  async aggregateSubreddit(subredditName: string, focus: string = 'general'): Promise<{
    contentAdded: number
    errors: string[]
    posts: any[]
  }> {
    const errors: string[] = []
    const processedPosts: any[] = []
    let contentAdded = 0

    try {
      // Fetch hot posts from subreddit
      const posts = await this.fetchSubredditPosts(subredditName, 'hot', 25)
      
      console.log(`📊 Found ${posts.length} posts in r/${subredditName}`)

      for (const post of posts) {
        try {
          // Filter for movie/TV content
          if (!this.isMovieTVContent(post)) {
            continue
          }

          // Check if already exists
          const exists = await this.checkPostExists(post.id)
          if (exists) {
            console.log(`⏭️ Post ${post.id} already exists`)
            continue
          }

          // Get or create Reddit creator
          const creatorId = await this.getOrCreateRedditCreator(post.author, subredditName)
          
          if (!creatorId) {
            console.log(`⚠️ Could not create creator for ${post.author}`)
            continue
          }

          // Process and store the post
          const contentData = await this.processRedditPost(post, creatorId, focus)
          
          const { error: insertError } = await this.supabase
            .from('content')
            .insert(contentData)

          if (insertError) {
            console.error(`❌ Error inserting post ${post.id}:`, insertError)
            errors.push(`Failed to insert post ${post.id}: ${insertError.message}`)
          } else {
            console.log(`✅ Added Reddit post: ${contentData.title}`)
            contentAdded++
            processedPosts.push(contentData)
          }

        } catch (postError) {
          console.error(`❌ Error processing post ${post.id}:`, postError)
          errors.push(`Failed to process post ${post.id}: ${postError}`)
        }
      }

    } catch (error) {
      console.error(`❌ Error fetching r/${subredditName}:`, error)
      errors.push(`Failed to fetch r/${subredditName}: ${error}`)
    }

    return { contentAdded, errors, posts: processedPosts }
  }

  /**
   * Fetch posts from a subreddit
   */
  private async fetchSubredditPosts(
    subreddit: string, 
    sort: 'hot' | 'new' | 'top' = 'hot', 
    limit: number = 25
  ): Promise<RedditPost[]> {
    const url = `https://www.reddit.com/r/${subreddit}/${sort}.json?limit=${limit}`
    
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'SceneSniffer/1.0 (Content Aggregator for Movie/TV Recommendations)'
      }
    })

    if (!response.ok) {
      throw new Error(`Reddit API error: ${response.status} ${response.statusText}`)
    }

    const data: RedditResponse = await response.json()
    return data.data.children.map(child => child.data)
  }

  /**
   * Check if post is movie/TV related
   */
  private isMovieTVContent(post: RedditPost): boolean {
    // Skip NSFW content
    if (post.over_18) return false

    // Skip deleted/removed posts
    if (post.selftext === '[deleted]' || post.selftext === '[removed]') return false

    const text = (post.title + ' ' + post.selftext).toLowerCase()
    const movieTVKeywords = [
      'movie', 'film', 'cinema', 'tv', 'series', 'show', 'episode', 'season',
      'netflix', 'disney', 'hbo', 'amazon', 'hulu', 'apple tv', 'paramount',
      'marvel', 'dc', 'star wars', 'horror', 'comedy', 'drama', 'thriller',
      'actor', 'actress', 'director', 'trailer', 'review', 'recommendation',
      'spoiler', 'theory', 'ending', 'plot', 'character', 'cast'
    ]

    // Must contain at least one movie/TV keyword
    const hasKeyword = movieTVKeywords.some(keyword => text.includes(keyword))
    
    // Must have minimum engagement (quality filter)
    const hasEngagement = post.ups >= 5 || post.num_comments >= 2

    return hasKeyword && hasEngagement
  }

  /**
   * Process Reddit post into content format
   */
  private async processRedditPost(post: RedditPost, creatorId: string, focus: string): Promise<any> {
    const contentType = this.classifyRedditContent(post, focus)
    const aiSummary = await this.generateAISummary(post)
    
    return {
      creator_id: creatorId,
      title: post.title,
      description: post.selftext || '',
      content_type: contentType,
      platform: 'reddit',
      platform_url: `https://reddit.com${post.permalink}`,
      platform_id: post.id,
      thumbnail_url: this.getValidThumbnail(post.thumbnail),
      published_at: new Date(post.created_utc * 1000).toISOString(),
      media_type: 'text',
      engagement_metrics: {
        upvotes: post.ups,
        comments: post.num_comments,
        awards: post.total_awards_received,
        gilded: post.gilded
      },
      content_metadata: {
        subreddit: post.subreddit,
        flair: post.link_flair_text,
        author: post.author,
        focus_area: focus
      },
      ai_summary: aiSummary,
      ai_content_quality_score: this.calculateQualityScore(post),
      tags: this.extractTags(post),
      referenced_titles: this.extractReferencedTitles(post.title + ' ' + post.selftext)
    }
  }

  /**
   * Classify Reddit content type
   */
  private classifyRedditContent(post: RedditPost, focus: string): string {
    const title = post.title.toLowerCase()
    const flair = (post.link_flair_text || '').toLowerCase()
    
    // Use flair if available
    if (flair.includes('discussion')) return 'theory'
    if (flair.includes('review')) return 'review'
    if (flair.includes('news')) return 'news'
    if (flair.includes('recommendation')) return 'recommendation'
    
    // Use title keywords
    if (title.includes('review') || title.includes('rating')) return 'review'
    if (title.includes('recommend') || title.includes('suggest')) return 'recommendation'
    if (title.includes('theory') || title.includes('explained')) return 'theory'
    if (title.includes('news') || title.includes('announced')) return 'news'
    if (title.includes('discussion') || title.includes('thoughts')) return 'theory'
    
    // Use subreddit focus
    if (focus === 'theory') return 'theory'
    if (focus === 'analysis') return 'breakdown'
    
    return 'review' // Default
  }

  /**
   * Calculate content quality score
   */
  private calculateQualityScore(post: RedditPost): number {
    let score = 0.5 // Base score
    
    // Engagement factors
    if (post.ups > 100) score += 0.2
    if (post.ups > 1000) score += 0.1
    if (post.num_comments > 50) score += 0.1
    if (post.total_awards_received > 0) score += 0.1
    
    // Content quality factors
    if (post.selftext.length > 500) score += 0.1 // Substantial content
    if (post.link_flair_text) score += 0.05 // Properly flaired
    
    return Math.min(score, 1.0)
  }

  /**
   * Extract tags from Reddit post
   */
  private extractTags(post: RedditPost): string[] {
    const tags: string[] = []
    
    // Add subreddit as tag
    tags.push(post.subreddit)
    
    // Add flair as tag if exists
    if (post.link_flair_text) {
      tags.push(post.link_flair_text.toLowerCase())
    }
    
    // Extract genre tags from title
    const genreKeywords = ['horror', 'comedy', 'drama', 'action', 'thriller', 'sci-fi', 'fantasy', 'romance']
    const text = post.title.toLowerCase()
    
    genreKeywords.forEach(genre => {
      if (text.includes(genre)) {
        tags.push(genre)
      }
    })
    
    return tags.slice(0, 10) // Limit to 10 tags
  }

  /**
   * Extract referenced movie/TV titles
   */
  private extractReferencedTitles(text: string): string[] {
    // This is a simplified version - could be enhanced with NLP
    const titles: string[] = []
    
    // Look for quoted titles
    const quotedMatches = text.match(/"([^"]+)"/g)
    if (quotedMatches) {
      quotedMatches.forEach(match => {
        const title = match.replace(/"/g, '')
        if (title.length > 2 && title.length < 100) {
          titles.push(title)
        }
      })
    }
    
    return titles.slice(0, 5) // Limit to 5 titles
  }

  /**
   * Generate AI summary (simplified version without OpenAI)
   */
  private async generateAISummary(post: RedditPost): Promise<string> {
    // Simple summary without AI for now
    const text = post.selftext || post.title
    if (text.length > 200) {
      return text.substring(0, 197) + '...'
    }
    return text
  }

  /**
   * Get valid thumbnail URL
   */
  private getValidThumbnail(thumbnail: string): string | null {
    if (!thumbnail || thumbnail === 'self' || thumbnail === 'default' || thumbnail === 'nsfw') {
      return null
    }
    return thumbnail
  }

  /**
   * Check if post already exists
   */
  private async checkPostExists(redditId: string): Promise<boolean> {
    const { data } = await this.supabase
      .from('content')
      .select('id')
      .eq('platform', 'reddit')
      .eq('platform_id', redditId)
      .single()
    
    return !!data
  }

  /**
   * Get or create Reddit creator
   */
  private async getOrCreateRedditCreator(username: string, subreddit: string): Promise<string | null> {
    try {
      // Check if creator exists
      const { data: existingCreator } = await this.supabase
        .from('creators')
        .select('id')
        .eq('platform', 'reddit')
        .eq('handle', username)
        .single()

      if (existingCreator) {
        return existingCreator.id
      }

      // Create new creator
      const { data: newCreator, error: createError } = await this.supabase
        .from('creators')
        .insert({
          name: `u/${username}`,
          handle: username,
          platform: 'reddit',
          trust_score: 6.0, // Reddit users generally have good trust
          verified: false,
          genres: [subreddit] // Use subreddit as genre
        })
        .select('id')
        .single()

      if (createError) {
        console.error('Error creating Reddit creator:', createError)
        return null
      }

      return newCreator.id

    } catch (error) {
      console.error('Error in getOrCreateRedditCreator:', error)
      return null
    }
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

export const redditAggregator = new RedditAggregator()

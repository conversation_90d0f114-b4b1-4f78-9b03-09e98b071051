// YouTube RSS feed aggregation (no API key required!)
import { XMLParser } from 'fast-xml-parser'
import { createServiceRoleClient } from '@/lib/supabase-server'

interface YouTubeRSSItem {
  'yt:videoId': string
  title: string
  link: {
    '@_href': string
  }
  author: {
    name: string
    uri: string
  }
  published: string
  updated: string
  'media:group': {
    'media:title': string
    'media:content': {
      '@_url': string
    }
    'media:thumbnail': {
      '@_url': string
    }
    'media:description': string
    'media:community': {
      'media:starRating': {
        '@_count': string
        '@_average': string
      }
      'media:statistics': {
        '@_views': string
      }
    }
  }
}

interface YouTubeRSSFeed {
  feed: {
    title: string
    link: Array<{
      '@_rel': string
      '@_href': string
    }>
    author: {
      name: string
      uri: string
    }
    entry: YouTubeRSSItem[]
  }
}

export class YouTubeRSSAggregator {
  private supabase = createServiceRoleClient()
  private xmlParser = new XMLParser({
    ignoreAttributes: false,
    attributeNamePrefix: '@_'
  })

  // Popular movie/TV YouTube channels
  private readonly MOVIE_TV_CHANNELS = [
    { 
      id: 'UCq6VFHwMzcMXbuKyG7SQYIg', 
      name: 'Emergency Awesome', 
      focus: 'news',
      subscribers: '4.2M'
    },
    { 
      id: 'UCKy1dAqELo0zrOtPkf0eTMw', 
      name: 'IGN Movie Trailers', 
      focus: 'news',
      subscribers: '15M'
    },
    { 
      id: 'UCiDJtJKMICpb9B1qf7qjEOA', 
      name: 'Screen Junkies', 
      focus: 'review',
      subscribers: '6.8M'
    },
    { 
      id: 'UC_bFhliKNnAHlZUcqojzWbQ', 
      name: 'The Critical Drinker', 
      focus: 'review',
      subscribers: '2.1M'
    },
    { 
      id: 'UCErSSa3CaP_GJxmFpdjG9Jw', 
      name: 'Looper', 
      focus: 'breakdown',
      subscribers: '5.4M'
    },
    { 
      id: 'UCq0OueAsdxH6b8nyAspwViw', 
      name: 'New Rockstars', 
      focus: 'theory',
      subscribers: '2.8M'
    }
  ]

  /**
   * Aggregate content from all movie/TV YouTube channels
   */
  async aggregateAllChannels(): Promise<{
    success: boolean
    contentAdded: number
    errors: string[]
    channelResults: Record<string, any>
  }> {
    console.log('📺 Starting YouTube RSS aggregation across all movie/TV channels...')
    
    let totalContentAdded = 0
    const errors: string[] = []
    const channelResults: Record<string, any> = {}

    for (const channel of this.MOVIE_TV_CHANNELS) {
      try {
        console.log(`🎬 Processing ${channel.name} (${channel.subscribers} subscribers)...`)
        
        const result = await this.aggregateChannel(channel.id, channel.name, channel.focus)
        channelResults[channel.name] = result
        totalContentAdded += result.contentAdded
        
        if (result.errors.length > 0) {
          errors.push(...result.errors)
        }

        // Small delay to be respectful
        await this.delay(500)
        
      } catch (error) {
        const errorMsg = `Failed to process ${channel.name}: ${error}`
        console.error(errorMsg)
        errors.push(errorMsg)
      }
    }

    return {
      success: errors.length === 0,
      contentAdded: totalContentAdded,
      errors,
      channelResults
    }
  }

  /**
   * Aggregate content from a specific YouTube channel via RSS
   */
  async aggregateChannel(channelId: string, channelName: string, focus: string): Promise<{
    contentAdded: number
    errors: string[]
    videos: any[]
  }> {
    const errors: string[] = []
    const processedVideos: any[] = []
    let contentAdded = 0

    try {
      // Fetch RSS feed for the channel
      const videos = await this.fetchChannelRSS(channelId)
      
      console.log(`📊 Found ${videos.length} videos from ${channelName}`)

      // Get or create creator
      const creatorId = await this.getOrCreateYouTubeCreator(channelId, channelName, focus)
      
      if (!creatorId) {
        errors.push(`Could not create creator for ${channelName}`)
        return { contentAdded: 0, errors, videos: [] }
      }

      for (const video of videos) {
        try {
          // Check if video already exists
          const exists = await this.checkVideoExists(video['yt:videoId'])
          if (exists) {
            console.log(`⏭️ Video ${video['yt:videoId']} already exists`)
            continue
          }

          // Process and store the video
          const contentData = await this.processYouTubeVideo(video, creatorId, focus)
          
          const { error: insertError } = await this.supabase
            .from('content')
            .insert(contentData)

          if (insertError) {
            console.error(`❌ Error inserting video ${video['yt:videoId']}:`, insertError)
            errors.push(`Failed to insert video ${video['yt:videoId']}: ${insertError.message}`)
          } else {
            console.log(`✅ Added YouTube video: ${contentData.title}`)
            contentAdded++
            processedVideos.push(contentData)
          }

        } catch (videoError) {
          console.error(`❌ Error processing video ${video['yt:videoId']}:`, videoError)
          errors.push(`Failed to process video ${video['yt:videoId']}: ${videoError}`)
        }
      }

    } catch (error) {
      console.error(`❌ Error fetching RSS for ${channelName}:`, error)
      errors.push(`Failed to fetch RSS for ${channelName}: ${error}`)
    }

    return { contentAdded, errors, videos: processedVideos }
  }

  /**
   * Fetch YouTube channel RSS feed
   */
  private async fetchChannelRSS(channelId: string): Promise<YouTubeRSSItem[]> {
    const rssUrl = `https://www.youtube.com/feeds/videos.xml?channel_id=${channelId}`
    
    const response = await fetch(rssUrl)
    
    if (!response.ok) {
      throw new Error(`YouTube RSS error: ${response.status} ${response.statusText}`)
    }

    const xmlText = await response.text()
    const parsedData: YouTubeRSSFeed = this.xmlParser.parse(xmlText)
    
    if (!parsedData.feed || !parsedData.feed.entry) {
      throw new Error('Invalid RSS feed structure')
    }

    // Handle single entry vs array
    const entries = Array.isArray(parsedData.feed.entry) 
      ? parsedData.feed.entry 
      : [parsedData.feed.entry]

    return entries
  }

  /**
   * Process YouTube video into content format
   */
  private async processYouTubeVideo(video: YouTubeRSSItem, creatorId: string, focus: string): Promise<any> {
    const videoId = video['yt:videoId']
    const title = video.title || video['media:group']?.['media:title'] || 'Untitled'
    const description = video['media:group']?.['media:description'] || ''
    const thumbnail = video['media:group']?.['media:thumbnail']?.['@_url'] || ''
    const publishedAt = video.published
    
    const contentType = this.classifyYouTubeContent(title, description, focus)
    const duration = await this.extractDuration(videoId) // Could be enhanced
    
    return {
      creator_id: creatorId,
      title: title,
      description: description,
      content_type: contentType,
      platform: 'youtube',
      platform_url: `https://www.youtube.com/watch?v=${videoId}`,
      platform_id: videoId,
      thumbnail_url: thumbnail,
      published_at: publishedAt,
      media_type: 'video',
      duration: duration,
      engagement_metrics: {
        views: this.extractViews(video),
        // Note: RSS doesn't include likes/comments, would need API for that
      },
      content_metadata: {
        channel_id: creatorId,
        video_url: `https://www.youtube.com/watch?v=${videoId}`,
        embed_url: `https://www.youtube.com/embed/${videoId}`,
        focus_area: focus
      },
      ai_content_quality_score: this.calculateVideoQualityScore(title, description),
      tags: this.extractVideoTags(title, description),
      referenced_titles: this.extractReferencedTitles(title + ' ' + description)
    }
  }

  /**
   * Classify YouTube content type
   */
  private classifyYouTubeContent(title: string, description: string, focus: string): string {
    const text = (title + ' ' + description).toLowerCase()
    
    // Use channel focus as primary indicator
    if (focus === 'review' && (text.includes('review') || text.includes('reaction'))) return 'review'
    if (focus === 'theory' && (text.includes('theory') || text.includes('explained'))) return 'theory'
    if (focus === 'breakdown' && (text.includes('breakdown') || text.includes('analysis'))) return 'breakdown'
    if (focus === 'news' && (text.includes('news') || text.includes('trailer'))) return 'news'
    
    // Fallback to title analysis
    if (text.includes('review') || text.includes('reaction')) return 'review'
    if (text.includes('theory') || text.includes('explained') || text.includes('ending')) return 'theory'
    if (text.includes('breakdown') || text.includes('analysis')) return 'breakdown'
    if (text.includes('trailer') || text.includes('news') || text.includes('announced')) return 'news'
    if (text.includes('recommend') || text.includes('best') || text.includes('top')) return 'recommendation'
    
    return focus === 'review' ? 'review' : 'news' // Default based on channel focus
  }

  /**
   * Extract video duration (simplified - could be enhanced with API call)
   */
  private async extractDuration(videoId: string): Promise<number | null> {
    // For now, return null - could be enhanced with YouTube API call
    // or by parsing the video page
    return null
  }

  /**
   * Extract view count from RSS (if available)
   */
  private extractViews(video: YouTubeRSSItem): number {
    try {
      const views = video['media:group']?.['media:community']?.['media:statistics']?.['@_views']
      return views ? parseInt(views) : 0
    } catch {
      return 0
    }
  }

  /**
   * Calculate video quality score
   */
  private calculateVideoQualityScore(title: string, description: string): number {
    let score = 0.6 // Base score for YouTube content
    
    // Title quality indicators
    if (title.length > 20 && title.length < 100) score += 0.1 // Good title length
    if (description.length > 100) score += 0.1 // Has description
    if (description.length > 500) score += 0.1 // Detailed description
    
    // Content type indicators
    if (title.toLowerCase().includes('review')) score += 0.1
    if (title.toLowerCase().includes('analysis') || title.toLowerCase().includes('breakdown')) score += 0.1
    
    return Math.min(score, 1.0)
  }

  /**
   * Extract tags from video
   */
  private extractVideoTags(title: string, description: string): string[] {
    const tags: string[] = []
    const text = (title + ' ' + description).toLowerCase()
    
    // Genre tags
    const genres = ['horror', 'comedy', 'drama', 'action', 'thriller', 'sci-fi', 'fantasy', 'romance', 'documentary']
    genres.forEach(genre => {
      if (text.includes(genre)) tags.push(genre)
    })
    
    // Platform tags
    const platforms = ['netflix', 'disney', 'hbo', 'amazon', 'apple', 'paramount', 'hulu']
    platforms.forEach(platform => {
      if (text.includes(platform)) tags.push(platform)
    })
    
    // Content type tags
    if (text.includes('trailer')) tags.push('trailer')
    if (text.includes('review')) tags.push('review')
    if (text.includes('theory')) tags.push('theory')
    if (text.includes('reaction')) tags.push('reaction')
    
    return tags.slice(0, 10)
  }

  /**
   * Extract referenced movie/TV titles
   */
  private extractReferencedTitles(text: string): string[] {
    const titles: string[] = []
    
    // Look for quoted titles
    const quotedMatches = text.match(/"([^"]+)"/g)
    if (quotedMatches) {
      quotedMatches.forEach(match => {
        const title = match.replace(/"/g, '')
        if (title.length > 2 && title.length < 100) {
          titles.push(title)
        }
      })
    }
    
    // Look for common movie/show patterns
    const patterns = [
      /(\w+(?:\s+\w+)*)\s+(?:movie|film|series|show)/gi,
      /(?:movie|film|series|show)\s+(\w+(?:\s+\w+)*)/gi
    ]
    
    patterns.forEach(pattern => {
      const matches = text.match(pattern)
      if (matches) {
        matches.forEach(match => {
          const cleaned = match.replace(/(movie|film|series|show)/gi, '').trim()
          if (cleaned.length > 2 && cleaned.length < 50) {
            titles.push(cleaned)
          }
        })
      }
    })
    
    return [...new Set(titles)].slice(0, 5) // Remove duplicates and limit
  }

  /**
   * Check if video already exists
   */
  private async checkVideoExists(videoId: string): Promise<boolean> {
    const { data } = await this.supabase
      .from('content')
      .select('id')
      .eq('platform', 'youtube')
      .eq('platform_id', videoId)
      .single()
    
    return !!data
  }

  /**
   * Get or create YouTube creator
   */
  private async getOrCreateYouTubeCreator(channelId: string, channelName: string, focus: string): Promise<string | null> {
    try {
      // Check if creator exists
      const { data: existingCreator } = await this.supabase
        .from('creators')
        .select('id')
        .eq('platform', 'youtube')
        .eq('handle', channelId)
        .single()

      if (existingCreator) {
        return existingCreator.id
      }

      // Create new creator
      const { data: newCreator, error: createError } = await this.supabase
        .from('creators')
        .insert({
          name: channelName,
          handle: channelId,
          platform: 'youtube',
          trust_score: 8.0, // YouTube creators generally have high trust
          verified: true, // Assume verified for popular channels
          genres: [focus] // Use focus as genre
        })
        .select('id')
        .single()

      if (createError) {
        console.error('Error creating YouTube creator:', createError)
        return null
      }

      return newCreator.id

    } catch (error) {
      console.error('Error in getOrCreateYouTubeCreator:', error)
      return null
    }
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

export const youtubeRSSAggregator = new YouTubeRSSAggregator()

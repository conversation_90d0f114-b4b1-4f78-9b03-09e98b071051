// Twitter API v2 integration
export class Twitter<PERSON>I {
  private bearerToken: string
  private apiKey: string
  private apiSecret: string

  constructor() {
    this.bearerToken = process.env.TWITTER_BEARER_TOKEN!
    this.apiKey = process.env.TWITTER_API_KEY!
    this.apiSecret = process.env.TWITTER_API_SECRET!
  }

  /**
   * Get user's recent tweets
   */
  async getUserTweets(userId: string, maxResults: number = 100): Promise<any[]> {
    const params = new URLSearchParams({
      'tweet.fields': 'created_at,public_metrics,referenced_tweets,attachments,context_annotations',
      'user.fields': 'username,name,verified',
      'expansions': 'author_id,referenced_tweets.id',
      'max_results': Math.min(maxResults, 100).toString()
    })

    const response = await fetch(
      `https://api.twitter.com/2/users/${userId}/tweets?${params.toString()}`,
      {
        headers: {
          'Authorization': `Bearer ${this.bearerToken}`,
          'Content-Type': 'application/json'
        }
      }
    )

    if (!response.ok) {
      throw new Error(`Twitter API error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    return data.data || []
  }

  /**
   * Search for movie/TV related tweets
   */
  async searchMovieTVTweets(query: string, maxResults: number = 100): Promise<any[]> {
    const movieTVQuery = `(${query}) (movie OR film OR TV OR series OR Netflix OR review OR recommendation) -is:retweet lang:en`
    
    const params = new URLSearchParams({
      'query': movieTVQuery,
      'tweet.fields': 'created_at,public_metrics,referenced_tweets,attachments,context_annotations',
      'user.fields': 'username,name,verified',
      'expansions': 'author_id',
      'max_results': Math.min(maxResults, 100).toString()
    })

    const response = await fetch(
      `https://api.twitter.com/2/tweets/search/recent?${params.toString()}`,
      {
        headers: {
          'Authorization': `Bearer ${this.bearerToken}`,
          'Content-Type': 'application/json'
        }
      }
    )

    if (!response.ok) {
      throw new Error(`Twitter search error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    return data.data || []
  }

  /**
   * Get trending topics related to movies/TV
   */
  async getTrendingMovieTV(): Promise<any[]> {
    // Get trending topics for US
    const response = await fetch(
      'https://api.twitter.com/1.1/trends/place.json?id=23424977', // US WOEID
      {
        headers: {
          'Authorization': `Bearer ${this.bearerToken}`
        }
      }
    )

    if (!response.ok) {
      throw new Error(`Twitter trends error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    const trends = data[0]?.trends || []

    // Filter for movie/TV related trends
    const movieTVKeywords = [
      'movie', 'film', 'cinema', 'netflix', 'disney', 'hbo', 'marvel', 'dc',
      'tv', 'series', 'show', 'episode', 'season', 'finale', 'premiere'
    ]

    return trends.filter((trend: any) => {
      const name = trend.name.toLowerCase()
      return movieTVKeywords.some(keyword => name.includes(keyword))
    })
  }

  /**
   * Get user information by username
   */
  async getUserByUsername(username: string): Promise<any> {
    const params = new URLSearchParams({
      'user.fields': 'id,username,name,verified,public_metrics,description'
    })

    const response = await fetch(
      `https://api.twitter.com/2/users/by/username/${username}?${params.toString()}`,
      {
        headers: {
          'Authorization': `Bearer ${this.bearerToken}`
        }
      }
    )

    if (!response.ok) {
      throw new Error(`Twitter user lookup error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    return data.data
  }

  /**
   * Get conversation thread for a tweet
   */
  async getConversationThread(conversationId: string): Promise<any[]> {
    const params = new URLSearchParams({
      'query': `conversation_id:${conversationId}`,
      'tweet.fields': 'created_at,public_metrics,referenced_tweets,in_reply_to_user_id',
      'user.fields': 'username,name,verified',
      'expansions': 'author_id',
      'max_results': '100'
    })

    const response = await fetch(
      `https://api.twitter.com/2/tweets/search/recent?${params.toString()}`,
      {
        headers: {
          'Authorization': `Bearer ${this.bearerToken}`
        }
      }
    )

    if (!response.ok) {
      throw new Error(`Twitter thread error: ${response.status} ${response.statusText}`)
    }

    const data = await response.json()
    return data.data || []
  }

  /**
   * Detect and group Twitter threads
   */
  async detectThreads(tweets: any[]): Promise<any[]> {
    const threads: any[] = []
    const processedTweets = new Set()

    for (const tweet of tweets) {
      if (processedTweets.has(tweet.id)) continue

      // Check if this tweet is part of a thread
      const threadTweets = await this.getThreadTweets(tweet, tweets)
      
      if (threadTweets.length > 1) {
        // This is a thread
        threads.push({
          id: tweet.id,
          type: 'thread',
          tweets: threadTweets,
          text: threadTweets.map(t => t.text).join('\n\n'),
          created_at: tweet.created_at,
          author_id: tweet.author_id,
          public_metrics: this.aggregateThreadMetrics(threadTweets)
        })

        // Mark all tweets in thread as processed
        threadTweets.forEach(t => processedTweets.add(t.id))
      } else {
        // Single tweet
        threads.push({
          ...tweet,
          type: 'single',
          tweets: [tweet]
        })
        processedTweets.add(tweet.id)
      }
    }

    return threads
  }

  /**
   * Get all tweets in a thread
   */
  private async getThreadTweets(startTweet: any, allTweets: any[]): Promise<any[]> {
    const threadTweets = [startTweet]
    
    // Look for replies from the same author
    const authorId = startTweet.author_id
    let currentTweet = startTweet

    while (true) {
      const nextTweet = allTweets.find(tweet => 
        tweet.author_id === authorId &&
        tweet.referenced_tweets?.some((ref: any) => 
          ref.type === 'replied_to' && ref.id === currentTweet.id
        )
      )

      if (!nextTweet) break

      threadTweets.push(nextTweet)
      currentTweet = nextTweet
    }

    return threadTweets
  }

  /**
   * Aggregate metrics for thread
   */
  private aggregateThreadMetrics(tweets: any[]): any {
    return tweets.reduce((acc, tweet) => ({
      retweet_count: acc.retweet_count + (tweet.public_metrics?.retweet_count || 0),
      like_count: acc.like_count + (tweet.public_metrics?.like_count || 0),
      reply_count: acc.reply_count + (tweet.public_metrics?.reply_count || 0),
      quote_count: acc.quote_count + (tweet.public_metrics?.quote_count || 0)
    }), { retweet_count: 0, like_count: 0, reply_count: 0, quote_count: 0 })
  }

  /**
   * Filter tweets for movie/TV content
   */
  filterMovieTVContent(tweets: any[]): any[] {
    const movieTVKeywords = [
      'movie', 'film', 'cinema', 'review', 'recommendation',
      'tv', 'series', 'show', 'episode', 'season',
      'netflix', 'disney', 'hbo', 'amazon', 'hulu',
      'marvel', 'dc', 'horror', 'comedy', 'drama',
      'actor', 'actress', 'director', 'trailer',
      'spoiler', 'theory', 'breakdown', 'analysis'
    ]

    return tweets.filter(tweet => {
      const text = tweet.text.toLowerCase()
      return movieTVKeywords.some(keyword => text.includes(keyword))
    })
  }

  /**
   * Check if tweet has movie/TV context annotations
   */
  hasMovieTVContext(tweet: any): boolean {
    const contexts = tweet.context_annotations || []
    const movieTVDomains = [
      'Movie', 'TV Show', 'Entertainment', 'Celebrity', 'Actor', 'Director'
    ]

    return contexts.some((context: any) => 
      movieTVDomains.some(domain => 
        context.domain?.name?.includes(domain) || 
        context.entity?.name?.includes(domain)
      )
    )
  }
}

export const twitterAPI = new TwitterAPI()

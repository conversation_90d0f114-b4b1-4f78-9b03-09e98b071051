import { createServiceRoleClient } from '@/lib/supabase-server'

export interface UserBehaviorProfile {
  user_id: string
  preferred_content_types: Record<string, number> // content_type -> preference score (0-1)
  preferred_genres: Record<string, number>        // genre -> preference score (0-1)
  preferred_creators: Record<string, number>      // creator_id -> preference score (0-1)
  engagement_patterns: {
    peak_hours: number[]                          // Hours of day when most active
    preferred_content_length: 'short' | 'medium' | 'long'
    interaction_frequency: number                 // Interactions per day
  }
  content_discovery: {
    openness_to_new_creators: number             // 0-1 score
    genre_exploration_rate: number               // 0-1 score
    trending_content_affinity: number            // 0-1 score
  }
  last_updated: Date
}

export class PersonalizationEngine {
  private supabase = createServiceRoleClient()

  /**
   * Build or update user behavior profile based on interactions
   */
  async buildUserProfile(userId: string): Promise<UserBehaviorProfile> {
    try {
      // Get user's content interactions from last 90 days
      const { data: interactions } = await this.supabase
        .from('content_interactions')
        .select(`
          interaction_type,
          interaction_value,
          created_at,
          content (
            content_type,
            tags,
            creator_id,
            creators (
              name
            )
          )
        `)
        .eq('user_id', userId)
        .gte('created_at', new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString())

      if (!interactions || interactions.length === 0) {
        return this.getDefaultProfile(userId)
      }

      // Analyze content type preferences
      const contentTypeScores = this.analyzeContentTypePreferences(interactions)
      
      // Analyze genre preferences
      const genreScores = this.analyzeGenrePreferences(interactions)
      
      // Analyze creator preferences
      const creatorScores = this.analyzeCreatorPreferences(interactions)
      
      // Analyze engagement patterns
      const engagementPatterns = this.analyzeEngagementPatterns(interactions)
      
      // Analyze content discovery patterns
      const discoveryPatterns = this.analyzeDiscoveryPatterns(interactions)

      const profile: UserBehaviorProfile = {
        user_id: userId,
        preferred_content_types: contentTypeScores,
        preferred_genres: genreScores,
        preferred_creators: creatorScores,
        engagement_patterns: engagementPatterns,
        content_discovery: discoveryPatterns,
        last_updated: new Date()
      }

      // Cache the profile for future use
      await this.cacheUserProfile(userId, profile)

      return profile

    } catch (error) {
      console.error('Error building user profile:', error)
      return this.getDefaultProfile(userId)
    }
  }

  /**
   * Get personalized content recommendations based on user profile
   */
  async getPersonalizedRecommendations(
    userId: string, 
    availableContent: any[], 
    limit: number = 20
  ): Promise<any[]> {
    const profile = await this.buildUserProfile(userId)
    
    // Score each content item based on user profile
    const scoredContent = availableContent.map(content => ({
      ...content,
      personalizationScore: this.calculatePersonalizationScore(content, profile)
    }))

    // Apply diversity injection to prevent echo chambers
    const diversifiedContent = this.injectDiversity(scoredContent, profile, limit)

    return diversifiedContent
      .sort((a, b) => b.personalizationScore - a.personalizationScore)
      .slice(0, limit)
  }

  /**
   * Calculate personalization score for a content item
   */
  private calculatePersonalizationScore(content: any, profile: UserBehaviorProfile): number {
    let score = 0

    // Content type preference
    const contentTypeScore = profile.preferred_content_types[content.content_type] || 0.5
    score += contentTypeScore * 30

    // Genre preference
    if (content.tags && content.tags.length > 0) {
      const genreScores = content.tags.map((tag: string) => 
        profile.preferred_genres[tag.toLowerCase()] || 0.3
      )
      const avgGenreScore = genreScores.reduce((a: number, b: number) => a + b, 0) / genreScores.length
      score += avgGenreScore * 25
    }

    // Creator preference
    const creatorScore = profile.preferred_creators[content.creator_id] || 0.4
    score += creatorScore * 20

    // Trending content affinity
    if (content.engagement_score > 10) { // High engagement content
      score += profile.content_discovery.trending_content_affinity * 15
    }

    // Freshness preference based on user patterns
    const daysSincePublished = (Date.now() - new Date(content.published_at).getTime()) / (1000 * 60 * 60 * 24)
    if (daysSincePublished <= 1) {
      score += 10 // Fresh content bonus
    }

    return Math.min(score, 100) // Cap at 100
  }

  /**
   * Inject diversity to prevent echo chambers
   */
  private injectDiversity(content: any[], profile: UserBehaviorProfile, limit: number): any[] {
    const diversityRate = profile.content_discovery.openness_to_new_creators
    const diversitySlots = Math.floor(limit * diversityRate * 0.3) // Up to 30% diversity

    if (diversitySlots === 0) return content

    // Separate familiar and unfamiliar content
    const familiarContent = content.filter(item => 
      profile.preferred_creators[item.creator_id] > 0.6
    )
    const unfamiliarContent = content.filter(item => 
      !profile.preferred_creators[item.creator_id] || profile.preferred_creators[item.creator_id] <= 0.6
    )

    // Take top familiar content
    const selectedFamiliar = familiarContent.slice(0, limit - diversitySlots)
    
    // Take top unfamiliar content for diversity
    const selectedUnfamiliar = unfamiliarContent.slice(0, diversitySlots)

    return [...selectedFamiliar, ...selectedUnfamiliar]
  }

  /**
   * Analyze content type preferences from interactions
   */
  private analyzeContentTypePreferences(interactions: any[]): Record<string, number> {
    const typeInteractions: Record<string, number> = {}
    const typeWeights = {
      'view': 1,
      'click': 2,
      'share': 5,
      'bookmark': 4,
      'like': 3,
      'time_spent': 0.1 // Per second
    }

    interactions.forEach(interaction => {
      const contentType = interaction.content?.content_type || 'review'
      const weight = typeWeights[interaction.interaction_type as keyof typeof typeWeights] || 1
      const value = interaction.interaction_type === 'time_spent' 
        ? (interaction.interaction_value || 0) * weight
        : weight

      typeInteractions[contentType] = (typeInteractions[contentType] || 0) + value
    })

    // Normalize scores to 0-1 range
    const maxScore = Math.max(...Object.values(typeInteractions))
    const normalizedScores: Record<string, number> = {}
    
    Object.entries(typeInteractions).forEach(([type, score]) => {
      normalizedScores[type] = maxScore > 0 ? score / maxScore : 0.5
    })

    return normalizedScores
  }

  /**
   * Analyze genre preferences from interactions
   */
  private analyzeGenrePreferences(interactions: any[]): Record<string, number> {
    const genreInteractions: Record<string, number> = {}

    interactions.forEach(interaction => {
      const tags = interaction.content?.tags || []
      const weight = interaction.interaction_type === 'share' ? 3 : 
                    interaction.interaction_type === 'bookmark' ? 2 : 1

      tags.forEach((tag: string) => {
        const genre = tag.toLowerCase()
        genreInteractions[genre] = (genreInteractions[genre] || 0) + weight
      })
    })

    // Normalize scores
    const maxScore = Math.max(...Object.values(genreInteractions))
    const normalizedScores: Record<string, number> = {}
    
    Object.entries(genreInteractions).forEach(([genre, score]) => {
      normalizedScores[genre] = maxScore > 0 ? score / maxScore : 0.5
    })

    return normalizedScores
  }

  /**
   * Analyze creator preferences from interactions
   */
  private analyzeCreatorPreferences(interactions: any[]): Record<string, number> {
    const creatorInteractions: Record<string, number> = {}

    interactions.forEach(interaction => {
      const creatorId = interaction.content?.creator_id
      if (creatorId) {
        const weight = interaction.interaction_type === 'share' ? 4 : 
                      interaction.interaction_type === 'bookmark' ? 3 : 
                      interaction.interaction_type === 'like' ? 2 : 1

        creatorInteractions[creatorId] = (creatorInteractions[creatorId] || 0) + weight
      }
    })

    // Normalize scores
    const maxScore = Math.max(...Object.values(creatorInteractions))
    const normalizedScores: Record<string, number> = {}
    
    Object.entries(creatorInteractions).forEach(([creatorId, score]) => {
      normalizedScores[creatorId] = maxScore > 0 ? score / maxScore : 0.5
    })

    return normalizedScores
  }

  /**
   * Analyze engagement patterns
   */
  private analyzeEngagementPatterns(interactions: any[]): UserBehaviorProfile['engagement_patterns'] {
    const hourCounts: Record<number, number> = {}
    let totalInteractions = interactions.length

    interactions.forEach(interaction => {
      const hour = new Date(interaction.created_at).getHours()
      hourCounts[hour] = (hourCounts[hour] || 0) + 1
    })

    // Find peak hours (top 3)
    const peakHours = Object.entries(hourCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([hour]) => parseInt(hour))

    return {
      peak_hours: peakHours,
      preferred_content_length: 'medium', // Could be enhanced with actual content length analysis
      interaction_frequency: totalInteractions / 90 // Per day over 90 days
    }
  }

  /**
   * Analyze content discovery patterns
   */
  private analyzeDiscoveryPatterns(interactions: any[]): UserBehaviorProfile['content_discovery'] {
    const uniqueCreators = new Set(interactions.map(i => i.content?.creator_id)).size
    const totalCreators = interactions.length > 0 ? interactions.length : 1

    return {
      openness_to_new_creators: Math.min(uniqueCreators / totalCreators, 1),
      genre_exploration_rate: 0.7, // Could be enhanced with genre diversity analysis
      trending_content_affinity: 0.6 // Could be enhanced with trending content interaction analysis
    }
  }

  /**
   * Get default profile for new users
   */
  private getDefaultProfile(userId: string): UserBehaviorProfile {
    return {
      user_id: userId,
      preferred_content_types: {
        'review': 0.7,
        'recommendation': 0.8,
        'theory': 0.5,
        'news': 0.6,
        'breakdown': 0.6,
        'spoiler-free': 0.7
      },
      preferred_genres: {},
      preferred_creators: {},
      engagement_patterns: {
        peak_hours: [19, 20, 21], // Evening hours
        preferred_content_length: 'medium',
        interaction_frequency: 5
      },
      content_discovery: {
        openness_to_new_creators: 0.7,
        genre_exploration_rate: 0.6,
        trending_content_affinity: 0.5
      },
      last_updated: new Date()
    }
  }

  /**
   * Cache user profile for performance
   */
  private async cacheUserProfile(userId: string, profile: UserBehaviorProfile): Promise<void> {
    try {
      // Store in user_preferences table as JSONB
      await this.supabase
        .from('user_preferences')
        .upsert({
          user_id: userId,
          behavior_profile: profile
        })
    } catch (error) {
      console.warn('Failed to cache user profile:', error)
    }
  }
}

export const personalizationEngine = new PersonalizationEngine()

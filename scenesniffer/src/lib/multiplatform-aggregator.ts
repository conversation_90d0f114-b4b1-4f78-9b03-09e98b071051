import { createServiceRoleClient } from '@/lib/supabase-server'

interface ContentAggregationResult {
  success: boolean
  contentAdded: number
  errors: string[]
  platform: string
}

interface InstagramPost {
  id: string
  caption: string
  media_type: 'IMAGE' | 'VIDEO' | 'CAROUSEL_ALBUM'
  media_url: string
  permalink: string
  timestamp: string
  like_count?: number
  comments_count?: number
  children?: { data: Array<{ media_url: string; media_type: string }> }
}

interface TwitterTweet {
  id: string
  text: string
  created_at: string
  author_id: string
  public_metrics: {
    retweet_count: number
    like_count: number
    reply_count: number
    quote_count: number
  }
  attachments?: {
    media_keys?: string[]
  }
  referenced_tweets?: Array<{
    type: 'replied_to' | 'quoted' | 'retweeted'
    id: string
  }>
}

export class MultiPlatformAggregator {
  private supabase = createServiceRoleClient()

  /**
   * Aggregate Instagram content for a creator
   */
  async aggregateInstagramContent(creatorId: string, instagramUserId: string): Promise<ContentAggregationResult> {
    const errors: string[] = []
    let contentAdded = 0

    try {
      console.log(`📸 Aggregating Instagram content for creator ${creatorId}`)

      // Fetch Instagram posts using Instagram Basic Display API
      const posts = await this.fetchInstagramPosts(instagramUserId)
      
      for (const post of posts) {
        try {
          // Check if content already exists
          const exists = await this.checkContentExists(creatorId, post.id, 'instagram')
          if (exists) {
            console.log(`⏭️ Instagram post ${post.id} already exists`)
            continue
          }

          // Process Instagram post with AI
          const processedContent = await this.processInstagramPost(post, creatorId)
          
          // Store in database
          const { error: insertError } = await this.supabase
            .from('content')
            .insert(processedContent)

          if (insertError) {
            console.error(`❌ Error inserting Instagram post ${post.id}:`, insertError)
            errors.push(`Failed to insert Instagram post ${post.id}: ${insertError.message}`)
          } else {
            console.log(`✅ Added Instagram post: ${processedContent.title}`)
            contentAdded++
          }

        } catch (postError) {
          console.error(`❌ Error processing Instagram post ${post.id}:`, postError)
          errors.push(`Failed to process Instagram post ${post.id}: ${postError}`)
        }
      }

    } catch (error) {
      console.error('❌ Instagram aggregation failed:', error)
      errors.push(`Instagram aggregation failed: ${error}`)
    }

    return {
      success: errors.length === 0,
      contentAdded,
      errors,
      platform: 'instagram'
    }
  }

  /**
   * Aggregate Twitter content for a creator
   */
  async aggregateTwitterContent(creatorId: string, twitterUserId: string): Promise<ContentAggregationResult> {
    const errors: string[] = []
    let contentAdded = 0

    try {
      console.log(`🐦 Aggregating Twitter content for creator ${creatorId}`)

      // Fetch recent tweets
      const tweets = await this.fetchTwitterTweets(twitterUserId)
      
      // Group tweets into threads
      const processedTweets = await this.groupTwitterThreads(tweets)
      
      for (const tweetData of processedTweets) {
        try {
          // Check if content already exists
          const exists = await this.checkContentExists(creatorId, tweetData.id, 'twitter')
          if (exists) {
            console.log(`⏭️ Twitter content ${tweetData.id} already exists`)
            continue
          }

          // Process Twitter content with AI
          const processedContent = await this.processTwitterContent(tweetData, creatorId)
          
          // Store in database
          const { error: insertError } = await this.supabase
            .from('content')
            .insert(processedContent)

          if (insertError) {
            console.error(`❌ Error inserting Twitter content ${tweetData.id}:`, insertError)
            errors.push(`Failed to insert Twitter content ${tweetData.id}: ${insertError.message}`)
          } else {
            console.log(`✅ Added Twitter content: ${processedContent.title}`)
            contentAdded++
          }

        } catch (tweetError) {
          console.error(`❌ Error processing Twitter content ${tweetData.id}:`, tweetError)
          errors.push(`Failed to process Twitter content ${tweetData.id}: ${tweetError}`)
        }
      }

    } catch (error) {
      console.error('❌ Twitter aggregation failed:', error)
      errors.push(`Twitter aggregation failed: ${error}`)
    }

    return {
      success: errors.length === 0,
      contentAdded,
      errors,
      platform: 'twitter'
    }
  }

  /**
   * Process Instagram post with AI enhancement
   */
  private async processInstagramPost(post: InstagramPost, creatorId: string): Promise<any> {
    // Extract media URLs for carousels
    const mediaUrls = post.media_type === 'CAROUSEL_ALBUM' && post.children
      ? post.children.data.map(child => child.media_url)
      : [post.media_url]

    // AI-powered content analysis
    const aiAnalysis = await this.analyzeInstagramContent(post)

    return {
      creator_id: creatorId,
      title: aiAnalysis.title,
      description: post.caption || '',
      content_type: aiAnalysis.contentType,
      platform: 'instagram',
      platform_url: post.permalink,
      platform_id: post.id,
      thumbnail_url: post.media_url,
      published_at: post.timestamp,
      ai_summary: aiAnalysis.summary,
      referenced_titles: aiAnalysis.referencedTitles,
      tags: aiAnalysis.tags,
      media_type: post.media_type.toLowerCase(),
      media_urls: mediaUrls,
      engagement_metrics: {
        likes: post.like_count || 0,
        comments: post.comments_count || 0
      },
      content_metadata: {
        hashtags: this.extractHashtags(post.caption || ''),
        mentions: this.extractMentions(post.caption || ''),
        media_count: mediaUrls.length
      },
      ai_extracted_text: aiAnalysis.extractedText,
      ai_content_quality_score: aiAnalysis.qualityScore,
      ai_topics: aiAnalysis.topics
    }
  }

  /**
   * Process Twitter content with AI enhancement
   */
  private async processTwitterContent(tweetData: any, creatorId: string): Promise<any> {
    // AI-powered content analysis
    const aiAnalysis = await this.analyzeTwitterContent(tweetData)

    return {
      creator_id: creatorId,
      title: aiAnalysis.title,
      description: tweetData.text,
      content_type: aiAnalysis.contentType,
      platform: 'twitter',
      platform_url: `https://twitter.com/user/status/${tweetData.id}`,
      platform_id: tweetData.id,
      published_at: tweetData.created_at,
      ai_summary: aiAnalysis.summary,
      referenced_titles: aiAnalysis.referencedTitles,
      tags: aiAnalysis.tags,
      media_type: tweetData.isThread ? 'thread' : 'text',
      media_urls: tweetData.isThread ? tweetData.threadTexts : [tweetData.text],
      engagement_metrics: {
        retweets: tweetData.public_metrics.retweet_count,
        likes: tweetData.public_metrics.like_count,
        replies: tweetData.public_metrics.reply_count,
        quotes: tweetData.public_metrics.quote_count
      },
      content_metadata: {
        mentions: this.extractMentions(tweetData.text),
        hashtags: this.extractHashtags(tweetData.text),
        is_thread: tweetData.isThread,
        thread_length: tweetData.isThread ? tweetData.threadTexts.length : 1
      },
      ai_extracted_text: tweetData.text,
      ai_content_quality_score: aiAnalysis.qualityScore,
      ai_topics: aiAnalysis.topics
    }
  }

  /**
   * AI analysis for Instagram content
   */
  private async analyzeInstagramContent(post: InstagramPost): Promise<any> {
    const OpenAI = require('openai')
    const client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    })

    const inputText = post.caption || 'Instagram post with visual content'

    try {
      // Analyze content type and extract information
      const response = await client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert at analyzing social media content about movies and TV shows. Extract key information and classify the content type.'
          },
          {
            role: 'user',
            content: `Analyze this Instagram post about movies/TV:

Caption: "${inputText}"
Media Type: ${post.media_type}

Return a JSON object with:
- title: A descriptive title (max 100 chars)
- contentType: One of: review, recommendation, news, spoiler-free, breakdown, theory
- summary: Brief summary (2-3 sentences)
- referencedTitles: Array of movie/TV titles mentioned
- tags: Array of relevant tags/genres
- topics: Array of main topics discussed
- qualityScore: Score 0.0-1.0 for content quality
- extractedText: Any text visible in images (if applicable)`
          }
        ],
        max_tokens: 500,
        temperature: 0.3
      })

      const result = JSON.parse(response.choices[0]?.message?.content || '{}')
      return {
        title: result.title || 'Instagram Post',
        contentType: result.contentType || 'recommendation',
        summary: result.summary || '',
        referencedTitles: result.referencedTitles || [],
        tags: result.tags || [],
        topics: result.topics || [],
        qualityScore: result.qualityScore || 0.5,
        extractedText: result.extractedText || ''
      }

    } catch (error) {
      console.warn('AI analysis failed for Instagram post, using fallback:', error)
      return {
        title: this.generateFallbackTitle(inputText),
        contentType: 'recommendation',
        summary: inputText.substring(0, 200),
        referencedTitles: [],
        tags: this.extractHashtags(inputText).map(h => h.replace('#', '')),
        topics: [],
        qualityScore: 0.5,
        extractedText: ''
      }
    }
  }

  /**
   * AI analysis for Twitter content
   */
  private async analyzeTwitterContent(tweetData: any): Promise<any> {
    const OpenAI = require('openai')
    const client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    })

    const inputText = tweetData.isThread 
      ? tweetData.threadTexts.join('\n\n')
      : tweetData.text

    try {
      const response = await client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert at analyzing Twitter content about movies and TV shows. Extract key information and classify the content type.'
          },
          {
            role: 'user',
            content: `Analyze this Twitter ${tweetData.isThread ? 'thread' : 'tweet'} about movies/TV:

Content: "${inputText}"

Return a JSON object with:
- title: A descriptive title (max 100 chars)
- contentType: One of: review, recommendation, news, spoiler-free, breakdown, theory
- summary: Brief summary (2-3 sentences)
- referencedTitles: Array of movie/TV titles mentioned
- tags: Array of relevant tags/genres
- topics: Array of main topics discussed
- qualityScore: Score 0.0-1.0 for content quality`
          }
        ],
        max_tokens: 400,
        temperature: 0.3
      })

      const result = JSON.parse(response.choices[0]?.message?.content || '{}')
      return {
        title: result.title || (tweetData.isThread ? 'Twitter Thread' : 'Tweet'),
        contentType: result.contentType || 'news',
        summary: result.summary || '',
        referencedTitles: result.referencedTitles || [],
        tags: result.tags || [],
        topics: result.topics || [],
        qualityScore: result.qualityScore || 0.5
      }

    } catch (error) {
      console.warn('AI analysis failed for Twitter content, using fallback:', error)
      return {
        title: this.generateFallbackTitle(inputText),
        contentType: 'news',
        summary: inputText.substring(0, 200),
        referencedTitles: [],
        tags: this.extractHashtags(inputText).map(h => h.replace('#', '')),
        topics: [],
        qualityScore: 0.5
      }
    }
  }

  // Helper methods
  private async checkContentExists(creatorId: string, platformId: string, platform: string): Promise<boolean> {
    const { data } = await this.supabase
      .from('content')
      .select('id')
      .eq('creator_id', creatorId)
      .eq('platform_id', platformId)
      .eq('platform', platform)
      .single()

    return !!data
  }

  private extractHashtags(text: string): string[] {
    const hashtags = text.match(/#\w+/g) || []
    return hashtags.slice(0, 10) // Limit to 10 hashtags
  }

  private extractMentions(text: string): string[] {
    const mentions = text.match(/@\w+/g) || []
    return mentions.slice(0, 5) // Limit to 5 mentions
  }

  private generateFallbackTitle(text: string): string {
    const words = text.split(' ').slice(0, 10).join(' ')
    return words.length > 80 ? words.substring(0, 77) + '...' : words
  }

  // Placeholder methods for API calls (to be implemented with actual API keys)
  private async fetchInstagramPosts(userId: string): Promise<InstagramPost[]> {
    // TODO: Implement Instagram Basic Display API call
    console.log('📸 Fetching Instagram posts for user:', userId)
    return []
  }

  private async fetchTwitterTweets(userId: string): Promise<TwitterTweet[]> {
    // TODO: Implement Twitter API v2 call
    console.log('🐦 Fetching Twitter tweets for user:', userId)
    return []
  }

  private async groupTwitterThreads(tweets: TwitterTweet[]): Promise<any[]> {
    // TODO: Implement thread detection and grouping logic
    console.log('🧵 Grouping Twitter threads')
    return tweets.map(tweet => ({ ...tweet, isThread: false, threadTexts: [tweet.text] }))
  }
}

export const multiPlatformAggregator = new MultiPlatformAggregator()

// Instagram Basic Display API integration
export class InstagramAPI {
  private appId: string
  private appSecret: string
  private redirectUri: string

  constructor() {
    this.appId = process.env.INSTAGRAM_APP_ID!
    this.appSecret = process.env.INSTAGRAM_APP_SECRET!
    this.redirectUri = process.env.INSTAGRAM_REDIRECT_URI!
  }

  /**
   * Get Instagram authorization URL for creator onboarding
   */
  getAuthUrl(state?: string): string {
    const params = new URLSearchParams({
      client_id: this.appId,
      redirect_uri: this.redirectUri,
      scope: 'user_profile,user_media',
      response_type: 'code',
      ...(state && { state })
    })

    return `https://api.instagram.com/oauth/authorize?${params.toString()}`
  }

  /**
   * Exchange authorization code for access token
   */
  async getAccessToken(code: string): Promise<{ access_token: string; user_id: string }> {
    const response = await fetch('https://api.instagram.com/oauth/access_token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        client_id: this.appId,
        client_secret: this.appSecret,
        grant_type: 'authorization_code',
        redirect_uri: this.redirectUri,
        code
      })
    })

    if (!response.ok) {
      throw new Error(`Instagram auth failed: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get long-lived access token (60 days)
   */
  async getLongLivedToken(shortToken: string): Promise<{ access_token: string; expires_in: number }> {
    const params = new URLSearchParams({
      grant_type: 'ig_exchange_token',
      client_secret: this.appSecret,
      access_token: shortToken
    })

    const response = await fetch(`https://graph.instagram.com/access_token?${params.toString()}`)
    
    if (!response.ok) {
      throw new Error(`Instagram long-lived token failed: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Refresh long-lived access token
   */
  async refreshToken(accessToken: string): Promise<{ access_token: string; expires_in: number }> {
    const params = new URLSearchParams({
      grant_type: 'ig_refresh_token',
      access_token: accessToken
    })

    const response = await fetch(`https://graph.instagram.com/refresh_access_token?${params.toString()}`)
    
    if (!response.ok) {
      throw new Error(`Instagram token refresh failed: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Get user's Instagram media
   */
  async getUserMedia(accessToken: string, limit: number = 25): Promise<any[]> {
    const fields = [
      'id',
      'caption',
      'media_type',
      'media_url',
      'permalink',
      'timestamp',
      'like_count',
      'comments_count',
      'children{media_url,media_type}'
    ].join(',')

    const params = new URLSearchParams({
      fields,
      limit: limit.toString(),
      access_token: accessToken
    })

    const response = await fetch(`https://graph.instagram.com/me/media?${params.toString()}`)
    
    if (!response.ok) {
      throw new Error(`Instagram media fetch failed: ${response.statusText}`)
    }

    const data = await response.json()
    return data.data || []
  }

  /**
   * Get specific media details
   */
  async getMediaDetails(mediaId: string, accessToken: string): Promise<any> {
    const fields = [
      'id',
      'caption',
      'media_type',
      'media_url',
      'permalink',
      'timestamp',
      'like_count',
      'comments_count',
      'children{media_url,media_type}'
    ].join(',')

    const params = new URLSearchParams({
      fields,
      access_token: accessToken
    })

    const response = await fetch(`https://graph.instagram.com/${mediaId}?${params.toString()}`)
    
    if (!response.ok) {
      throw new Error(`Instagram media details failed: ${response.statusText}`)
    }

    return response.json()
  }

  /**
   * Search for movie/TV related hashtags
   */
  async searchHashtag(hashtag: string, accessToken: string): Promise<any[]> {
    // Note: Hashtag search requires Instagram Business account
    // This is a placeholder for when you upgrade to Business API
    console.log(`Searching for hashtag: ${hashtag}`)
    return []
  }

  /**
   * Filter content for movie/TV relevance
   */
  filterMovieTVContent(posts: any[]): any[] {
    const movieTVKeywords = [
      'movie', 'film', 'cinema', 'review', 'recommendation',
      'tv', 'series', 'show', 'episode', 'season',
      'netflix', 'disney', 'hbo', 'amazon', 'hulu',
      'marvel', 'dc', 'horror', 'comedy', 'drama',
      'actor', 'actress', 'director', 'trailer'
    ]

    return posts.filter(post => {
      const caption = (post.caption || '').toLowerCase()
      return movieTVKeywords.some(keyword => caption.includes(keyword))
    })
  }
}

export const instagramAPI = new InstagramAPI()

import OpenAI from 'openai'

export class OpenAIService {
  private client: OpenAI | null = null

  constructor(apiKey?: string) {
    const key = apiKey || process.env.OPENAI_API_KEY
    if (key) {
      this.client = new OpenAI({
        apiKey: key
      })
      console.log('OpenAI client initialized with key:', key.substring(0, 10) + '...')
    } else {
      console.warn('OpenAI API key not found. AI features will be disabled.')
    }
  }

  /**
   * Summarize video content from title and description
   */
  async summarizeContent(
    title: string,
    description: string,
    contentType: string = 'review'
  ): Promise<string> {
    if (!this.client) {
      throw new Error('OpenAI API key is required for AI summarization')
    }

    try {
      const prompt = this.buildSummarizationPrompt(title, description, contentType)
      
      const response = await this.client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert at summarizing movie and TV content. Provide concise, engaging summaries that capture the key points without spoilers unless explicitly mentioned.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 200,
        temperature: 0.7
      })

      return response.choices[0]?.message?.content?.trim() || 'Summary not available'
    } catch (error) {
      console.error('Error generating AI summary:', error)
      throw new Error('Failed to generate AI summary')
    }
  }

  /**
   * Extract movie/TV show titles from content
   */
  async extractTitles(title: string, description: string): Promise<string[]> {
    if (!this.client) {
      throw new Error('OpenAI API key is required for title extraction')
    }

    try {
      const prompt = `
        Extract all movie and TV show titles mentioned in the following content.
        Return only the titles as a JSON array of strings.
        
        Title: ${title}
        Description: ${description}
        
        Focus on:
        - Movies and TV shows being reviewed, discussed, or analyzed
        - Titles in quotes or explicitly mentioned
        - Popular franchises and series
        
        Return format: ["Title 1", "Title 2", "Title 3"]
      `

      const response = await this.client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert at identifying movie and TV show titles in content. Return only valid JSON arrays.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 150,
        temperature: 0.3
      })

      const content = response.choices[0]?.message?.content?.trim()
      if (!content) return []

      try {
        const titles = JSON.parse(content)
        return Array.isArray(titles) ? titles.filter(title => typeof title === 'string' && title.length > 0) : []
      } catch {
        // If JSON parsing fails, try to extract titles manually
        return this.fallbackTitleExtraction(title, description)
      }
    } catch (error) {
      console.error('Error extracting titles with AI:', error)
      return this.fallbackTitleExtraction(title, description)
    }
  }

  /**
   * Categorize content type based on title and description
   */
  async categorizeContent(title: string, description: string): Promise<string> {
    if (!this.client) {
      // Fallback to simple keyword matching
      return this.fallbackCategorization(title, description)
    }

    try {
      const prompt = `
        Categorize the following content into one of these types:
        - review: Reviews, ratings, opinions about movies/shows
        - theory: Fan theories, explanations, analysis
        - news: News, updates, announcements
        - spoiler-free: Spoiler-free reviews or discussions
        - breakdown: Scene breakdowns, behind-the-scenes content
        - recommendation: Recommendations, "must watch" lists
        
        Title: ${title}
        Description: ${description}
        
        Return only the category name (lowercase).
      `

      const response = await this.client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert at categorizing movie and TV content. Return only the category name.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 10,
        temperature: 0.1
      })

      const category = response.choices[0]?.message?.content?.trim().toLowerCase()
      const validCategories = ['review', 'theory', 'news', 'spoiler-free', 'breakdown', 'recommendation']
      
      return validCategories.includes(category || '') ? category! : 'review'
    } catch (error) {
      console.error('Error categorizing content with AI:', error)
      return this.fallbackCategorization(title, description)
    }
  }

  /**
   * Generate tags for content
   */
  async generateTags(title: string, description: string, referencedTitles: string[]): Promise<string[]> {
    if (!this.client) {
      return this.fallbackTagGeneration(title, description)
    }

    try {
      const prompt = `
        Generate relevant tags for this movie/TV content. Focus on:
        - Genres (action, comedy, horror, etc.)
        - Themes (superhero, romance, sci-fi, etc.)
        - Content attributes (spoiler-free, in-depth, quick-review, etc.)
        
        Title: ${title}
        Description: ${description}
        Referenced titles: ${referencedTitles.join(', ')}
        
        Return 3-8 relevant tags as a JSON array.
        Format: ["tag1", "tag2", "tag3"]
      `

      const response = await this.client.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [
          {
            role: 'system',
            content: 'You are an expert at generating relevant tags for movie and TV content. Return only valid JSON arrays.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 100,
        temperature: 0.5
      })

      const content = response.choices[0]?.message?.content?.trim()
      if (!content) return []

      try {
        const tags = JSON.parse(content)
        return Array.isArray(tags) ? tags.filter(tag => typeof tag === 'string' && tag.length > 0) : []
      } catch {
        return this.fallbackTagGeneration(title, description)
      }
    } catch (error) {
      console.error('Error generating tags with AI:', error)
      return this.fallbackTagGeneration(title, description)
    }
  }

  private buildSummarizationPrompt(title: string, description: string, contentType: string): string {
    return `
      Summarize this ${contentType} content in 2-3 sentences. Be engaging and informative.
      
      Title: ${title}
      Description: ${description}
      
      Focus on the main points and what makes this content valuable to viewers.
      Keep it concise and spoiler-free unless the content explicitly contains spoilers.
    `
  }

  private fallbackTitleExtraction(title: string, description: string): string[] {
    const text = `${title} ${description}`
    const titlePattern = /["']([^"']{2,50})["']/g
    const titles: string[] = []
    let match

    while ((match = titlePattern.exec(text)) !== null) {
      titles.push(match[1])
    }

    return [...new Set(titles)]
  }

  private fallbackCategorization(title: string, description: string): string {
    const text = `${title} ${description}`.toLowerCase()
    
    if (text.includes('review') || text.includes('rating')) return 'review'
    if (text.includes('theory') || text.includes('explained')) return 'theory'
    if (text.includes('news') || text.includes('update')) return 'news'
    if (text.includes('spoiler-free')) return 'spoiler-free'
    if (text.includes('breakdown') || text.includes('scene')) return 'breakdown'
    if (text.includes('recommend') || text.includes('must watch')) return 'recommendation'
    
    return 'review'
  }

  private fallbackTagGeneration(title: string, description: string): string[] {
    const text = `${title} ${description}`.toLowerCase()
    const tags: string[] = []

    // Genre detection
    if (text.includes('horror')) tags.push('horror')
    if (text.includes('comedy')) tags.push('comedy')
    if (text.includes('action')) tags.push('action')
    if (text.includes('drama')) tags.push('drama')
    if (text.includes('sci-fi') || text.includes('science fiction')) tags.push('sci-fi')
    if (text.includes('romance')) tags.push('romance')
    if (text.includes('thriller')) tags.push('thriller')
    if (text.includes('anime')) tags.push('anime')
    if (text.includes('superhero')) tags.push('superhero')

    // Content type tags
    if (text.includes('spoiler-free')) tags.push('spoiler-free')
    if (text.includes('in-depth')) tags.push('in-depth')
    if (text.includes('quick')) tags.push('quick-review')

    return tags.length > 0 ? tags : ['general']
  }
}

// Export function to create instance with fresh environment variables
export const createOpenAIService = () => new OpenAIService()

// Export singleton instance (created lazily)
export const openaiService = createOpenAIService()

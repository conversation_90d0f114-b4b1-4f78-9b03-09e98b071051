// Alternative content aggregation using accessible APIs and web scraping
import { createServiceRoleClient } from '@/lib/supabase-server'

export class AlternativeAggregator {
  private supabase = createServiceRoleClient()

  /**
   * 1. YouTube RSS Feed Aggregation (No API key needed!)
   */
  async aggregateYouTubeRSS(channelId: string, creatorId: string): Promise<any> {
    try {
      const rssUrl = `https://www.youtube.com/feeds/videos.xml?channel_id=${channelId}`
      const response = await fetch(rssUrl)
      const xmlText = await response.text()
      
      // Parse XML to extract video data
      const videos = this.parseYouTubeRSS(xmlText)
      
      let contentAdded = 0
      for (const video of videos) {
        const exists = await this.checkContentExists(creatorId, video.id, 'youtube')
        if (!exists) {
          await this.storeContent({
            creator_id: creatorId,
            title: video.title,
            description: video.description,
            content_type: this.classifyContent(video.title, video.description),
            platform: 'youtube',
            platform_url: video.url,
            platform_id: video.id,
            thumbnail_url: video.thumbnail,
            published_at: video.publishedAt,
            media_type: 'video'
          })
          contentAdded++
        }
      }
      
      return { contentAdded, platform: 'youtube_rss' }
    } catch (error) {
      console.error('YouTube RSS aggregation failed:', error)
      return { contentAdded: 0, error: error.message }
    }
  }

  /**
   * 2. Reddit API Aggregation (Free!)
   */
  async aggregateRedditContent(): Promise<any> {
    try {
      const subreddits = [
        'movies', 'television', 'MovieDetails', 'FanTheories', 
        'netflix', 'horror', 'scifi', 'marvelstudios'
      ]
      
      let totalContentAdded = 0
      
      for (const subreddit of subreddits) {
        const url = `https://www.reddit.com/r/${subreddit}/hot.json?limit=25`
        const response = await fetch(url, {
          headers: {
            'User-Agent': 'SceneSniffer/1.0 (Content Aggregator)'
          }
        })
        
        const data = await response.json()
        const posts = data.data.children
        
        for (const post of posts) {
          const postData = post.data
          
          // Filter for movie/TV content
          if (this.isMovieTVContent(postData.title, postData.selftext)) {
            const exists = await this.checkContentExists('reddit', postData.id, 'reddit')
            if (!exists) {
              await this.storeContent({
                creator_id: await this.getOrCreateRedditCreator(postData.author),
                title: postData.title,
                description: postData.selftext || '',
                content_type: this.classifyRedditContent(postData),
                platform: 'reddit',
                platform_url: `https://reddit.com${postData.permalink}`,
                platform_id: postData.id,
                thumbnail_url: postData.thumbnail !== 'self' ? postData.thumbnail : null,
                published_at: new Date(postData.created_utc * 1000).toISOString(),
                media_type: 'text',
                engagement_metrics: {
                  upvotes: postData.ups,
                  comments: postData.num_comments,
                  awards: postData.total_awards_received
                },
                content_metadata: {
                  subreddit: postData.subreddit,
                  flair: postData.link_flair_text,
                  gilded: postData.gilded
                }
              })
              totalContentAdded++
            }
          }
        }
      }
      
      return { contentAdded: totalContentAdded, platform: 'reddit' }
    } catch (error) {
      console.error('Reddit aggregation failed:', error)
      return { contentAdded: 0, error: error.message }
    }
  }

  /**
   * 3. Podcast RSS Aggregation
   */
  async aggregatePodcastContent(): Promise<any> {
    try {
      const podcastFeeds = [
        {
          name: 'The Watch',
          url: 'https://feeds.megaphone.fm/the-watch',
          creatorName: 'The Ringer'
        },
        {
          name: 'The Big Picture',
          url: 'https://feeds.simplecast.com/dHoohVNH',
          creatorName: 'The Ringer'
        },
        {
          name: 'The Rewatchables',
          url: 'https://rss.art19.com/the-rewatchables',
          creatorName: 'The Ringer'
        }
      ]
      
      let totalContentAdded = 0
      
      for (const podcast of podcastFeeds) {
        const response = await fetch(podcast.url)
        const xmlText = await response.text()
        const episodes = this.parsePodcastRSS(xmlText)
        
        const creatorId = await this.getOrCreatePodcastCreator(podcast.name, podcast.creatorName)
        
        for (const episode of episodes) {
          const exists = await this.checkContentExists(creatorId, episode.guid, 'podcast')
          if (!exists) {
            await this.storeContent({
              creator_id: creatorId,
              title: episode.title,
              description: episode.description,
              content_type: 'review', // Most podcast episodes are reviews/discussions
              platform: 'podcast',
              platform_url: episode.url,
              platform_id: episode.guid,
              thumbnail_url: episode.image,
              published_at: episode.publishedAt,
              media_type: 'audio',
              duration: episode.duration,
              content_metadata: {
                podcast_name: podcast.name,
                episode_number: episode.episodeNumber
              }
            })
            totalContentAdded++
          }
        }
      }
      
      return { contentAdded: totalContentAdded, platform: 'podcast' }
    } catch (error) {
      console.error('Podcast aggregation failed:', error)
      return { contentAdded: 0, error: error.message }
    }
  }

  /**
   * 4. Letterboxd Web Scraping
   */
  async aggregateLetterboxdContent(): Promise<any> {
    try {
      // Note: This would require a web scraping library like Puppeteer
      // For now, this is a placeholder showing the structure
      
      const letterboxdUrls = [
        'https://letterboxd.com/films/popular/this/week/',
        'https://letterboxd.com/reviews/popular/this/week/'
      ]
      
      // Would implement web scraping here
      console.log('Letterboxd scraping would be implemented here')
      
      return { contentAdded: 0, platform: 'letterboxd', note: 'Requires web scraping setup' }
    } catch (error) {
      console.error('Letterboxd aggregation failed:', error)
      return { contentAdded: 0, error: error.message }
    }
  }

  /**
   * 5. Mastodon API Aggregation (Open source Twitter alternative)
   */
  async aggregateMastodonContent(): Promise<any> {
    try {
      const instances = [
        'https://mastodon.social/api/v1/timelines/tag/movies',
        'https://mastodon.social/api/v1/timelines/tag/television',
        'https://film.social/api/v1/timelines/public'
      ]
      
      let totalContentAdded = 0
      
      for (const instanceUrl of instances) {
        const response = await fetch(instanceUrl)
        const posts = await response.json()
        
        for (const post of posts) {
          if (this.isMovieTVContent(post.content, '')) {
            const exists = await this.checkContentExists('mastodon', post.id, 'mastodon')
            if (!exists) {
              await this.storeContent({
                creator_id: await this.getOrCreateMastodonCreator(post.account),
                title: this.extractTitle(post.content),
                description: this.stripHTML(post.content),
                content_type: this.classifyContent(post.content, ''),
                platform: 'mastodon',
                platform_url: post.url,
                platform_id: post.id,
                published_at: post.created_at,
                media_type: 'text',
                engagement_metrics: {
                  reblogs: post.reblogs_count,
                  favourites: post.favourites_count,
                  replies: post.replies_count
                }
              })
              totalContentAdded++
            }
          }
        }
      }
      
      return { contentAdded: totalContentAdded, platform: 'mastodon' }
    } catch (error) {
      console.error('Mastodon aggregation failed:', error)
      return { contentAdded: 0, error: error.message }
    }
  }

  // Helper methods
  private parseYouTubeRSS(xml: string): any[] {
    // XML parsing logic for YouTube RSS
    // Would use a library like 'fast-xml-parser'
    return []
  }

  private parsePodcastRSS(xml: string): any[] {
    // XML parsing logic for podcast RSS
    return []
  }

  private isMovieTVContent(title: string, content: string): boolean {
    const keywords = [
      'movie', 'film', 'cinema', 'tv', 'series', 'show', 'episode',
      'netflix', 'disney', 'hbo', 'marvel', 'dc', 'review', 'trailer'
    ]
    const text = (title + ' ' + content).toLowerCase()
    return keywords.some(keyword => text.includes(keyword))
  }

  private classifyContent(title: string, description: string): string {
    const text = (title + ' ' + description).toLowerCase()
    
    if (text.includes('review') || text.includes('rating')) return 'review'
    if (text.includes('recommend') || text.includes('must watch')) return 'recommendation'
    if (text.includes('theory') || text.includes('explained')) return 'theory'
    if (text.includes('news') || text.includes('announced')) return 'news'
    if (text.includes('breakdown') || text.includes('analysis')) return 'breakdown'
    
    return 'review' // Default
  }

  private classifyRedditContent(postData: any): string {
    const flair = postData.link_flair_text?.toLowerCase() || ''
    const title = postData.title.toLowerCase()
    
    if (flair.includes('discussion') || title.includes('discussion')) return 'theory'
    if (flair.includes('review') || title.includes('review')) return 'review'
    if (flair.includes('news') || title.includes('news')) return 'news'
    if (title.includes('theory') || title.includes('explained')) return 'theory'
    
    return 'review'
  }

  private async checkContentExists(creatorId: string, platformId: string, platform: string): Promise<boolean> {
    const { data } = await this.supabase
      .from('content')
      .select('id')
      .eq('creator_id', creatorId)
      .eq('platform_id', platformId)
      .eq('platform', platform)
      .single()
    
    return !!data
  }

  private async storeContent(contentData: any): Promise<void> {
    const { error } = await this.supabase
      .from('content')
      .insert(contentData)
    
    if (error) {
      console.error('Error storing content:', error)
    }
  }

  private async getOrCreateRedditCreator(username: string): Promise<string> {
    // Implementation to find or create Reddit user as creator
    return 'reddit-creator-id'
  }

  private async getOrCreatePodcastCreator(podcastName: string, creatorName: string): Promise<string> {
    // Implementation to find or create podcast as creator
    return 'podcast-creator-id'
  }

  private async getOrCreateMastodonCreator(account: any): Promise<string> {
    // Implementation to find or create Mastodon user as creator
    return 'mastodon-creator-id'
  }

  private extractTitle(content: string): string {
    // Extract title from HTML content
    const stripped = this.stripHTML(content)
    return stripped.substring(0, 100) + (stripped.length > 100 ? '...' : '')
  }

  private stripHTML(html: string): string {
    return html.replace(/<[^>]*>/g, '')
  }
}

export const alternativeAggregator = new AlternativeAggregator()

import axios from 'axios'
import { YouTubeVideo } from '@/types'

const YOUTUBE_API_BASE_URL = 'https://www.googleapis.com/youtube/v3'
const API_KEY = process.env.YOUTUBE_API_KEY

if (!API_KEY) {
  console.warn('YouTube API key not found. YouTube features will be disabled.')
}

export class YouTubeAPI {
  private apiKey: string

  constructor(apiKey?: string) {
    this.apiKey = apiKey || API_KEY || ''
  }

  /**
   * Search for videos by channel ID
   */
  async getChannelVideos(
    channelId: string,
    maxResults: number = 50,
    publishedAfter?: string
  ): Promise<YouTubeVideo[]> {
    if (!this.apiKey) {
      throw new Error('YouTube API key is required')
    }

    try {
      const params: any = {
        part: 'snippet',
        channelId,
        maxResults,
        order: 'date',
        type: 'video',
        key: this.apiKey
      }

      if (publishedAfter) {
        params.publishedAfter = publishedAfter
      }

      const response = await axios.get(`${YOUTUBE_API_BASE_URL}/search`, {
        params
      })

      return response.data.items || []
    } catch (error) {
      console.error('Error fetching YouTube videos:', error)
      throw new Error('Failed to fetch YouTube videos')
    }
  }

  /**
   * Get channel information by handle or channel ID
   */
  async getChannelInfo(channelIdentifier: string): Promise<any> {
    if (!this.apiKey) {
      throw new Error('YouTube API key is required')
    }

    try {
      // Try to get channel by handle first (if it starts with @)
      if (channelIdentifier.startsWith('@')) {
        const handle = channelIdentifier.substring(1)
        const response = await axios.get(`${YOUTUBE_API_BASE_URL}/channels`, {
          params: {
            part: 'snippet,statistics',
            forHandle: handle,
            key: this.apiKey
          }
        })

        if (response.data.items && response.data.items.length > 0) {
          return response.data.items[0]
        }
      }

      // Try to get channel by ID
      const response = await axios.get(`${YOUTUBE_API_BASE_URL}/channels`, {
        params: {
          part: 'snippet,statistics',
          id: channelIdentifier,
          key: this.apiKey
        }
      })

      if (response.data.items && response.data.items.length > 0) {
        return response.data.items[0]
      }

      throw new Error('Channel not found')
    } catch (error) {
      console.error('Error fetching YouTube channel:', error)
      throw new Error('Failed to fetch YouTube channel information')
    }
  }

  /**
   * Get video details including captions for AI processing
   */
  async getVideoDetails(videoId: string): Promise<any> {
    if (!this.apiKey) {
      throw new Error('YouTube API key is required')
    }

    try {
      const response = await axios.get(`${YOUTUBE_API_BASE_URL}/videos`, {
        params: {
          part: 'snippet,contentDetails,statistics',
          id: videoId,
          key: this.apiKey
        }
      })

      if (response.data.items && response.data.items.length > 0) {
        return response.data.items[0]
      }

      throw new Error('Video not found')
    } catch (error) {
      console.error('Error fetching YouTube video details:', error)
      throw new Error('Failed to fetch YouTube video details')
    }
  }

  /**
   * Search for videos by query
   */
  async searchVideos(
    query: string,
    maxResults: number = 25,
    channelId?: string
  ): Promise<YouTubeVideo[]> {
    if (!this.apiKey) {
      throw new Error('YouTube API key is required')
    }

    try {
      const params: any = {
        part: 'snippet',
        q: query,
        maxResults,
        order: 'relevance',
        type: 'video',
        key: this.apiKey
      }

      if (channelId) {
        params.channelId = channelId
      }

      const response = await axios.get(`${YOUTUBE_API_BASE_URL}/search`, {
        params
      })

      return response.data.items || []
    } catch (error) {
      console.error('Error searching YouTube videos:', error)
      throw new Error('Failed to search YouTube videos')
    }
  }

  /**
   * Extract movie/TV show titles from video title and description
   */
  extractReferencedTitles(title: string, description: string): string[] {
    const titles: string[] = []
    const text = `${title} ${description}`.toLowerCase()

    // Common patterns for movie/TV references
    const patterns = [
      /(?:movie|film|series|show|anime)[\s:]*["']([^"']+)["']/gi,
      /["']([^"']+)["'][\s]*(?:movie|film|series|show|anime)/gi,
      /(?:review|breakdown|analysis|theory)[\s:]*["']([^"']+)["']/gi,
      /(?:watching|watched|seen)[\s:]*["']([^"']+)["']/gi
    ]

    patterns.forEach(pattern => {
      let match
      while ((match = pattern.exec(text)) !== null) {
        const title = match[1].trim()
        if (title.length > 2 && title.length < 100) {
          titles.push(title)
        }
      }
    })

    // Remove duplicates and return
    return [...new Set(titles)]
  }

  /**
   * Determine content type based on video metadata
   */
  categorizeContent(title: string, description: string): string {
    const text = `${title} ${description}`.toLowerCase()

    if (text.includes('review') || text.includes('rating')) {
      return 'review'
    }
    if (text.includes('theory') || text.includes('explained') || text.includes('analysis')) {
      return 'theory'
    }
    if (text.includes('news') || text.includes('update') || text.includes('announcement')) {
      return 'news'
    }
    if (text.includes('spoiler-free') || text.includes('no spoilers')) {
      return 'spoiler-free'
    }
    if (text.includes('breakdown') || text.includes('scene') || text.includes('behind')) {
      return 'breakdown'
    }
    if (text.includes('recommend') || text.includes('must watch') || text.includes('best')) {
      return 'recommendation'
    }

    // Default to review if unclear
    return 'review'
  }
}

// Export singleton instance
export const youtubeAPI = new YouTubeAPI()

// Podcast RSS feed aggregation for movie/TV content
import { XMLParser } from 'fast-xml-parser'
import { createServiceRoleClient } from '@/lib/supabase-server'

interface PodcastEpisode {
  title: string
  description: string
  link: string
  guid: string
  pubDate: string
  'itunes:duration'?: string
  'itunes:image'?: {
    '@_href': string
  }
  enclosure?: {
    '@_url': string
    '@_type': string
  }
  'itunes:episode'?: string
  'itunes:season'?: string
}

interface PodcastFeed {
  rss: {
    channel: {
      title: string
      description: string
      link: string
      'itunes:image'?: {
        '@_href': string
      }
      'itunes:author'?: string
      item: PodcastEpisode[]
    }
  }
}

export class PodcastRSSAggregator {
  private supabase = createServiceRoleClient()
  private xmlParser = new XMLParser({
    ignoreAttributes: false,
    attributeNamePrefix: '@_'
  })

  // Top movie/TV podcasts with RSS feeds (verified working URLs)
  private readonly MOVIE_TV_PODCASTS = [
    {
      name: 'The Big Picture',
      url: 'https://feeds.simplecast.com/dHoohVNH',
      creator: 'The Ringer',
      focus: 'movie_discussion',
      description: 'Movie discussions and industry analysis'
    },
    {
      name: 'The Rewatchables',
      url: 'https://rss.art19.com/the-rewatchables',
      creator: 'The Ringer',
      focus: 'movie_review',
      description: 'Rewatching and discussing classic movies'
    },
    {
      name: 'The Watch',
      url: 'https://feeds.megaphone.fm/the-watch',
      creator: 'The Ringer',
      focus: 'tv_discussion',
      description: 'TV show discussions and analysis'
    }
  ]

  /**
   * Aggregate content from all movie/TV podcasts
   */
  async aggregateAllPodcasts(): Promise<{
    success: boolean
    contentAdded: number
    errors: string[]
    podcastResults: Record<string, any>
  }> {
    console.log('🎙️ Starting podcast RSS aggregation across all movie/TV podcasts...')
    
    let totalContentAdded = 0
    const errors: string[] = []
    const podcastResults: Record<string, any> = {}

    for (const podcast of this.MOVIE_TV_PODCASTS) {
      try {
        console.log(`🎧 Processing ${podcast.name} by ${podcast.creator}...`)
        
        const result = await this.aggregatePodcast(podcast)
        podcastResults[podcast.name] = result
        totalContentAdded += result.contentAdded
        
        if (result.errors.length > 0) {
          errors.push(...result.errors)
        }

        // Small delay to be respectful
        await this.delay(1000)
        
      } catch (error) {
        const errorMsg = `Failed to process ${podcast.name}: ${error}`
        console.error(errorMsg)
        errors.push(errorMsg)
      }
    }

    return {
      success: errors.length === 0,
      contentAdded: totalContentAdded,
      errors,
      podcastResults
    }
  }

  /**
   * Aggregate content from a specific podcast
   */
  async aggregatePodcast(podcast: any): Promise<{
    contentAdded: number
    errors: string[]
    episodes: any[]
  }> {
    const errors: string[] = []
    const processedEpisodes: any[] = []
    let contentAdded = 0

    try {
      // Fetch RSS feed for the podcast
      const episodes = await this.fetchPodcastRSS(podcast.url)
      
      console.log(`📊 Found ${episodes.length} episodes from ${podcast.name}`)

      // Get or create creator
      const creatorId = await this.getOrCreatePodcastCreator(podcast)
      
      if (!creatorId) {
        errors.push(`Could not create creator for ${podcast.name}`)
        return { contentAdded: 0, errors, episodes: [] }
      }

      for (const episode of episodes) {
        try {
          // Filter for movie/TV content
          if (!this.isMovieTVEpisode(episode)) {
            continue
          }

          // Check if episode already exists
          const exists = await this.checkEpisodeExists(episode.guid)
          if (exists) {
            console.log(`⏭️ Episode ${episode.guid} already exists`)
            continue
          }

          // Process and store the episode
          const contentData = await this.processPodcastEpisode(episode, creatorId, podcast)
          
          const { error: insertError } = await this.supabase
            .from('content')
            .insert(contentData)

          if (insertError) {
            console.error(`❌ Error inserting episode ${episode.guid}:`, insertError)
            errors.push(`Failed to insert episode ${episode.guid}: ${insertError.message}`)
          } else {
            console.log(`✅ Added podcast episode: ${contentData.title}`)
            contentAdded++
            processedEpisodes.push(contentData)
          }

        } catch (episodeError) {
          console.error(`❌ Error processing episode ${episode.guid}:`, episodeError)
          errors.push(`Failed to process episode ${episode.guid}: ${episodeError}`)
        }
      }

    } catch (error) {
      console.error(`❌ Error fetching RSS for ${podcast.name}:`, error)
      errors.push(`Failed to fetch RSS for ${podcast.name}: ${error}`)
    }

    return { contentAdded, errors, episodes: processedEpisodes }
  }

  /**
   * Fetch podcast RSS feed
   */
  private async fetchPodcastRSS(rssUrl: string): Promise<PodcastEpisode[]> {
    const response = await fetch(rssUrl, {
      headers: {
        'User-Agent': 'SceneSniffer/1.0 (Podcast Aggregator)'
      }
    })
    
    if (!response.ok) {
      throw new Error(`Podcast RSS error: ${response.status} ${response.statusText}`)
    }

    const xmlText = await response.text()
    const parsedData: PodcastFeed = this.xmlParser.parse(xmlText)
    
    if (!parsedData.rss || !parsedData.rss.channel || !parsedData.rss.channel.item) {
      throw new Error('Invalid podcast RSS structure')
    }

    // Handle single episode vs array
    const episodes = Array.isArray(parsedData.rss.channel.item) 
      ? parsedData.rss.channel.item 
      : [parsedData.rss.channel.item]

    return episodes.slice(0, 20) // Limit to recent 20 episodes
  }

  /**
   * Check if episode is movie/TV related
   */
  private isMovieTVEpisode(episode: PodcastEpisode): boolean {
    const text = (episode.title + ' ' + episode.description).toLowerCase()
    
    const movieTVKeywords = [
      'movie', 'film', 'cinema', 'tv', 'series', 'show', 'episode', 'season',
      'netflix', 'disney', 'hbo', 'amazon', 'hulu', 'apple tv', 'paramount',
      'marvel', 'dc', 'star wars', 'horror', 'comedy', 'drama', 'thriller',
      'actor', 'actress', 'director', 'trailer', 'review', 'recommendation',
      'spoiler', 'theory', 'ending', 'plot', 'character', 'cast', 'franchise',
      'rewatch', 'draft', 'binge', 'streaming', 'premiere', 'finale'
    ]

    return movieTVKeywords.some(keyword => text.includes(keyword))
  }

  /**
   * Process podcast episode into content format
   */
  private async processPodcastEpisode(episode: PodcastEpisode, creatorId: string, podcast: any): Promise<any> {
    const contentType = this.classifyPodcastContent(episode, podcast.focus)
    const duration = this.parseDuration(episode['itunes:duration'])
    const publishedAt = new Date(episode.pubDate).toISOString()
    
    return {
      creator_id: creatorId,
      title: episode.title,
      description: episode.description || '',
      content_type: contentType,
      platform: 'podcast',
      platform_url: episode.link,
      platform_id: episode.guid,
      thumbnail_url: episode['itunes:image']?.['@_href'] || null,
      published_at: publishedAt,
      media_type: 'audio',
      duration: duration,
      engagement_metrics: {
        // Podcasts don't typically have public engagement metrics
      },
      content_metadata: {
        podcast_name: podcast.name,
        creator: podcast.creator,
        episode_number: episode['itunes:episode'],
        season_number: episode['itunes:season'],
        audio_url: episode.enclosure?.['@_url'],
        focus_area: podcast.focus
      },
      ai_content_quality_score: this.calculatePodcastQualityScore(episode, podcast),
      tags: this.extractPodcastTags(episode, podcast),
      referenced_titles: this.extractReferencedTitles(episode.title + ' ' + episode.description)
    }
  }

  /**
   * Classify podcast content type
   */
  private classifyPodcastContent(episode: PodcastEpisode, focus: string): string {
    const title = episode.title.toLowerCase()
    const description = episode.description.toLowerCase()
    
    // Use podcast focus as primary indicator
    if (focus === 'movie_review' || focus === 'tv_review') return 'review'
    if (focus === 'movie_discussion' || focus === 'tv_discussion') return 'theory'
    if (focus === 'tv_analysis') return 'breakdown'
    
    // Fallback to episode content analysis
    if (title.includes('review') || description.includes('review')) return 'review'
    if (title.includes('draft') || description.includes('draft')) return 'recommendation'
    if (title.includes('rewatch') || description.includes('rewatch')) return 'review'
    if (title.includes('breakdown') || description.includes('analysis')) return 'breakdown'
    if (title.includes('theory') || description.includes('explained')) return 'theory'
    
    return 'review' // Default for podcasts
  }

  /**
   * Parse iTunes duration format
   */
  private parseDuration(duration?: string): number | null {
    if (!duration) return null
    
    try {
      // Handle formats like "1:23:45" or "23:45" or "45"
      const parts = duration.split(':').map(Number)
      
      if (parts.length === 3) {
        // Hours:Minutes:Seconds
        return parts[0] * 3600 + parts[1] * 60 + parts[2]
      } else if (parts.length === 2) {
        // Minutes:Seconds
        return parts[0] * 60 + parts[1]
      } else if (parts.length === 1) {
        // Just seconds
        return parts[0]
      }
    } catch (error) {
      // If parsing fails, try to extract just numbers
      if (typeof duration === 'string') {
        const numbers = duration.match(/\d+/g)
        if (numbers && numbers.length > 0) {
          return parseInt(numbers[0]) * 60 // Assume minutes
        }
      }
    }
    
    return null
  }

  /**
   * Calculate podcast quality score
   */
  private calculatePodcastQualityScore(episode: PodcastEpisode, podcast: any): number {
    let score = 0.8 // Base score for podcast content (generally high quality)
    
    // Episode quality indicators
    if (episode.description.length > 200) score += 0.1 // Good description
    if (episode['itunes:duration']) score += 0.05 // Has duration info
    if (episode['itunes:episode']) score += 0.05 // Has episode number
    
    // Podcast quality indicators
    if (podcast.creator === 'The Ringer') score += 0.1 // High-quality network
    
    return Math.min(score, 1.0)
  }

  /**
   * Extract tags from podcast episode
   */
  private extractPodcastTags(episode: PodcastEpisode, podcast: any): string[] {
    const tags: string[] = []
    
    // Add podcast name as tag
    tags.push(podcast.name.toLowerCase().replace(/\s+/g, '_'))
    
    // Add focus area as tag
    tags.push(podcast.focus)
    
    // Add creator as tag
    tags.push(podcast.creator.toLowerCase().replace(/\s+/g, '_'))
    
    // Extract genre tags from title and description
    const text = (episode.title + ' ' + episode.description).toLowerCase()
    const genreKeywords = ['horror', 'comedy', 'drama', 'action', 'thriller', 'sci-fi', 'fantasy', 'romance', 'documentary']
    
    genreKeywords.forEach(genre => {
      if (text.includes(genre)) {
        tags.push(genre)
      }
    })
    
    // Add content type tags
    if (text.includes('review')) tags.push('review')
    if (text.includes('draft')) tags.push('draft')
    if (text.includes('rewatch')) tags.push('rewatch')
    if (text.includes('analysis')) tags.push('analysis')
    
    return tags.slice(0, 10)
  }

  /**
   * Extract referenced movie/TV titles
   */
  private extractReferencedTitles(text: string): string[] {
    const titles: string[] = []
    
    // Look for quoted titles
    const quotedMatches = text.match(/"([^"]+)"/g)
    if (quotedMatches) {
      quotedMatches.forEach(match => {
        const title = match.replace(/"/g, '')
        if (title.length > 2 && title.length < 100) {
          titles.push(title)
        }
      })
    }
    
    // Look for titles in single quotes
    const singleQuotedMatches = text.match(/'([^']+)'/g)
    if (singleQuotedMatches) {
      singleQuotedMatches.forEach(match => {
        const title = match.replace(/'/g, '')
        if (title.length > 2 && title.length < 100) {
          titles.push(title)
        }
      })
    }
    
    return [...new Set(titles)].slice(0, 5) // Remove duplicates and limit
  }

  /**
   * Check if episode already exists
   */
  private async checkEpisodeExists(guid: string): Promise<boolean> {
    const { data } = await this.supabase
      .from('content')
      .select('id')
      .eq('platform', 'podcast')
      .eq('platform_id', guid)
      .single()
    
    return !!data
  }

  /**
   * Get or create podcast creator
   */
  private async getOrCreatePodcastCreator(podcast: any): Promise<string | null> {
    try {
      // Check if creator exists
      const { data: existingCreator } = await this.supabase
        .from('creators')
        .select('id')
        .eq('platform', 'podcast')
        .eq('handle', podcast.name)
        .single()

      if (existingCreator) {
        return existingCreator.id
      }

      // Create new creator
      const { data: newCreator, error: createError } = await this.supabase
        .from('creators')
        .insert({
          name: podcast.name,
          handle: podcast.name,
          platform: 'podcast',
          trust_score: 9.0, // Podcasts generally have very high trust
          verified: true, // Professional podcasts are considered verified
          genres: [podcast.focus]
        })
        .select('id')
        .single()

      if (createError) {
        console.error('Error creating podcast creator:', createError)
        return null
      }

      return newCreator.id

    } catch (error) {
      console.error('Error in getOrCreatePodcastCreator:', error)
      return null
    }
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

export const podcastRSSAggregator = new PodcastRSSAggregator()

import { createClient } from 'redis'

// Redis client for caching
let redisClient: any = null

async function getRedisClient() {
  if (!redisClient) {
    try {
      redisClient = createClient({
        url: process.env.REDIS_URL || 'redis://localhost:6379'
      })
      
      redisClient.on('error', (err: Error) => {
        console.warn('Redis Client Error:', err)
        redisClient = null // Reset client on error
      })
      
      await redisClient.connect()
      console.log('✅ Redis connected for caching')
    } catch (error) {
      console.warn('⚠️ Redis connection failed, caching disabled:', error)
      redisClient = null
    }
  }
  
  return redisClient
}

export class FeedCache {
  private static TTL = {
    FEED_CONTENT: 300,      // 5 minutes for feed content
    USER_PREFERENCES: 1800, // 30 minutes for user preferences
    CREATOR_DATA: 3600,     // 1 hour for creator data
    CONTENT_STATS: 600      // 10 minutes for content stats
  }

  /**
   * Get cached feed content for a user
   */
  static async getFeedContent(userId: string, filters: any): Promise<any | null> {
    try {
      const client = await getRedisClient()
      if (!client) return null

      const cacheKey = `feed:${userId}:${JSON.stringify(filters)}`
      const cached = await client.get(cacheKey)
      
      if (cached) {
        console.log('🎯 Cache hit for feed content')
        return JSON.parse(cached)
      }
    } catch (error) {
      console.warn('Cache get error:', error)
    }
    
    return null
  }

  /**
   * Cache feed content for a user
   */
  static async setFeedContent(userId: string, filters: any, content: any): Promise<void> {
    try {
      const client = await getRedisClient()
      if (!client) return

      const cacheKey = `feed:${userId}:${JSON.stringify(filters)}`
      await client.setEx(cacheKey, this.TTL.FEED_CONTENT, JSON.stringify(content))
      console.log('💾 Feed content cached')
    } catch (error) {
      console.warn('Cache set error:', error)
    }
  }

  /**
   * Get cached user preferences
   */
  static async getUserPreferences(userId: string): Promise<any | null> {
    try {
      const client = await getRedisClient()
      if (!client) return null

      const cacheKey = `user_prefs:${userId}`
      const cached = await client.get(cacheKey)
      
      if (cached) {
        console.log('🎯 Cache hit for user preferences')
        return JSON.parse(cached)
      }
    } catch (error) {
      console.warn('Cache get error:', error)
    }
    
    return null
  }

  /**
   * Cache user preferences
   */
  static async setUserPreferences(userId: string, preferences: any): Promise<void> {
    try {
      const client = await getRedisClient()
      if (!client) return

      const cacheKey = `user_prefs:${userId}`
      await client.setEx(cacheKey, this.TTL.USER_PREFERENCES, JSON.stringify(preferences))
      console.log('💾 User preferences cached')
    } catch (error) {
      console.warn('Cache set error:', error)
    }
  }

  /**
   * Invalidate user's feed cache when new content is added
   */
  static async invalidateUserFeedCache(userId: string): Promise<void> {
    try {
      const client = await getRedisClient()
      if (!client) return

      const pattern = `feed:${userId}:*`
      const keys = await client.keys(pattern)
      
      if (keys.length > 0) {
        await client.del(keys)
        console.log(`🗑️ Invalidated ${keys.length} feed cache entries for user ${userId}`)
      }
    } catch (error) {
      console.warn('Cache invalidation error:', error)
    }
  }

  /**
   * Get cached content stats
   */
  static async getContentStats(userId: string): Promise<any | null> {
    try {
      const client = await getRedisClient()
      if (!client) return null

      const cacheKey = `content_stats:${userId}`
      const cached = await client.get(cacheKey)
      
      if (cached) {
        console.log('🎯 Cache hit for content stats')
        return JSON.parse(cached)
      }
    } catch (error) {
      console.warn('Cache get error:', error)
    }
    
    return null
  }

  /**
   * Cache content stats
   */
  static async setContentStats(userId: string, stats: any): Promise<void> {
    try {
      const client = await getRedisClient()
      if (!client) return

      const cacheKey = `content_stats:${userId}`
      await client.setEx(cacheKey, this.TTL.CONTENT_STATS, JSON.stringify(stats))
      console.log('💾 Content stats cached')
    } catch (error) {
      console.warn('Cache set error:', error)
    }
  }

  /**
   * Clear all cache (for development/testing)
   */
  static async clearAll(): Promise<void> {
    try {
      const client = await getRedisClient()
      if (!client) return

      await client.flushAll()
      console.log('🗑️ All cache cleared')
    } catch (error) {
      console.warn('Cache clear error:', error)
    }
  }
}

// Export for use in other modules
export default FeedCache

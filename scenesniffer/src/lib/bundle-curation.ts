import { createServiceRoleClient } from '@/lib/supabase-server'
import { Creator } from '@/types'

export interface CurationCriteria {
  platform: string
  theme: string
  tags: string[]
  minTrustScore: number
  minActivityDays: number
  maxCreators: number
  diversityWeight: number
}

export interface CreatorScore {
  creator: Creator
  score: number
  reasons: string[]
  metrics: {
    trustScore: number
    activityScore: number
    topicRelevance: number
    diversityBonus: number
    qualityScore: number
  }
}

export class BundleCurationEngine {
  private supabase = createServiceRoleClient()

  /**
   * Main curation function - selects top creators for a bundle theme
   */
  async curateBundle(criteria: CurationCriteria): Promise<CreatorScore[]> {
    console.log(`🤖 Starting AI curation for theme: ${criteria.theme}`)
    
    // Step 1: Get eligible creators
    const eligibleCreators = await this.getEligibleCreators(criteria)
    console.log(`📊 Found ${eligibleCreators.length} eligible creators`)
    
    if (eligibleCreators.length === 0) {
      return []
    }

    // Step 2: Score each creator
    const scoredCreators = await this.scoreCreators(eligibleCreators, criteria)
    console.log(`⚖️ Scored ${scoredCreators.length} creators`)

    // Step 3: Apply diversity constraints
    const diversifiedCreators = this.applyDiversityConstraints(scoredCreators, criteria)
    console.log(`🎯 Applied diversity constraints`)

    // Step 4: Select top N creators
    const selectedCreators = diversifiedCreators
      .sort((a, b) => b.score - a.score)
      .slice(0, criteria.maxCreators)

    console.log(`✅ Selected ${selectedCreators.length} creators for bundle`)
    return selectedCreators
  }

  /**
   * Get creators that meet basic eligibility criteria
   */
  private async getEligibleCreators(criteria: CurationCriteria): Promise<Creator[]> {
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - criteria.minActivityDays)

    // Get creators with recent activity
    const { data: creators, error } = await this.supabase
      .from('creators')
      .select(`
        *,
        content (
          id,
          published_at,
          tags,
          ai_summary
        )
      `)
      .eq('platform', criteria.platform)
      .gte('trust_score', criteria.minTrustScore)
      .order('trust_score', { ascending: false })

    if (error) {
      console.error('Error fetching creators:', error)
      return []
    }

    // Filter creators with recent activity
    const activeCreators = creators?.filter(creator => {
      const recentContent = creator.content?.filter(content => 
        new Date(content.published_at) > cutoffDate
      )
      return (recentContent?.length || 0) >= 1 // Minimum 1 post in activity period
    }) || []

    return activeCreators.map(creator => ({
      id: creator.id,
      name: creator.name,
      platform: creator.platform,
      handle: creator.handle,
      avatar_url: creator.avatar_url,
      follower_count: creator.follower_count,
      verified: creator.verified,
      trust_score: creator.trust_score,
      genres: creator.genres || [],
      created_at: creator.created_at,
      updated_at: creator.updated_at
    }))
  }

  /**
   * Score creators based on multiple factors
   */
  private async scoreCreators(creators: Creator[], criteria: CurationCriteria): Promise<CreatorScore[]> {
    const scoredCreators: CreatorScore[] = []

    for (const creator of creators) {
      const score = await this.calculateCreatorScore(creator, criteria)
      scoredCreators.push(score)
    }

    return scoredCreators
  }

  /**
   * Calculate comprehensive score for a creator
   */
  private async calculateCreatorScore(creator: Creator, criteria: CurationCriteria): Promise<CreatorScore> {
    const metrics = {
      trustScore: 0,
      activityScore: 0,
      topicRelevance: 0,
      diversityBonus: 0,
      qualityScore: 0
    }

    const reasons: string[] = []

    // 1. Trust Score (0-10 scale, weight: 25%)
    metrics.trustScore = creator.trust_score / 10
    if (creator.trust_score >= 8) {
      reasons.push('High trust score')
    }

    // 2. Activity Score (based on recent content, weight: 20%)
    metrics.activityScore = await this.calculateActivityScore(creator.id)
    if (metrics.activityScore > 0.8) {
      reasons.push('Very active creator')
    }

    // 3. Topic Relevance (based on content tags, weight: 30%)
    metrics.topicRelevance = await this.calculateTopicRelevance(creator.id, criteria.tags)
    if (metrics.topicRelevance > 0.7) {
      reasons.push(`Strong ${criteria.theme} content`)
    }

    // 4. Quality Score (based on AI summaries and engagement, weight: 20%)
    metrics.qualityScore = await this.calculateQualityScore(creator.id)
    if (metrics.qualityScore > 0.8) {
      reasons.push('High quality content')
    }

    // 5. Verification bonus (weight: 5%)
    const verificationBonus = creator.verified ? 0.1 : 0
    if (creator.verified) {
      reasons.push('Verified creator')
    }

    // Calculate final score
    const finalScore = (
      metrics.trustScore * 0.25 +
      metrics.activityScore * 0.20 +
      metrics.topicRelevance * 0.30 +
      metrics.qualityScore * 0.20 +
      verificationBonus * 0.05
    )

    return {
      creator,
      score: Math.round(finalScore * 100) / 100, // Round to 2 decimal places
      reasons,
      metrics
    }
  }

  /**
   * Calculate activity score based on recent content frequency
   */
  private async calculateActivityScore(creatorId: string): Promise<number> {
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const { data: recentContent, error } = await this.supabase
      .from('content')
      .select('id, published_at')
      .eq('creator_id', creatorId)
      .gte('published_at', thirtyDaysAgo.toISOString())

    if (error || !recentContent) {
      return 0
    }

    // Score based on content frequency (more content = higher score, up to 1.0)
    const contentCount = recentContent.length
    return Math.min(contentCount / 20, 1.0) // Max score at 20+ posts per month
  }

  /**
   * Calculate topic relevance based on content tags
   */
  private async calculateTopicRelevance(creatorId: string, targetTags: string[]): Promise<number> {
    const { data: content, error } = await this.supabase
      .from('content')
      .select('tags, ai_summary')
      .eq('creator_id', creatorId)
      .limit(20) // Check last 20 pieces of content

    if (error || !content) {
      return 0
    }

    let relevanceScore = 0
    let totalContent = content.length

    for (const item of content) {
      const contentTags = item.tags || []
      const summary = item.ai_summary || ''
      
      // Check tag overlap
      const tagMatches = targetTags.filter(tag => 
        contentTags.some(contentTag => 
          contentTag.toLowerCase().includes(tag.toLowerCase()) ||
          tag.toLowerCase().includes(contentTag.toLowerCase())
        )
      ).length

      // Check summary relevance (simple keyword matching)
      const summaryMatches = targetTags.filter(tag =>
        summary.toLowerCase().includes(tag.toLowerCase())
      ).length

      // Score this piece of content
      const itemScore = (tagMatches + summaryMatches) / targetTags.length
      relevanceScore += Math.min(itemScore, 1.0)
    }

    return totalContent > 0 ? relevanceScore / totalContent : 0
  }

  /**
   * Calculate quality score based on AI summaries and content metrics
   */
  private async calculateQualityScore(creatorId: string): Promise<number> {
    const { data: content, error } = await this.supabase
      .from('content')
      .select('ai_summary, referenced_titles')
      .eq('creator_id', creatorId)
      .limit(10) // Check last 10 pieces of content

    if (error || !content) {
      return 0.5 // Default neutral score
    }

    let qualityScore = 0
    let scoredItems = 0

    for (const item of content) {
      if (item.ai_summary) {
        scoredItems++
        
        // Score based on summary length and referenced titles
        const summaryLength = item.ai_summary.length
        const hasReferences = (item.referenced_titles?.length || 0) > 0
        
        // Quality indicators
        let itemScore = 0.5 // Base score
        
        if (summaryLength > 200) itemScore += 0.2 // Detailed content
        if (summaryLength > 500) itemScore += 0.1 // Very detailed
        if (hasReferences) itemScore += 0.2 // References movies/shows
        
        qualityScore += Math.min(itemScore, 1.0)
      }
    }

    return scoredItems > 0 ? qualityScore / scoredItems : 0.5
  }

  /**
   * Apply diversity constraints to avoid similar creators
   */
  private applyDiversityConstraints(scoredCreators: CreatorScore[], criteria: CurationCriteria): CreatorScore[] {
    if (criteria.diversityWeight <= 0) {
      return scoredCreators
    }

    const diversified: CreatorScore[] = []
    const usedHandles = new Set<string>()
    const genreDistribution = new Map<string, number>()

    // Sort by score first
    const sorted = [...scoredCreators].sort((a, b) => b.score - a.score)

    for (const scoredCreator of sorted) {
      const creator = scoredCreator.creator
      
      // Check for handle similarity (avoid very similar creators)
      const similarHandle = Array.from(usedHandles).some(handle => 
        this.calculateStringSimilarity(creator.handle, handle) > 0.7
      )

      if (similarHandle && diversified.length >= 3) {
        continue // Skip similar creators after we have some diversity
      }

      // Apply diversity bonus based on genre distribution
      const creatorGenres = creator.genres || []
      let diversityBonus = 0

      for (const genre of creatorGenres) {
        const currentCount = genreDistribution.get(genre) || 0
        if (currentCount === 0) {
          diversityBonus += 0.1 // Bonus for new genre
        }
      }

      // Update score with diversity bonus
      scoredCreator.score += diversityBonus * criteria.diversityWeight
      scoredCreator.metrics.diversityBonus = diversityBonus

      if (diversityBonus > 0) {
        scoredCreator.reasons.push('Adds genre diversity')
      }

      diversified.push(scoredCreator)
      usedHandles.add(creator.handle)

      // Update genre distribution
      for (const genre of creatorGenres) {
        genreDistribution.set(genre, (genreDistribution.get(genre) || 0) + 1)
      }
    }

    return diversified
  }

  /**
   * Calculate string similarity (simple implementation)
   */
  private calculateStringSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2
    const shorter = str1.length > str2.length ? str2 : str1
    
    if (longer.length === 0) return 1.0
    
    const editDistance = this.levenshteinDistance(longer, shorter)
    return (longer.length - editDistance) / longer.length
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = []

    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }

    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }

    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          )
        }
      }
    }

    return matrix[str2.length][str1.length]
  }
}

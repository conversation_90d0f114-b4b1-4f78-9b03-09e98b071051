import axios from 'axios'
import { TMDbMovie, TMDbTVShow } from '@/types'

const TMDB_BASE_URL = 'https://api.themoviedb.org/3'
const TMDB_IMAGE_BASE_URL = 'https://image.tmdb.org/t/p'
const API_KEY = process.env.TMDB_API_KEY

if (!API_KEY) {
  console.warn('TMDb API key not found. Movie/TV features will be disabled.')
}

export class TMDbAPI {
  private apiKey: string

  constructor(apiKey?: string) {
    this.apiKey = apiKey || API_KEY || ''
  }

  /**
   * Search for movies by title
   */
  async searchMovies(query: string, year?: number): Promise<TMDbMovie[]> {
    if (!this.apiKey) {
      throw new Error('TMDb API key is required')
    }

    try {
      const params: any = {
        api_key: this.apiKey,
        query,
        include_adult: false
      }

      if (year) {
        params.year = year
      }

      const response = await axios.get(`${TMDB_BASE_URL}/search/movie`, {
        params
      })

      return response.data.results || []
    } catch (error) {
      console.error('Error searching TMDb movies:', error)
      throw new Error('Failed to search movies')
    }
  }

  /**
   * Search for TV shows by title
   */
  async searchTVShows(query: string, year?: number): Promise<TMDbTVShow[]> {
    if (!this.apiKey) {
      throw new Error('TMDb API key is required')
    }

    try {
      const params: any = {
        api_key: this.apiKey,
        query,
        include_adult: false
      }

      if (year) {
        params.first_air_date_year = year
      }

      const response = await axios.get(`${TMDB_BASE_URL}/search/tv`, {
        params
      })

      return response.data.results || []
    } catch (error) {
      console.error('Error searching TMDb TV shows:', error)
      throw new Error('Failed to search TV shows')
    }
  }

  /**
   * Get movie details by ID
   */
  async getMovieDetails(movieId: number): Promise<TMDbMovie> {
    if (!this.apiKey) {
      throw new Error('TMDb API key is required')
    }

    try {
      const response = await axios.get(`${TMDB_BASE_URL}/movie/${movieId}`, {
        params: {
          api_key: this.apiKey,
          append_to_response: 'credits,videos,watch/providers'
        }
      })

      return response.data
    } catch (error) {
      console.error('Error fetching TMDb movie details:', error)
      throw new Error('Failed to fetch movie details')
    }
  }

  /**
   * Get TV show details by ID
   */
  async getTVShowDetails(tvId: number): Promise<TMDbTVShow> {
    if (!this.apiKey) {
      throw new Error('TMDb API key is required')
    }

    try {
      const response = await axios.get(`${TMDB_BASE_URL}/tv/${tvId}`, {
        params: {
          api_key: this.apiKey,
          append_to_response: 'credits,videos,watch/providers'
        }
      })

      return response.data
    } catch (error) {
      console.error('Error fetching TMDb TV show details:', error)
      throw new Error('Failed to fetch TV show details')
    }
  }

  /**
   * Get streaming providers for a movie
   */
  async getMovieProviders(movieId: number, region: string = 'US'): Promise<any> {
    if (!this.apiKey) {
      throw new Error('TMDb API key is required')
    }

    try {
      const response = await axios.get(`${TMDB_BASE_URL}/movie/${movieId}/watch/providers`, {
        params: {
          api_key: this.apiKey
        }
      })

      return response.data.results[region] || null
    } catch (error) {
      console.error('Error fetching movie providers:', error)
      return null
    }
  }

  /**
   * Get streaming providers for a TV show
   */
  async getTVShowProviders(tvId: number, region: string = 'US'): Promise<any> {
    if (!this.apiKey) {
      throw new Error('TMDb API key is required')
    }

    try {
      const response = await axios.get(`${TMDB_BASE_URL}/tv/${tvId}/watch/providers`, {
        params: {
          api_key: this.apiKey
        }
      })

      return response.data.results[region] || null
    } catch (error) {
      console.error('Error fetching TV show providers:', error)
      return null
    }
  }

  /**
   * Get trending movies
   */
  async getTrendingMovies(timeWindow: 'day' | 'week' = 'week'): Promise<TMDbMovie[]> {
    if (!this.apiKey) {
      throw new Error('TMDb API key is required')
    }

    try {
      const response = await axios.get(`${TMDB_BASE_URL}/trending/movie/${timeWindow}`, {
        params: {
          api_key: this.apiKey
        }
      })

      return response.data.results || []
    } catch (error) {
      console.error('Error fetching trending movies:', error)
      throw new Error('Failed to fetch trending movies')
    }
  }

  /**
   * Get trending TV shows
   */
  async getTrendingTVShows(timeWindow: 'day' | 'week' = 'week'): Promise<TMDbTVShow[]> {
    if (!this.apiKey) {
      throw new Error('TMDb API key is required')
    }

    try {
      const response = await axios.get(`${TMDB_BASE_URL}/trending/tv/${timeWindow}`, {
        params: {
          api_key: this.apiKey
        }
      })

      return response.data.results || []
    } catch (error) {
      console.error('Error fetching trending TV shows:', error)
      throw new Error('Failed to fetch trending TV shows')
    }
  }

  /**
   * Get genre list for movies
   */
  async getMovieGenres(): Promise<any[]> {
    if (!this.apiKey) {
      throw new Error('TMDb API key is required')
    }

    try {
      const response = await axios.get(`${TMDB_BASE_URL}/genre/movie/list`, {
        params: {
          api_key: this.apiKey
        }
      })

      return response.data.genres || []
    } catch (error) {
      console.error('Error fetching movie genres:', error)
      return []
    }
  }

  /**
   * Get genre list for TV shows
   */
  async getTVGenres(): Promise<any[]> {
    if (!this.apiKey) {
      throw new Error('TMDb API key is required')
    }

    try {
      const response = await axios.get(`${TMDB_BASE_URL}/genre/tv/list`, {
        params: {
          api_key: this.apiKey
        }
      })

      return response.data.genres || []
    } catch (error) {
      console.error('Error fetching TV genres:', error)
      return []
    }
  }

  /**
   * Build image URL from TMDb path
   */
  getImageUrl(path: string, size: string = 'w500'): string {
    if (!path) return ''
    return `${TMDB_IMAGE_BASE_URL}/${size}${path}`
  }

  /**
   * Search for both movies and TV shows
   */
  async searchMulti(query: string): Promise<{ movies: TMDbMovie[], tvShows: TMDbTVShow[] }> {
    const [movies, tvShows] = await Promise.all([
      this.searchMovies(query),
      this.searchTVShows(query)
    ])

    return { movies, tvShows }
  }

  /**
   * Get popular movies
   */
  async getPopularMovies(page: number = 1): Promise<TMDbMovie[]> {
    if (!this.apiKey) {
      throw new Error('TMDb API key is required')
    }

    try {
      const response = await axios.get(`${TMDB_BASE_URL}/movie/popular`, {
        params: {
          api_key: this.apiKey,
          page
        }
      })

      return response.data.results || []
    } catch (error) {
      console.error('Error fetching popular movies:', error)
      throw new Error('Failed to fetch popular movies')
    }
  }

  /**
   * Get popular TV shows
   */
  async getPopularTVShows(page: number = 1): Promise<TMDbTVShow[]> {
    if (!this.apiKey) {
      throw new Error('TMDb API key is required')
    }

    try {
      const response = await axios.get(`${TMDB_BASE_URL}/tv/popular`, {
        params: {
          api_key: this.apiKey,
          page
        }
      })

      return response.data.results || []
    } catch (error) {
      console.error('Error fetching popular TV shows:', error)
      throw new Error('Failed to fetch popular TV shows')
    }
  }

  /**
   * Get top rated movies
   */
  async getTopRatedMovies(page: number = 1): Promise<TMDbMovie[]> {
    if (!this.apiKey) {
      throw new Error('TMDb API key is required')
    }

    try {
      const response = await axios.get(`${TMDB_BASE_URL}/movie/top_rated`, {
        params: {
          api_key: this.apiKey,
          page
        }
      })

      return response.data.results || []
    } catch (error) {
      console.error('Error fetching top rated movies:', error)
      throw new Error('Failed to fetch top rated movies')
    }
  }

  /**
   * Get top rated TV shows
   */
  async getTopRatedTVShows(page: number = 1): Promise<TMDbTVShow[]> {
    if (!this.apiKey) {
      throw new Error('TMDb API key is required')
    }

    try {
      const response = await axios.get(`${TMDB_BASE_URL}/tv/top_rated`, {
        params: {
          api_key: this.apiKey,
          page
        }
      })

      return response.data.results || []
    } catch (error) {
      console.error('Error fetching top rated TV shows:', error)
      throw new Error('Failed to fetch top rated TV shows')
    }
  }

  /**
   * Discover movies by genre
   */
  async discoverMoviesByGenre(genreId: number, page: number = 1): Promise<TMDbMovie[]> {
    if (!this.apiKey) {
      throw new Error('TMDb API key is required')
    }

    try {
      const response = await axios.get(`${TMDB_BASE_URL}/discover/movie`, {
        params: {
          api_key: this.apiKey,
          with_genres: genreId,
          page,
          sort_by: 'popularity.desc'
        }
      })

      return response.data.results || []
    } catch (error) {
      console.error('Error discovering movies by genre:', error)
      throw new Error('Failed to discover movies by genre')
    }
  }

  /**
   * Discover TV shows by genre
   */
  async discoverTVShowsByGenre(genreId: number, page: number = 1): Promise<TMDbTVShow[]> {
    if (!this.apiKey) {
      throw new Error('TMDb API key is required')
    }

    try {
      const response = await axios.get(`${TMDB_BASE_URL}/discover/tv`, {
        params: {
          api_key: this.apiKey,
          with_genres: genreId,
          page,
          sort_by: 'popularity.desc'
        }
      })

      return response.data.results || []
    } catch (error) {
      console.error('Error discovering TV shows by genre:', error)
      throw new Error('Failed to discover TV shows by genre')
    }
  }

  /**
   * Get movie genres
   */
  async getMovieGenres(): Promise<{ id: number; name: string }[]> {
    if (!this.apiKey) {
      throw new Error('TMDb API key is required')
    }

    try {
      const response = await axios.get(`${TMDB_BASE_URL}/genre/movie/list`, {
        params: {
          api_key: this.apiKey
        }
      })

      return response.data.genres || []
    } catch (error) {
      console.error('Error fetching movie genres:', error)
      throw new Error('Failed to fetch movie genres')
    }
  }

  /**
   * Get TV show genres
   */
  async getTVGenres(): Promise<{ id: number; name: string }[]> {
    if (!this.apiKey) {
      throw new Error('TMDb API key is required')
    }

    try {
      const response = await axios.get(`${TMDB_BASE_URL}/genre/tv/list`, {
        params: {
          api_key: this.apiKey
        }
      })

      return response.data.genres || []
    } catch (error) {
      console.error('Error fetching TV genres:', error)
      throw new Error('Failed to fetch TV genres')
    }
  }

  /**
   * Extract year from date string
   */
  extractYear(dateString: string): number | undefined {
    if (!dateString) return undefined
    const year = parseInt(dateString.split('-')[0])
    return isNaN(year) ? undefined : year
  }
}

// Export singleton instance
export const tmdbAPI = new TMDbAPI()

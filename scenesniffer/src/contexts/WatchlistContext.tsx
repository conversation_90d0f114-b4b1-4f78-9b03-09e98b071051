'use client'

import React, { createContext, useContext, useReducer, useCallback, useEffect } from 'react'
import { useAuth } from '@/components/providers/auth-provider'

// Types
export interface WatchlistItem {
  id: string
  user_id: string
  content_type: 'movie' | 'tv'
  tmdb_id: number
  title: string
  poster_path?: string
  status: 'want_to_watch' | 'watching' | 'completed' | 'on_hold' | 'dropped'
  current_season?: number
  current_episode?: number
  progress_percentage: number
  last_watched_at?: string
  started_watching_at?: string
  user_rating?: number
  personal_notes?: string
  total_runtime?: number
  added_at: string
  updated_at: string
}

export interface WatchlistState {
  items: WatchlistItem[]
  currentlyWatching: WatchlistItem[]
  loading: boolean
  error: string | null
  lastFetch: number | null
}

export interface WatchlistContextType {
  state: WatchlistState
  actions: {
    addToWatchlist: (item: Omit<WatchlistItem, 'id' | 'user_id' | 'added_at' | 'updated_at'>) => Promise<WatchlistItem>
    removeFromWatchlist: (id: string) => Promise<void>
    updateWatchlistItem: (id: string, updates: Partial<WatchlistItem>) => Promise<WatchlistItem>
    updateProgress: (id: string, progress: { progress_percentage?: number; current_season?: number; current_episode?: number }) => Promise<WatchlistItem>
    performAction: (id: string, action: 'start_watching' | 'finish_watching' | 'next_episode' | 'next_season' | 'mark_current_episode_watched') => Promise<WatchlistItem>
    checkWatchlistStatus: (tmdbIds: number[], contentType?: string) => Promise<Record<string, any>>
    fetchWatchlist: (filters?: { status?: string; type?: string }) => Promise<void>
    fetchCurrentlyWatching: () => Promise<void>
    clearError: () => void
  }
}

// Action types
type WatchlistAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_ITEMS'; payload: WatchlistItem[] }
  | { type: 'SET_CURRENTLY_WATCHING'; payload: WatchlistItem[] }
  | { type: 'ADD_ITEM'; payload: WatchlistItem }
  | { type: 'UPDATE_ITEM'; payload: WatchlistItem }
  | { type: 'REMOVE_ITEM'; payload: string }
  | { type: 'SET_LAST_FETCH'; payload: number }

// Reducer
function watchlistReducer(state: WatchlistState, action: WatchlistAction): WatchlistState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload }
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, loading: false }
    
    case 'SET_ITEMS':
      return { ...state, items: action.payload, loading: false, lastFetch: Date.now() }
    
    case 'SET_CURRENTLY_WATCHING':
      return { ...state, currentlyWatching: action.payload }
    
    case 'ADD_ITEM':
      return { 
        ...state, 
        items: [...state.items, action.payload],
        currentlyWatching: action.payload.status === 'watching' 
          ? [...state.currentlyWatching, action.payload]
          : state.currentlyWatching
      }
    
    case 'UPDATE_ITEM':
      const updatedItems = state.items.map(item => 
        item.id === action.payload.id ? action.payload : item
      )
      const updatedCurrentlyWatching = state.currentlyWatching.map(item => 
        item.id === action.payload.id ? action.payload : item
      ).filter(item => item.status === 'watching')
      
      return { 
        ...state, 
        items: updatedItems,
        currentlyWatching: updatedCurrentlyWatching
      }
    
    case 'REMOVE_ITEM':
      return { 
        ...state, 
        items: state.items.filter(item => item.id !== action.payload),
        currentlyWatching: state.currentlyWatching.filter(item => item.id !== action.payload)
      }
    
    case 'SET_LAST_FETCH':
      return { ...state, lastFetch: action.payload }
    
    default:
      return state
  }
}

// Initial state
const initialState: WatchlistState = {
  items: [],
  currentlyWatching: [],
  loading: false,
  error: null,
  lastFetch: null
}

// Context
const WatchlistContext = createContext<WatchlistContextType | undefined>(undefined)

// Provider component
export function WatchlistProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(watchlistReducer, initialState)
  const { user } = useAuth()

  // API call helper
  const apiCall = useCallback(async (endpoint: string, options: RequestInit = {}) => {
    const response = await fetch(`/api/watchlist${endpoint}`, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'API request failed')
    }

    return response.json()
  }, [])

  // Actions
  const addToWatchlist = useCallback(async (item: Omit<WatchlistItem, 'id' | 'user_id' | 'added_at' | 'updated_at'>) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      dispatch({ type: 'SET_ERROR', payload: null })

      const response = await apiCall('', {
        method: 'POST',
        body: JSON.stringify(item),
      })

      dispatch({ type: 'ADD_ITEM', payload: response.data })
      return response.data
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to add to watchlist'
      dispatch({ type: 'SET_ERROR', payload: errorMessage })
      throw error
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }, [apiCall])

  const removeFromWatchlist = useCallback(async (id: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      dispatch({ type: 'SET_ERROR', payload: null })

      await apiCall(`/${id}`, { method: 'DELETE' })
      dispatch({ type: 'REMOVE_ITEM', payload: id })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to remove from watchlist'
      dispatch({ type: 'SET_ERROR', payload: errorMessage })
      throw error
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }, [apiCall])

  const updateWatchlistItem = useCallback(async (id: string, updates: Partial<WatchlistItem>) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      dispatch({ type: 'SET_ERROR', payload: null })

      const response = await apiCall(`/${id}`, {
        method: 'PATCH',
        body: JSON.stringify(updates),
      })

      dispatch({ type: 'UPDATE_ITEM', payload: response.data })
      return response.data
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update watchlist item'
      dispatch({ type: 'SET_ERROR', payload: errorMessage })
      throw error
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }, [apiCall])

  const updateProgress = useCallback(async (id: string, progress: { progress_percentage?: number; current_season?: number; current_episode?: number }) => {
    try {
      dispatch({ type: 'SET_ERROR', payload: null })

      const response = await apiCall(`/${id}/progress`, {
        method: 'PATCH',
        body: JSON.stringify(progress),
      })

      dispatch({ type: 'UPDATE_ITEM', payload: response.data })
      return response.data
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update progress'
      dispatch({ type: 'SET_ERROR', payload: errorMessage })
      throw error
    }
  }, [apiCall])

  const performAction = useCallback(async (id: string, action: string) => {
    try {
      dispatch({ type: 'SET_ERROR', payload: null })

      const response = await apiCall(`/${id}/progress`, {
        method: 'POST',
        body: JSON.stringify({ action }),
      })

      dispatch({ type: 'UPDATE_ITEM', payload: response.data })
      return response.data
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to perform action'
      dispatch({ type: 'SET_ERROR', payload: errorMessage })
      throw error
    }
  }, [apiCall])

  const checkWatchlistStatus = useCallback(async (tmdbIds: number[], contentType?: string) => {
    try {
      const params = new URLSearchParams({
        tmdb_ids: tmdbIds.join(','),
        ...(contentType && { content_type: contentType })
      })

      const response = await apiCall(`/check?${params}`)
      return response.data
    } catch (error) {
      console.error('Failed to check watchlist status:', error)
      return {}
    }
  }, [apiCall])

  const fetchWatchlist = useCallback(async (filters?: { status?: string; type?: string }) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true })
      dispatch({ type: 'SET_ERROR', payload: null })

      const params = new URLSearchParams()
      if (filters?.status) params.append('status', filters.status)
      if (filters?.type) params.append('type', filters.type)

      const response = await apiCall(`?${params}`)
      dispatch({ type: 'SET_ITEMS', payload: response.data })
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch watchlist'
      dispatch({ type: 'SET_ERROR', payload: errorMessage })
    }
  }, [apiCall])

  const fetchCurrentlyWatching = useCallback(async () => {
    try {
      const response = await apiCall('/currently-watching?metadata=true')
      dispatch({ type: 'SET_CURRENTLY_WATCHING', payload: response.data })
    } catch (error) {
      console.error('Failed to fetch currently watching:', error)
    }
  }, [apiCall])

  const clearError = useCallback(() => {
    dispatch({ type: 'SET_ERROR', payload: null })
  }, [])

  // Don't auto-fetch - let components decide when to fetch
  // useEffect(() => {
  //   if (user) {
  //     fetchCurrentlyWatching()
  //   }
  // }, [user])

  const contextValue: WatchlistContextType = {
    state,
    actions: {
      addToWatchlist,
      removeFromWatchlist,
      updateWatchlistItem,
      updateProgress,
      performAction,
      checkWatchlistStatus,
      fetchWatchlist,
      fetchCurrentlyWatching,
      clearError,
    },
  }

  return (
    <WatchlistContext.Provider value={contextValue}>
      {children}
    </WatchlistContext.Provider>
  )
}

// Hook to use the watchlist context
export function useWatchlist() {
  const context = useContext(WatchlistContext)
  if (context === undefined) {
    throw new Error('useWatchlist must be used within a WatchlistProvider')
  }
  return context
}

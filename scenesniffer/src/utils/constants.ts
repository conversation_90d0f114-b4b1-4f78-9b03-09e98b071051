// Genre options for onboarding
export const GENRES = [
  'Action',
  'Adventure',
  'Animation',
  'Comedy',
  'Crime',
  'Documentary',
  'Drama',
  'Family',
  'Fantasy',
  'History',
  'Horror',
  'Music',
  'Mystery',
  'Romance',
  'Science Fiction',
  'Thriller',
  'War',
  'Western',
  'K-Drama',
  'Anime',
  'True Crime',
  'Superhero',
  'Psychological Thriller',
  'Dark Comedy',
  'Indie',
  'Foreign',
  'Biographical',
  'Sports'
] as const

// Streaming service options
export const STREAMING_SERVICES = [
  { id: 'netflix', name: 'Netflix', logo: '/logos/netflix.png' },
  { id: 'amazon-prime', name: 'Amazon Prime Video', logo: '/logos/prime.png' },
  { id: 'disney-plus', name: 'Disney+', logo: '/logos/disney.png' },
  { id: 'hulu', name: '<PERSON><PERSON>', logo: '/logos/hulu.png' },
  { id: 'hbo-max', name: '<PERSON> (HBO)', logo: '/logos/hbo.png' },
  { id: 'apple-tv', name: 'Apple TV+', logo: '/logos/apple.png' },
  { id: 'paramount-plus', name: 'Paramount+', logo: '/logos/paramount.png' },
  { id: 'peacock', name: 'Peacock', logo: '/logos/peacock.png' },
  { id: 'crunchyroll', name: 'Crunchyroll', logo: '/logos/crunchyroll.png' },
  { id: 'funimation', name: 'Funimation', logo: '/logos/funimation.png' },
  { id: 'youtube-tv', name: 'YouTube TV', logo: '/logos/youtube-tv.png' },
  { id: 'starz', name: 'Starz', logo: '/logos/starz.png' },
  { id: 'showtime', name: 'Showtime', logo: '/logos/showtime.png' }
] as const

// Platform options for creators
export const PLATFORMS = [
  { id: 'youtube', name: 'YouTube', icon: '📺' },
  { id: 'instagram', name: 'Instagram', icon: '📷' },
  { id: 'twitter', name: 'Twitter/X', icon: '🐦' },
  { id: 'tiktok', name: 'TikTok', icon: '🎵' }
] as const

// Bundle theme options
export const BUNDLE_THEMES = [
  'Horror Masters',
  'Sci-Fi Visionaries',
  'Comedy Central',
  'Action Heroes',
  'Drama Queens',
  'Indie Darlings',
  'Animation Station',
  'Documentary Deep Dive',
  'Thriller Zone',
  'Fantasy Realm',
  'Romance Corner',
  'Mystery Solvers',
  'Adventure Seekers',
  'Film Critics',
  'Behind the Scenes',
  'Movie Reviews',
  'Pop Culture',
  'Cinematic Analysis',
  'Film History',
  'International Cinema',
  'Streaming Experts',
  'Genre Specialists',
  'Rising Stars',
  'Veteran Voices'
] as const

// Content type classifications
export const CONTENT_TYPES = [
  { id: 'review', name: 'Review', color: 'bg-blue-100 text-blue-800' },
  { id: 'theory', name: 'Theory', color: 'bg-purple-100 text-purple-800' },
  { id: 'news', name: 'News', color: 'bg-green-100 text-green-800' },
  { id: 'spoiler-free', name: 'Spoiler-Free', color: 'bg-yellow-100 text-yellow-800' },
  { id: 'breakdown', name: 'Breakdown', color: 'bg-red-100 text-red-800' },
  { id: 'recommendation', name: 'Recommendation', color: 'bg-indigo-100 text-indigo-800' }
] as const

// TMDb configuration
export const TMDB_BASE_URL = 'https://api.themoviedb.org/3'
export const TMDB_IMAGE_BASE_URL = 'https://image.tmdb.org/t/p'

// YouTube API configuration
export const YOUTUBE_API_BASE_URL = 'https://www.googleapis.com/youtube/v3'

// App configuration
export const APP_NAME = 'SceneSniffer'
export const APP_DESCRIPTION = 'Your personalized movie and TV content intelligence platform'

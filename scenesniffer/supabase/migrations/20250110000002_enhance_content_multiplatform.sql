-- Enhance content table for multi-platform support and detailed content display
-- This migration adds fields needed for Instagram, Twitter, and enhanced content details

-- Add platform field to content table
ALTER TABLE public.content 
ADD COLUMN IF NOT EXISTS platform VARCHAR(20) NOT NULL DEFAULT 'youtube';

-- Add multi-platform specific fields
ALTER TABLE public.content 
ADD COLUMN IF NOT EXISTS media_type VARCHAR(20), -- video, image, carousel, text, thread
ADD COLUMN IF NOT EXISTS media_urls JSONB DEFAULT '[]', -- Array of media URLs for carousels/threads
ADD COLUMN IF NOT EXISTS duration INTEGER, -- Duration in seconds (for videos)
ADD COLUMN IF NOT EXISTS engagement_metrics JSONB DEFAULT '{}', -- Platform-specific engagement data
ADD COLUMN IF NOT EXISTS content_metadata JSONB DEFAULT '{}'; -- Platform-specific metadata

-- Add AI-enhanced fields for better content details
ALTER TABLE public.content 
ADD COLUMN IF NOT EXISTS ai_extracted_text TEXT, -- Text extracted from images/videos
ADD COLUMN IF NOT EXISTS ai_content_quality_score DECIMAL(3,2) DEFAULT 0.0, -- 0.0 to 1.0
ADD COLUMN IF NOT EXISTS ai_topics JSONB DEFAULT '[]'; -- AI-detected topics and entities

-- Update the unique constraint to include platform
ALTER TABLE public.content DROP CONSTRAINT IF EXISTS content_creator_id_platform_id_key;
ALTER TABLE public.content ADD CONSTRAINT content_creator_platform_unique 
UNIQUE(creator_id, platform_id, platform);

-- Add check constraint for platform values
ALTER TABLE public.content ADD CONSTRAINT content_platform_check
CHECK (platform IN ('youtube', 'instagram', 'twitter', 'reddit', 'podcast', 'mastodon'));

-- Add check constraint for media_type values
ALTER TABLE public.content ADD CONSTRAINT content_media_type_check
CHECK (media_type IN ('video', 'image', 'carousel', 'text', 'thread', 'reel', 'story', 'audio'));

-- Update creators table to support new platforms
ALTER TABLE public.creators DROP CONSTRAINT IF EXISTS creators_platform_check;
ALTER TABLE public.creators ADD CONSTRAINT creators_platform_check
CHECK (platform IN ('youtube', 'instagram', 'twitter', 'tiktok', 'reddit', 'podcast', 'mastodon'));

-- Create indexes for multi-platform queries
CREATE INDEX IF NOT EXISTS idx_content_platform ON public.content(platform);
CREATE INDEX IF NOT EXISTS idx_content_media_type ON public.content(media_type);
CREATE INDEX IF NOT EXISTS idx_content_platform_published ON public.content(platform, published_at DESC);
CREATE INDEX IF NOT EXISTS idx_content_quality_score ON public.content(ai_content_quality_score DESC);

-- Create GIN indexes for JSONB fields
CREATE INDEX IF NOT EXISTS idx_content_engagement_metrics_gin ON public.content USING GIN (engagement_metrics);
CREATE INDEX IF NOT EXISTS idx_content_metadata_gin ON public.content USING GIN (content_metadata);
CREATE INDEX IF NOT EXISTS idx_content_ai_topics_gin ON public.content USING GIN (ai_topics);

-- Create content details view for unified display
CREATE OR REPLACE VIEW content_details_view AS
SELECT 
    c.id,
    c.title,
    c.description,
    c.content_type,
    c.platform,
    c.platform_url,
    c.platform_id,
    c.thumbnail_url,
    c.published_at,
    c.ai_summary,
    c.referenced_titles,
    c.tags,
    c.media_type,
    c.media_urls,
    c.duration,
    c.engagement_metrics,
    c.content_metadata,
    c.ai_extracted_text,
    c.ai_content_quality_score,
    c.ai_topics,
    
    -- Creator information
    cr.name as creator_name,
    cr.platform as creator_platform,
    cr.handle as creator_handle,
    cr.avatar_url as creator_avatar,
    cr.verified as creator_verified,
    cr.trust_score as creator_trust_score,
    
    -- Computed fields for display
    CASE 
        WHEN c.platform = 'youtube' THEN 'YouTube Video'
        WHEN c.platform = 'instagram' AND c.media_type = 'reel' THEN 'Instagram Reel'
        WHEN c.platform = 'instagram' AND c.media_type = 'carousel' THEN 'Instagram Carousel'
        WHEN c.platform = 'instagram' AND c.media_type = 'image' THEN 'Instagram Post'
        WHEN c.platform = 'twitter' AND c.media_type = 'thread' THEN 'Twitter Thread'
        WHEN c.platform = 'twitter' THEN 'Tweet'
        ELSE 'Content'
    END as display_type,
    
    -- Engagement summary
    CASE 
        WHEN c.platform = 'youtube' THEN 
            COALESCE((c.engagement_metrics->>'views')::text, '0') || ' views'
        WHEN c.platform = 'instagram' THEN 
            COALESCE((c.engagement_metrics->>'likes')::text, '0') || ' likes'
        WHEN c.platform = 'twitter' THEN 
            COALESCE((c.engagement_metrics->>'retweets')::text, '0') || ' retweets'
        ELSE 'No engagement data'
    END as engagement_summary,
    
    -- Duration display
    CASE 
        WHEN c.duration IS NOT NULL AND c.duration > 0 THEN
            CASE 
                WHEN c.duration >= 3600 THEN 
                    (c.duration / 3600)::text || 'h ' || ((c.duration % 3600) / 60)::text || 'm'
                WHEN c.duration >= 60 THEN 
                    (c.duration / 60)::text || 'm ' || (c.duration % 60)::text || 's'
                ELSE 
                    c.duration::text || 's'
            END
        ELSE NULL
    END as duration_display

FROM public.content c
JOIN public.creators cr ON c.creator_id = cr.id;

-- Create function to get platform-specific content details
CREATE OR REPLACE FUNCTION get_content_details(content_id UUID)
RETURNS TABLE (
    id UUID,
    title TEXT,
    description TEXT,
    content_type VARCHAR(50),
    platform VARCHAR(20),
    platform_url TEXT,
    platform_id VARCHAR(255),
    thumbnail_url TEXT,
    published_at TIMESTAMP WITH TIME ZONE,
    ai_summary TEXT,
    referenced_titles TEXT[],
    tags TEXT[],
    media_type VARCHAR(20),
    media_urls JSONB,
    duration INTEGER,
    engagement_metrics JSONB,
    content_metadata JSONB,
    ai_extracted_text TEXT,
    ai_content_quality_score DECIMAL(3,2),
    ai_topics JSONB,
    creator_name VARCHAR(255),
    creator_handle VARCHAR(255),
    creator_avatar TEXT,
    creator_verified BOOLEAN,
    creator_trust_score DECIMAL(3,2),
    display_type TEXT,
    engagement_summary TEXT,
    duration_display TEXT,
    platform_specific_data JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cdv.*,
        -- Platform-specific data compilation
        CASE 
            WHEN cdv.platform = 'youtube' THEN
                jsonb_build_object(
                    'video_id', cdv.platform_id,
                    'embed_url', 'https://www.youtube.com/embed/' || cdv.platform_id,
                    'watch_url', cdv.platform_url,
                    'channel_url', 'https://www.youtube.com/channel/' || (cdv.content_metadata->>'channel_id'),
                    'category', cdv.content_metadata->>'category',
                    'language', cdv.content_metadata->>'language'
                )
            WHEN cdv.platform = 'instagram' THEN
                jsonb_build_object(
                    'post_id', cdv.platform_id,
                    'post_url', cdv.platform_url,
                    'media_type', cdv.media_type,
                    'media_count', jsonb_array_length(cdv.media_urls),
                    'profile_url', 'https://instagram.com/' || cdv.creator_handle,
                    'hashtags', cdv.content_metadata->>'hashtags'
                )
            WHEN cdv.platform = 'twitter' THEN
                jsonb_build_object(
                    'tweet_id', cdv.platform_id,
                    'tweet_url', cdv.platform_url,
                    'is_thread', cdv.media_type = 'thread',
                    'thread_length', CASE WHEN cdv.media_type = 'thread' THEN jsonb_array_length(cdv.media_urls) ELSE 1 END,
                    'profile_url', 'https://twitter.com/' || cdv.creator_handle,
                    'mentions', cdv.content_metadata->>'mentions'
                )
            ELSE '{}'::jsonb
        END as platform_specific_data
    FROM content_details_view cdv
    WHERE cdv.id = content_id;
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON COLUMN public.content.platform IS 'Content platform: youtube, instagram, twitter';
COMMENT ON COLUMN public.content.media_type IS 'Type of media: video, image, carousel, text, thread, reel, story';
COMMENT ON COLUMN public.content.media_urls IS 'Array of media URLs for multi-media content (carousels, threads)';
COMMENT ON COLUMN public.content.duration IS 'Content duration in seconds (for videos/reels)';
COMMENT ON COLUMN public.content.engagement_metrics IS 'Platform-specific engagement data (views, likes, retweets, etc.)';
COMMENT ON COLUMN public.content.content_metadata IS 'Platform-specific metadata and additional information';
COMMENT ON COLUMN public.content.ai_extracted_text IS 'Text extracted from images/videos using AI';
COMMENT ON COLUMN public.content.ai_content_quality_score IS 'AI-assessed content quality score (0.0 to 1.0)';
COMMENT ON COLUMN public.content.ai_topics IS 'AI-detected topics, entities, and themes';
COMMENT ON VIEW content_details_view IS 'Unified view for displaying content details across all platforms';
COMMENT ON FUNCTION get_content_details IS 'Function to retrieve comprehensive content details with platform-specific formatting';

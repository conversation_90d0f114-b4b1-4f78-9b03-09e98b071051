-- Enhanced Watchlist Migration
-- Adds currently watching functionality and progress tracking

-- First, let's add the new columns to the existing watchlist table
ALTER TABLE public.watchlist 
ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'want_to_watch' 
CHECK (status IN ('want_to_watch', 'watching', 'completed', 'on_hold', 'dropped'));

ALTER TABLE public.watchlist 
ADD COLUMN IF NOT EXISTS current_season INTEGER DEFAULT NULL;

ALTER TABLE public.watchlist 
ADD COLUMN IF NOT EXISTS current_episode INTEGER DEFAULT NULL;

ALTER TABLE public.watchlist 
ADD COLUMN IF NOT EXISTS progress_percentage DECIMAL(5,2) DEFAULT 0.0 
CHECK (progress_percentage >= 0 AND progress_percentage <= 100);

ALTER TABLE public.watchlist 
ADD COLUMN IF NOT EXISTS last_watched_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;

ALTER TABLE public.watchlist 
ADD COLUMN IF NOT EXISTS started_watching_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;

ALTER TABLE public.watchlist 
ADD COLUMN IF NOT EXISTS user_rating INTEGER DEFAULT NULL 
CHECK (user_rating >= 1 AND user_rating <= 10);

ALTER TABLE public.watchlist 
ADD COLUMN IF NOT EXISTS personal_notes TEXT DEFAULT NULL;

ALTER TABLE public.watchlist 
ADD COLUMN IF NOT EXISTS total_runtime INTEGER DEFAULT NULL; -- in minutes

ALTER TABLE public.watchlist 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Update the existing watched column logic
-- If watched is true, set status to completed and progress to 100%
UPDATE public.watchlist 
SET 
  status = 'completed',
  progress_percentage = 100.0,
  started_watching_at = COALESCE(watched_at, added_at),
  last_watched_at = COALESCE(watched_at, added_at)
WHERE watched = true;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_watchlist_status ON public.watchlist(status);
CREATE INDEX IF NOT EXISTS idx_watchlist_last_watched ON public.watchlist(last_watched_at DESC);
CREATE INDEX IF NOT EXISTS idx_watchlist_user_status ON public.watchlist(user_id, status);

-- Create trigger to automatically update updated_at
CREATE OR REPLACE FUNCTION update_watchlist_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    
    -- Auto-set started_watching_at when status changes to 'watching'
    IF OLD.status != 'watching' AND NEW.status = 'watching' AND NEW.started_watching_at IS NULL THEN
        NEW.started_watching_at = NOW();
    END IF;
    
    -- Auto-set last_watched_at when progress is updated
    IF OLD.progress_percentage != NEW.progress_percentage OR 
       OLD.current_episode != NEW.current_episode OR 
       OLD.current_season != NEW.current_season THEN
        NEW.last_watched_at = NOW();
    END IF;
    
    -- Auto-complete when progress reaches 100%
    IF NEW.progress_percentage >= 100.0 AND NEW.status != 'completed' THEN
        NEW.status = 'completed';
        NEW.last_watched_at = NOW();
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply the trigger
DROP TRIGGER IF EXISTS update_watchlist_updated_at_trigger ON public.watchlist;
CREATE TRIGGER update_watchlist_updated_at_trigger
    BEFORE UPDATE ON public.watchlist
    FOR EACH ROW EXECUTE FUNCTION update_watchlist_updated_at();

-- Update RLS policies for the enhanced watchlist
DROP POLICY IF EXISTS "Users can manage their own watchlist" ON public.watchlist;
CREATE POLICY "Users can manage their own watchlist" ON public.watchlist
    FOR ALL USING (auth.uid() = user_id);

-- Create a view for currently watching items (for easy querying)
CREATE OR REPLACE VIEW public.currently_watching AS
SELECT 
    w.*,
    CASE 
        WHEN w.content_type = 'movie' THEN m.title
        WHEN w.content_type = 'tv' THEN tv.name
        ELSE w.title
    END as display_title,
    CASE 
        WHEN w.content_type = 'movie' THEN m.poster_path
        WHEN w.content_type = 'tv' THEN tv.poster_path
        ELSE w.poster_path
    END as display_poster,
    CASE 
        WHEN w.content_type = 'movie' THEN m.runtime
        WHEN w.content_type = 'tv' THEN tv.episode_run_time[1]
        ELSE w.total_runtime
    END as runtime_minutes,
    CASE 
        WHEN w.content_type = 'tv' THEN tv.number_of_seasons
        ELSE NULL
    END as total_seasons,
    CASE 
        WHEN w.content_type = 'tv' THEN tv.number_of_episodes
        ELSE NULL
    END as total_episodes
FROM public.watchlist w
LEFT JOIN public.movies m ON w.content_type = 'movie' AND w.tmdb_id = m.tmdb_id
LEFT JOIN public.tv_shows tv ON w.content_type = 'tv' AND w.tmdb_id = tv.tmdb_id
WHERE w.status = 'watching'
ORDER BY w.last_watched_at DESC NULLS LAST, w.started_watching_at DESC;

-- Grant access to the view
GRANT SELECT ON public.currently_watching TO authenticated;

-- Add comments for documentation
COMMENT ON COLUMN public.watchlist.status IS 'Current viewing status: want_to_watch, watching, completed, on_hold, dropped';
COMMENT ON COLUMN public.watchlist.current_season IS 'Current season for TV shows (1-based)';
COMMENT ON COLUMN public.watchlist.current_episode IS 'Current episode within season for TV shows (1-based)';
COMMENT ON COLUMN public.watchlist.progress_percentage IS 'Viewing progress as percentage (0-100)';
COMMENT ON COLUMN public.watchlist.last_watched_at IS 'Timestamp of last viewing activity';
COMMENT ON COLUMN public.watchlist.started_watching_at IS 'Timestamp when user started watching';
COMMENT ON COLUMN public.watchlist.user_rating IS 'User rating from 1-10';
COMMENT ON COLUMN public.watchlist.personal_notes IS 'User notes about the content';
COMMENT ON COLUMN public.watchlist.total_runtime IS 'Cached runtime in minutes for progress calculation';

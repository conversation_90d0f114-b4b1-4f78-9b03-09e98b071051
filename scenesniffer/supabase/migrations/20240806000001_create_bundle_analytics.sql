-- Bundle Analytics and Performance Tracking Tables
-- Migration for Phase 6: Enhanced Bundle Curation & AI Discovery

-- Bundle Performance Metrics table
CREATE TABLE IF NOT EXISTS public.bundle_performance_metrics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    bundle_id UUID REFERENCES public.creator_bundles(id) ON DELETE CASCADE NOT NULL,
    
    -- Core performance metrics (0.0 to 10.0 scale)
    engagement_score DECIMAL(5,2) DEFAULT 0.0 CHECK (engagement_score >= 0 AND engagement_score <= 10),
    creator_retention DECIMAL(5,2) DEFAULT 0.0 CHECK (creator_retention >= 0 AND creator_retention <= 10),
    content_freshness DECIMAL(5,2) DEFAULT 0.0 CHECK (content_freshness >= 0 AND content_freshness <= 10),
    user_satisfaction DECIMAL(5,2) DEFAULT 0.0 CHECK (user_satisfaction >= 0 AND user_satisfaction <= 10),
    discovery_rate DECIMAL(5,2) DEFAULT 0.0 CHECK (discovery_rate >= 0 AND discovery_rate <= 10),
    
    -- Raw metrics for calculation
    total_views INTEGER DEFAULT 0,
    total_interactions INTEGER DEFAULT 0,
    unique_users INTEGER DEFAULT 0,
    creators_added INTEGER DEFAULT 0,
    creators_removed INTEGER DEFAULT 0,
    user_ratings_count INTEGER DEFAULT 0,
    user_ratings_sum INTEGER DEFAULT 0,
    
    -- Metadata
    measurement_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    measurement_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    measured_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trending Topics table
CREATE TABLE IF NOT EXISTS public.trending_topics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    topic_name VARCHAR(255) NOT NULL,
    topic_keywords TEXT[] DEFAULT '{}',
    trend_score DECIMAL(5,2) NOT NULL CHECK (trend_score >= 0 AND trend_score <= 10),
    source VARCHAR(50) NOT NULL CHECK (source IN ('social', 'news', 'platform', 'ai_analysis', 'user_behavior')),
    source_data JSONB DEFAULT '{}',
    
    -- Lifecycle
    detected_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'processed')),
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bundle Lifecycle Events table
CREATE TABLE IF NOT EXISTS public.bundle_lifecycle_events (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    bundle_id UUID REFERENCES public.creator_bundles(id) ON DELETE CASCADE NOT NULL,
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN ('created', 'refreshed', 'optimized', 'retired', 'reactivated', 'quality_check')),
    event_data JSONB DEFAULT '{}',
    
    -- Performance context
    performance_score DECIMAL(5,2),
    trigger_reason VARCHAR(255),
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by VARCHAR(50) DEFAULT 'system' -- 'system', 'admin', 'ai'
);

-- Bundle User Interactions table (for detailed analytics)
CREATE TABLE IF NOT EXISTS public.bundle_user_interactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    bundle_id UUID REFERENCES public.creator_bundles(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    interaction_type VARCHAR(50) NOT NULL CHECK (interaction_type IN ('view', 'add', 'remove', 'rate', 'share', 'creator_click')),
    interaction_data JSONB DEFAULT '{}',
    
    -- Context
    session_id VARCHAR(255),
    user_agent TEXT,
    referrer TEXT,
    
    -- Metadata
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bundle_performance_metrics_bundle_id ON public.bundle_performance_metrics(bundle_id);
CREATE INDEX IF NOT EXISTS idx_bundle_performance_metrics_measured_at ON public.bundle_performance_metrics(measured_at DESC);
CREATE INDEX IF NOT EXISTS idx_bundle_performance_metrics_engagement ON public.bundle_performance_metrics(engagement_score DESC);

CREATE INDEX IF NOT EXISTS idx_trending_topics_status ON public.trending_topics(status);
CREATE INDEX IF NOT EXISTS idx_trending_topics_trend_score ON public.trending_topics(trend_score DESC);
CREATE INDEX IF NOT EXISTS idx_trending_topics_detected_at ON public.trending_topics(detected_at DESC);
CREATE INDEX IF NOT EXISTS idx_trending_topics_expires_at ON public.trending_topics(expires_at);

CREATE INDEX IF NOT EXISTS idx_bundle_lifecycle_events_bundle_id ON public.bundle_lifecycle_events(bundle_id);
CREATE INDEX IF NOT EXISTS idx_bundle_lifecycle_events_event_type ON public.bundle_lifecycle_events(event_type);
CREATE INDEX IF NOT EXISTS idx_bundle_lifecycle_events_created_at ON public.bundle_lifecycle_events(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_bundle_user_interactions_bundle_id ON public.bundle_user_interactions(bundle_id);
CREATE INDEX IF NOT EXISTS idx_bundle_user_interactions_user_id ON public.bundle_user_interactions(user_id);
CREATE INDEX IF NOT EXISTS idx_bundle_user_interactions_type ON public.bundle_user_interactions(interaction_type);
CREATE INDEX IF NOT EXISTS idx_bundle_user_interactions_created_at ON public.bundle_user_interactions(created_at DESC);

-- Update triggers
CREATE TRIGGER update_trending_topics_updated_at 
  BEFORE UPDATE ON public.trending_topics 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- RLS Policies
ALTER TABLE public.bundle_performance_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.trending_topics ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bundle_lifecycle_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bundle_user_interactions ENABLE ROW LEVEL SECURITY;

-- Bundle Performance Metrics Policies
DROP POLICY IF EXISTS "Anyone can view bundle performance metrics" ON public.bundle_performance_metrics;
CREATE POLICY "Anyone can view bundle performance metrics" ON public.bundle_performance_metrics FOR SELECT USING (true);

DROP POLICY IF EXISTS "System can manage bundle performance metrics" ON public.bundle_performance_metrics;
CREATE POLICY "System can manage bundle performance metrics" ON public.bundle_performance_metrics 
  FOR ALL USING (auth.role() = 'service_role');

-- Trending Topics Policies
DROP POLICY IF EXISTS "Anyone can view trending topics" ON public.trending_topics;
CREATE POLICY "Anyone can view trending topics" ON public.trending_topics FOR SELECT USING (true);

DROP POLICY IF EXISTS "System can manage trending topics" ON public.trending_topics;
CREATE POLICY "System can manage trending topics" ON public.trending_topics 
  FOR ALL USING (auth.role() = 'service_role');

-- Bundle Lifecycle Events Policies
DROP POLICY IF EXISTS "Anyone can view bundle lifecycle events" ON public.bundle_lifecycle_events;
CREATE POLICY "Anyone can view bundle lifecycle events" ON public.bundle_lifecycle_events FOR SELECT USING (true);

DROP POLICY IF EXISTS "System can manage bundle lifecycle events" ON public.bundle_lifecycle_events;
CREATE POLICY "System can manage bundle lifecycle events" ON public.bundle_lifecycle_events 
  FOR ALL USING (auth.role() = 'service_role');

-- Bundle User Interactions Policies
DROP POLICY IF EXISTS "Users can view own interactions" ON public.bundle_user_interactions;
CREATE POLICY "Users can view own interactions" ON public.bundle_user_interactions 
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert own interactions" ON public.bundle_user_interactions;
CREATE POLICY "Users can insert own interactions" ON public.bundle_user_interactions 
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "System can manage all interactions" ON public.bundle_user_interactions;
CREATE POLICY "System can manage all interactions" ON public.bundle_user_interactions 
  FOR ALL USING (auth.role() = 'service_role');

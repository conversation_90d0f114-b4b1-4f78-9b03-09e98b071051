-- Creator Bundles table
CREATE TABLE IF NOT EXISTS public.creator_bundles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    tags TEXT[] DEFAULT '{}',
    themes TEXT[] DEFAULT '{}',
    platform VARCHAR(50) NOT NULL CHECK (platform IN ('youtube', 'instagram', 'twitter', 'tiktok', 'twitch')),
    creator_count INTEGER DEFAULT 0,
    refreshed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ttl INTEGER DEFAULT 604800,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'draft')),
    pinned_creator_ids UUID[] DEFAULT '{}',
    suppressed_creator_ids UUID[] DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bundle Creators relationship table
CREATE TABLE IF NOT EXISTS public.bundle_creators (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    bundle_id UUID REFERENCES public.creator_bundles(id) ON DELETE CASCADE NOT NULL,
    creator_id UUID REFERENCES public.creators(id) ON DELETE CASCADE NOT NULL,
    position INTEGER DEFAULT 0,
    score DECIMAL(5,2) DEFAULT 0.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(bundle_id, creator_id)
);

-- User Bundle subscriptions table
CREATE TABLE IF NOT EXISTS public.user_bundles (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE NOT NULL,
    bundle_id UUID REFERENCES public.creator_bundles(id) ON DELETE CASCADE NOT NULL,
    added_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    included_creator_ids UUID[] DEFAULT '{}',
    UNIQUE(user_id, bundle_id)
);

-- Bundle Analytics table
CREATE TABLE IF NOT EXISTS public.bundle_analytics (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    bundle_id UUID REFERENCES public.creator_bundles(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN ('impression', 'view', 'add', 'remove')),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX IF NOT EXISTS idx_creator_bundles_platform ON public.creator_bundles(platform);
CREATE INDEX IF NOT EXISTS idx_creator_bundles_status ON public.creator_bundles(status);
CREATE INDEX IF NOT EXISTS idx_creator_bundles_refreshed_at ON public.creator_bundles(refreshed_at DESC);
CREATE INDEX IF NOT EXISTS idx_bundle_creators_bundle_id ON public.bundle_creators(bundle_id);
CREATE INDEX IF NOT EXISTS idx_bundle_creators_creator_id ON public.bundle_creators(creator_id);
CREATE INDEX IF NOT EXISTS idx_bundle_creators_position ON public.bundle_creators(bundle_id, position);
CREATE INDEX IF NOT EXISTS idx_user_bundles_user_id ON public.user_bundles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_bundles_bundle_id ON public.user_bundles(bundle_id);
CREATE INDEX IF NOT EXISTS idx_bundle_analytics_bundle_id ON public.bundle_analytics(bundle_id);
CREATE INDEX IF NOT EXISTS idx_bundle_analytics_event_type ON public.bundle_analytics(event_type);
CREATE INDEX IF NOT EXISTS idx_bundle_analytics_created_at ON public.bundle_analytics(created_at DESC);

-- RLS Policies
ALTER TABLE public.creator_bundles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bundle_creators ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_bundles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bundle_analytics ENABLE ROW LEVEL SECURITY;

-- Public read access for bundles and bundle_creators
DROP POLICY IF EXISTS "Anyone can view creator_bundles" ON public.creator_bundles;
CREATE POLICY "Anyone can view creator_bundles" ON public.creator_bundles FOR SELECT USING (true);

DROP POLICY IF EXISTS "Anyone can view bundle_creators" ON public.bundle_creators;
CREATE POLICY "Anyone can view bundle_creators" ON public.bundle_creators FOR SELECT USING (true);

-- User-specific policies for user_bundles
DROP POLICY IF EXISTS "Users can view own bundles" ON public.user_bundles;
CREATE POLICY "Users can view own bundles" ON public.user_bundles FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert own bundles" ON public.user_bundles;
CREATE POLICY "Users can insert own bundles" ON public.user_bundles FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete own bundles" ON public.user_bundles;
CREATE POLICY "Users can delete own bundles" ON public.user_bundles FOR DELETE USING (auth.uid() = user_id);

-- Analytics policies
DROP POLICY IF EXISTS "Users can insert own analytics" ON public.bundle_analytics;
CREATE POLICY "Users can insert own analytics" ON public.bundle_analytics FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can view own analytics" ON public.bundle_analytics;
CREATE POLICY "Users can view own analytics" ON public.bundle_analytics FOR SELECT USING (auth.uid() = user_id);

-- Update trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers
DROP TRIGGER IF EXISTS update_creator_bundles_updated_at ON public.creator_bundles;
CREATE TRIGGER update_creator_bundles_updated_at 
  BEFORE UPDATE ON public.creator_bundles 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Database optimization indexes for Cinematic Pulse Feed
-- This migration adds indexes to improve feed query performance

-- Index for creator-based content filtering (most common query pattern)
CREATE INDEX IF NOT EXISTS idx_content_creator_published 
ON content (creator_id, published_at DESC);

-- Index for content type filtering
CREATE INDEX IF NOT EXISTS idx_content_type 
ON content (content_type);

-- Composite index for creator + content type filtering
CREATE INDEX IF NOT EXISTS idx_content_creator_type_published 
ON content (creator_id, content_type, published_at DESC);

-- Index for platform_id to prevent duplicates efficiently
CREATE INDEX IF NOT EXISTS idx_content_platform_creator 
ON content (platform_id, creator_id);

-- GIN index for tags array searching (for genre filtering)
CREATE INDEX IF NOT EXISTS idx_content_tags_gin 
ON content USING GIN (tags);

-- Index for AI summary existence (for relevance scoring)
CREATE INDEX IF NOT EXISTS idx_content_ai_summary 
ON content (creator_id, published_at DESC) 
WHERE ai_summary IS NOT NULL AND ai_summary != '';

-- Index for referenced titles array
CREATE INDEX IF NOT EXISTS idx_content_referenced_titles_gin 
ON content USING GIN (referenced_titles);

-- Partial index for recent content (last 30 days) - most frequently accessed
CREATE INDEX IF NOT EXISTS idx_content_recent 
ON content (creator_id, published_at DESC) 
WHERE published_at > (NOW() - INTERVAL '30 days');

-- Index for user_creators table (for feed queries)
CREATE INDEX IF NOT EXISTS idx_user_creators_user_id 
ON user_creators (user_id);

-- Index for creators table trust_score (for relevance scoring)
CREATE INDEX IF NOT EXISTS idx_creators_trust_score 
ON creators (trust_score DESC);

-- Analyze tables to update statistics
ANALYZE content;
ANALYZE creators;
ANALYZE user_creators;

-- Add comments for documentation
COMMENT ON INDEX idx_content_creator_published IS 'Primary index for feed queries by creator and recency';
COMMENT ON INDEX idx_content_tags_gin IS 'GIN index for efficient tag/genre filtering';
COMMENT ON INDEX idx_content_recent IS 'Partial index for recent content optimization';

-- Add engagement tracking for content items
-- This will help improve the relevance scoring algorithm

-- Add engagement columns to content table
ALTER TABLE public.content 
ADD COLUMN IF NOT EXISTS view_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS engagement_score DECIMAL(5,2) DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS last_viewed_at TIMESTAMP WITH TIME ZONE;

-- Create content interactions table for detailed tracking
CREATE TABLE IF NOT EXISTS public.content_interactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    content_id UUID REFERENCES public.content(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    interaction_type VARCHAR(50) NOT NULL CHECK (interaction_type IN ('view', 'click', 'share', 'bookmark', 'like', 'time_spent')),
    interaction_value DECIMAL(10,2) DEFAULT 1.0, -- For time_spent, this would be seconds
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON>reate indexes for performance
CREATE INDEX IF NOT EXISTS idx_content_view_count ON public.content(view_count DESC);
CREATE INDEX IF NOT EXISTS idx_content_engagement_score ON public.content(engagement_score DESC);
CREATE INDEX IF NOT EXISTS idx_content_last_viewed ON public.content(last_viewed_at DESC);
CREATE INDEX IF NOT EXISTS idx_content_interactions_content_id ON public.content_interactions(content_id);
CREATE INDEX IF NOT EXISTS idx_content_interactions_user_id ON public.content_interactions(user_id);
CREATE INDEX IF NOT EXISTS idx_content_interactions_type ON public.content_interactions(interaction_type);
CREATE INDEX IF NOT EXISTS idx_content_interactions_created_at ON public.content_interactions(created_at DESC);

-- Create function to update content engagement metrics
CREATE OR REPLACE FUNCTION update_content_engagement()
RETURNS TRIGGER AS $$
BEGIN
    -- Update view count and last viewed time for view interactions
    IF NEW.interaction_type = 'view' THEN
        UPDATE public.content 
        SET 
            view_count = view_count + 1,
            last_viewed_at = NEW.created_at
        WHERE id = NEW.content_id;
    END IF;
    
    -- Calculate and update engagement score
    -- This is a simple algorithm that can be enhanced later
    UPDATE public.content 
    SET engagement_score = (
        SELECT COALESCE(
            (COUNT(CASE WHEN interaction_type = 'view' THEN 1 END) * 1.0) +
            (COUNT(CASE WHEN interaction_type = 'click' THEN 1 END) * 2.0) +
            (COUNT(CASE WHEN interaction_type = 'share' THEN 1 END) * 5.0) +
            (COUNT(CASE WHEN interaction_type = 'bookmark' THEN 1 END) * 3.0) +
            (COUNT(CASE WHEN interaction_type = 'like' THEN 1 END) * 2.0) +
            (SUM(CASE WHEN interaction_type = 'time_spent' THEN interaction_value ELSE 0 END) / 60.0), -- Convert seconds to minutes
            0.0
        )
        FROM public.content_interactions 
        WHERE content_id = NEW.content_id
        AND created_at > NOW() - INTERVAL '30 days' -- Only consider last 30 days
    )
    WHERE id = NEW.content_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update engagement metrics
DROP TRIGGER IF EXISTS trigger_update_content_engagement ON public.content_interactions;
CREATE TRIGGER trigger_update_content_engagement
    AFTER INSERT ON public.content_interactions
    FOR EACH ROW
    EXECUTE FUNCTION update_content_engagement();

-- Create materialized view for trending content (refreshed periodically)
CREATE MATERIALIZED VIEW IF NOT EXISTS trending_content AS
SELECT 
    c.id,
    c.title,
    c.creator_id,
    c.content_type,
    c.published_at,
    c.view_count,
    c.engagement_score,
    -- Calculate trending score based on recent engagement
    (
        c.engagement_score * 
        EXP(-EXTRACT(EPOCH FROM (NOW() - c.published_at)) / (7 * 24 * 3600)) -- Decay over 7 days
    ) as trending_score,
    -- Calculate velocity (engagement per day since published)
    CASE 
        WHEN EXTRACT(EPOCH FROM (NOW() - c.published_at)) > 0 
        THEN c.engagement_score / (EXTRACT(EPOCH FROM (NOW() - c.published_at)) / (24 * 3600))
        ELSE 0 
    END as engagement_velocity
FROM public.content c
WHERE c.published_at > NOW() - INTERVAL '30 days' -- Only recent content
ORDER BY trending_score DESC;

-- Create index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_trending_content_id ON trending_content(id);
CREATE INDEX IF NOT EXISTS idx_trending_content_score ON trending_content(trending_score DESC);
CREATE INDEX IF NOT EXISTS idx_trending_content_velocity ON trending_content(engagement_velocity DESC);

-- Function to refresh trending content view
CREATE OR REPLACE FUNCTION refresh_trending_content()
RETURNS void AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY trending_content;
END;
$$ LANGUAGE plpgsql;

-- Add comments for documentation
COMMENT ON COLUMN public.content.view_count IS 'Total number of views for this content';
COMMENT ON COLUMN public.content.engagement_score IS 'Calculated engagement score based on user interactions';
COMMENT ON COLUMN public.content.last_viewed_at IS 'Timestamp of the most recent view';
COMMENT ON TABLE public.content_interactions IS 'Tracks user interactions with content for engagement analytics';
COMMENT ON MATERIALIZED VIEW trending_content IS 'Materialized view of trending content with calculated scores';

-- Enable RLS on content_interactions
ALTER TABLE public.content_interactions ENABLE ROW LEVEL SECURITY;

-- RLS policy for content_interactions (users can only see their own interactions)
CREATE POLICY "Users can view their own content interactions" ON public.content_interactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own content interactions" ON public.content_interactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

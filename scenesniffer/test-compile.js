// Simple test to check if the project compiles
console.log('Testing basic compilation...')

// Test if we can import basic modules
try {
  const fs = require('fs')
  const path = require('path')
  
  // Check if key files exist
  const srcPath = path.join(__dirname, 'src')
  const appPath = path.join(srcPath, 'app')
  
  console.log('✅ Basic Node.js modules work')
  console.log('✅ src directory exists:', fs.existsSync(srcPath))
  console.log('✅ app directory exists:', fs.existsSync(appPath))
  
  // Check package.json
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  console.log('✅ Package.json loaded:', packageJson.name)
  
  console.log('🎉 Basic compilation test passed!')
  
} catch (error) {
  console.error('❌ Compilation test failed:', error.message)
  process.exit(1)
}

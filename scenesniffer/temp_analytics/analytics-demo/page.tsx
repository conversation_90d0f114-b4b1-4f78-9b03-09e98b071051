'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { BundleAnalyticsDashboard } from '@/components/analytics/BundleAnalyticsDashboard'
import { useBundleAnalytics, useTrendingTopics } from '@/hooks/useBundleAnalytics'
import { Database, BarChart3, TrendingUp, Package, Loader2, CheckCircle, XCircle } from 'lucide-react'

export default function AnalyticsDemo() {
  const [setupStatus, setSetupStatus] = useState<'checking' | 'ready' | 'needs_setup'>('checking')
  const [selectedBundleId, setSelectedBundleId] = useState<string>('')
  const [availableBundles, setAvailableBundles] = useState<any[]>([])
  const [isLoadingBundles, setIsLoadingBundles] = useState(false)
  const { trackInteraction } = useBundleAnalytics()
  const { trends, refetch: refetchTrends } = useTrendingTopics()

  // Check if analytics tables are set up
  useEffect(() => {
    checkAnalyticsSetup()
    loadAvailableBundles()
  }, [])

  const checkAnalyticsSetup = async () => {
    try {
      const response = await fetch('/api/setup-analytics-tables', {
        method: 'POST'
      })
      const data = await response.json()
      
      if (data.success) {
        setSetupStatus('ready')
      } else {
        setSetupStatus('needs_setup')
      }
    } catch (error) {
      console.error('Error checking analytics setup:', error)
      setSetupStatus('needs_setup')
    }
  }

  const loadAvailableBundles = async () => {
    try {
      setIsLoadingBundles(true)
      const response = await fetch('/api/bundles?limit=10')
      const data = await response.json()
      
      if (data.success && data.bundles) {
        setAvailableBundles(data.bundles)
        if (data.bundles.length > 0) {
          setSelectedBundleId(data.bundles[0].id)
        }
      }
    } catch (error) {
      console.error('Error loading bundles:', error)
    } finally {
      setIsLoadingBundles(false)
    }
  }

  const createTestTrendingTopic = async () => {
    try {
      const testTopics = [
        {
          topic_name: 'AI Content Creation',
          topic_keywords: ['ai', 'artificial intelligence', 'content', 'automation'],
          trend_score: 8.5,
          source: 'ai_analysis'
        },
        {
          topic_name: 'Sustainable Living',
          topic_keywords: ['sustainability', 'eco-friendly', 'green', 'environment'],
          trend_score: 7.2,
          source: 'social'
        },
        {
          topic_name: 'Remote Work Tips',
          topic_keywords: ['remote work', 'productivity', 'home office', 'digital nomad'],
          trend_score: 6.8,
          source: 'user_behavior'
        }
      ]

      for (const topic of testTopics) {
        await fetch('/api/trends', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(topic)
        })
      }

      // Refresh trends
      refetchTrends()
    } catch (error) {
      console.error('Error creating test trends:', error)
    }
  }

  const simulateUserInteractions = async () => {
    if (!selectedBundleId) return

    const interactions = [
      { type: 'view', data: { source: 'demo' } },
      { type: 'creator_click', data: { creator_id: 'demo_creator_1' } },
      { type: 'creator_click', data: { creator_id: 'demo_creator_2' } },
      { type: 'rate', data: { rating: 4 } },
      { type: 'add', data: { source: 'demo' } }
    ]

    for (const interaction of interactions) {
      await trackInteraction(selectedBundleId, interaction.type as any, interaction.data)
      // Small delay between interactions
      await new Promise(resolve => setTimeout(resolve, 500))
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-4">
      <div className="max-w-7xl mx-auto py-8">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">Bundle Analytics Demo</h1>
          <p className="text-xl text-gray-300">Test the new analytics and metrics system</p>
        </div>

        <div className="grid gap-6">
          {/* Setup Section */}
          <Card className="bg-white/10 border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Database className="h-5 w-5" />
                Analytics Setup
              </CardTitle>
              <CardDescription className="text-gray-300">
                Check and initialize the analytics database tables
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                {setupStatus === 'checking' && (
                  <>
                    <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
                    <span className="text-gray-300">Checking analytics setup...</span>
                  </>
                )}
                {setupStatus === 'ready' && (
                  <>
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span className="text-green-300">Analytics tables are ready!</span>
                  </>
                )}
                {setupStatus === 'needs_setup' && (
                  <>
                    <XCircle className="h-5 w-5 text-red-500" />
                    <span className="text-red-300">Analytics tables need to be created</span>
                  </>
                )}
              </div>
              
              {setupStatus === 'needs_setup' && (
                <div className="p-4 bg-yellow-900/30 rounded-lg border border-yellow-600/30">
                  <p className="text-yellow-200 mb-2">
                    <strong>Setup Required:</strong> Please run the analytics migration file:
                  </p>
                  <code className="text-sm bg-black/30 p-2 rounded block text-yellow-100">
                    supabase/migrations/20240806000001_create_bundle_analytics.sql
                  </code>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Bundle Selection */}
          {setupStatus === 'ready' && (
            <Card className="bg-white/10 border-white/20">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Bundle Selection
                </CardTitle>
                <CardDescription className="text-gray-300">
                  Choose a bundle to analyze
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {isLoadingBundles ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="text-gray-300">Loading bundles...</span>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Select Bundle:
                      </label>
                      <select
                        value={selectedBundleId}
                        onChange={(e) => setSelectedBundleId(e.target.value)}
                        className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
                      >
                        {availableBundles.map((bundle) => (
                          <option key={bundle.id} value={bundle.id}>
                            {bundle.title} ({bundle.creator_count} creators)
                          </option>
                        ))}
                      </select>
                    </div>

                    <div className="flex gap-2">
                      <Button
                        onClick={simulateUserInteractions}
                        disabled={!selectedBundleId}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <BarChart3 className="mr-2 h-4 w-4" />
                        Simulate Interactions
                      </Button>
                      
                      <Button
                        onClick={createTestTrendingTopic}
                        variant="outline"
                        className="text-white border-white/30 hover:bg-white/10"
                      >
                        <TrendingUp className="mr-2 h-4 w-4" />
                        Add Test Trends
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Analytics Dashboard */}
          {setupStatus === 'ready' && selectedBundleId && (
            <BundleAnalyticsDashboard 
              bundleId={selectedBundleId}
              showOverview={true}
            />
          )}

          {/* Instructions */}
          <Card className="bg-white/10 border-white/20">
            <CardHeader>
              <CardTitle className="text-white">How to Use</CardTitle>
            </CardHeader>
            <CardContent className="text-gray-300 space-y-2">
              <p>1. <strong>Setup:</strong> Ensure analytics tables are created (run migration if needed)</p>
              <p>2. <strong>Select Bundle:</strong> Choose a bundle from the dropdown</p>
              <p>3. <strong>Simulate Interactions:</strong> Generate test user interactions</p>
              <p>4. <strong>View Analytics:</strong> Click "Calculate Metrics" to see performance data</p>
              <p>5. <strong>Test Trends:</strong> Add sample trending topics to see the system in action</p>
              <div className="mt-4 p-3 bg-purple-900/30 rounded">
                <p className="text-purple-200">
                  <strong>Key Features:</strong> Real-time analytics tracking, performance metrics calculation, 
                  trending topic detection, and comprehensive bundle insights!
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

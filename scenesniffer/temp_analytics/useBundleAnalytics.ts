import { useCallback, useEffect, useState } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'

export interface BundleMetrics {
  engagement_score: number
  creator_retention: number
  content_freshness: number
  user_satisfaction: number
  discovery_rate: number
}

export interface BundleAnalyticsData {
  bundle_id: string
  current_metrics: BundleMetrics | null
  performance_history: any[]
  period: {
    start: string
    end: string
    label: string
  }
}

export interface UseBundleAnalyticsReturn {
  // Data
  analytics: BundleAnalyticsData | null
  isLoading: boolean
  error: string | null

  // Actions
  trackInteraction: (
    bundleId: string,
    interactionType: 'view' | 'add' | 'remove' | 'rate' | 'share' | 'creator_click',
    data?: any
  ) => Promise<void>
  
  fetchAnalytics: (bundleId: string, period?: string, calculate?: boolean) => Promise<void>
  calculateMetrics: (bundleId: string, period?: string) => Promise<BundleMetrics | null>
  
  // Convenience methods
  trackView: (bundleId: string) => Promise<void>
  trackCreatorClick: (bundleId: string, creatorId: string) => Promise<void>
  trackBundleAdd: (bundleId: string) => Promise<void>
  trackBundleRemove: (bundleId: string) => Promise<void>
  trackRating: (bundleId: string, rating: number) => Promise<void>
}

export function useBundleAnalytics(): UseBundleAnalyticsReturn {
  const supabase = createClientComponentClient()
  const [user, setUser] = useState<any>(null)
  const [analytics, setAnalytics] = useState<BundleAnalyticsData | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Get user from Supabase
  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
    }
    getUser()
  }, [supabase])

  // Generate session ID for tracking
  const [sessionId] = useState(() => {
    if (typeof window !== 'undefined') {
      return sessionStorage.getItem('analytics_session_id') || 
             (() => {
               const id = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
               sessionStorage.setItem('analytics_session_id', id)
               return id
             })()
    }
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  })

  /**
   * Track a user interaction with a bundle
   */
  const trackInteraction = useCallback(async (
    bundleId: string,
    interactionType: 'view' | 'add' | 'remove' | 'rate' | 'share' | 'creator_click',
    data?: any
  ) => {
    try {
      const response = await fetch('/api/bundles/analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          bundle_id: bundleId,
          user_id: user?.id,
          interaction_type: interactionType,
          interaction_data: data,
          session_id: sessionId,
          user_agent: typeof window !== 'undefined' ? navigator.userAgent : undefined,
          referrer: typeof window !== 'undefined' ? document.referrer : undefined
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error('Failed to track interaction:', errorData.error)
      }
    } catch (err) {
      console.error('Error tracking interaction:', err)
    }
  }, [user?.id, sessionId])

  /**
   * Fetch analytics data for a bundle
   */
  const fetchAnalytics = useCallback(async (
    bundleId: string, 
    period: string = '7d', 
    calculate: boolean = false
  ) => {
    try {
      setIsLoading(true)
      setError(null)

      const params = new URLSearchParams({
        period,
        calculate: calculate.toString()
      })

      const response = await fetch(`/api/bundles/${bundleId}/analytics?${params}`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch analytics')
      }

      const result = await response.json()
      setAnalytics(result.data)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('Error fetching analytics:', err)
    } finally {
      setIsLoading(false)
    }
  }, [])

  /**
   * Calculate metrics for a bundle
   */
  const calculateMetrics = useCallback(async (
    bundleId: string, 
    period: string = '7d'
  ): Promise<BundleMetrics | null> => {
    try {
      const response = await fetch(`/api/bundles/${bundleId}/analytics`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          period,
          store: true
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to calculate metrics')
      }

      const result = await response.json()
      return result.data.metrics

    } catch (err) {
      console.error('Error calculating metrics:', err)
      return null
    }
  }, [])

  // Convenience methods
  const trackView = useCallback((bundleId: string) => 
    trackInteraction(bundleId, 'view'), [trackInteraction])

  const trackCreatorClick = useCallback((bundleId: string, creatorId: string) => 
    trackInteraction(bundleId, 'creator_click', { creator_id: creatorId }), [trackInteraction])

  const trackBundleAdd = useCallback((bundleId: string) => 
    trackInteraction(bundleId, 'add'), [trackInteraction])

  const trackBundleRemove = useCallback((bundleId: string) => 
    trackInteraction(bundleId, 'remove'), [trackInteraction])

  const trackRating = useCallback((bundleId: string, rating: number) => 
    trackInteraction(bundleId, 'rate', { rating }), [trackInteraction])

  return {
    // Data
    analytics,
    isLoading,
    error,

    // Actions
    trackInteraction,
    fetchAnalytics,
    calculateMetrics,

    // Convenience methods
    trackView,
    trackCreatorClick,
    trackBundleAdd,
    trackBundleRemove,
    trackRating
  }
}

/**
 * Hook for automatically tracking bundle views
 */
export function useBundleViewTracking(bundleId: string | null) {
  const supabase = createClientComponentClient()
  const [user, setUser] = useState<any>(null)
  const [sessionId] = useState(() => {
    if (typeof window !== 'undefined') {
      return sessionStorage.getItem('analytics_session_id') ||
             (() => {
               const id = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
               sessionStorage.setItem('analytics_session_id', id)
               return id
             })()
    }
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  })

  // Get user from Supabase
  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
    }
    getUser()
  }, [supabase])

  const trackView = useCallback(async (bundleId: string) => {
    try {
      await fetch('/api/bundles/analytics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          bundle_id: bundleId,
          user_id: user?.id,
          interaction_type: 'view',
          interaction_data: { source: 'auto_track' },
          session_id: sessionId,
          user_agent: typeof window !== 'undefined' ? navigator.userAgent : undefined,
          referrer: typeof window !== 'undefined' ? document.referrer : undefined
        })
      })
    } catch (err) {
      console.error('Error tracking view:', err)
    }
  }, [user?.id, sessionId])

  useEffect(() => {
    if (bundleId) {
      // Track view after a short delay to ensure the user actually viewed the bundle
      const timer = setTimeout(() => {
        trackView(bundleId)
      }, 1000)

      return () => clearTimeout(timer)
    }
  }, [bundleId, trackView])
}

/**
 * Hook for fetching trending topics
 */
export function useTrendingTopics(limit: number = 20) {
  const [trends, setTrends] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchTrends = useCallback(async (source?: string) => {
    try {
      setIsLoading(true)
      setError(null)

      const params = new URLSearchParams({
        limit: limit.toString()
      })

      if (source) {
        params.append('source', source)
      }

      const response = await fetch(`/api/trends?${params}`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch trends')
      }

      const result = await response.json()
      setTrends(result.data)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('Error fetching trends:', err)
    } finally {
      setIsLoading(false)
    }
  }, [limit])

  useEffect(() => {
    fetchTrends()
  }, [fetchTrends])

  return {
    trends,
    isLoading,
    error,
    refetch: fetchTrends
  }
}

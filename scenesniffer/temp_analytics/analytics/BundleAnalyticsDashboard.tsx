'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Clock, 
  Star, 
  Eye,
  RefreshCw,
  Calendar,
  Activity
} from 'lucide-react'
import { useBundleAnalytics, useTrendingTopics } from '@/hooks/useBundleAnalytics'

interface BundleAnalyticsDashboardProps {
  bundleId?: string
  showOverview?: boolean
  className?: string
}

export function BundleAnalyticsDashboard({ 
  bundleId, 
  showOverview = true,
  className = '' 
}: BundleAnalyticsDashboardProps) {
  const { analytics, isLoading, fetchAnalytics, calculateMetrics } = useBundleAnalytics()
  const { trends, isLoading: trendsLoading } = useTrendingTopics(10)
  const [calculating, setCalculating] = useState(false)

  useEffect(() => {
    if (bundleId) {
      fetchAnalytics(bundleId, '7d', true)
    }
  }, [bundleId, fetchAnalytics])

  const handleCalculateMetrics = async () => {
    if (!bundleId) return
    
    setCalculating(true)
    try {
      await calculateMetrics(bundleId, '7d')
      await fetchAnalytics(bundleId, '7d', true)
    } finally {
      setCalculating(false)
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 8) return 'text-green-400'
    if (score >= 6) return 'text-yellow-400'
    if (score >= 4) return 'text-orange-400'
    return 'text-red-400'
  }

  const getScoreBg = (score: number) => {
    if (score >= 8) return 'bg-green-600/20'
    if (score >= 6) return 'bg-yellow-600/20'
    if (score >= 4) return 'bg-orange-600/20'
    return 'bg-red-600/20'
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Bundle-specific Analytics */}
      {bundleId && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-white">Bundle Analytics</h2>
            <Button
              onClick={handleCalculateMetrics}
              disabled={calculating || isLoading}
              variant="outline"
              className="text-white border-white/30 hover:bg-white/10"
            >
              {calculating ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Calculating...
                </>
              ) : (
                <>
                  <BarChart3 className="mr-2 h-4 w-4" />
                  Calculate Metrics
                </>
              )}
            </Button>
          </div>

          {isLoading ? (
            <Card className="bg-white/10 border-white/20">
              <CardContent className="p-8 text-center">
                <RefreshCw className="h-8 w-8 animate-spin text-purple-400 mx-auto mb-4" />
                <p className="text-gray-300">Loading analytics...</p>
              </CardContent>
            </Card>
          ) : analytics?.current_metrics ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              {/* Engagement Score */}
              <Card className="bg-white/10 border-white/20">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-300 flex items-center gap-2">
                    <Activity className="h-4 w-4" />
                    Engagement
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="text-2xl font-bold text-white mb-2">
                    {analytics.current_metrics.engagement_score.toFixed(1)}
                  </div>
                  <Progress 
                    value={analytics.current_metrics.engagement_score * 10} 
                    className="h-2"
                  />
                  <p className="text-xs text-gray-400 mt-2">
                    Interactions per view
                  </p>
                </CardContent>
              </Card>

              {/* Creator Retention */}
              <Card className="bg-white/10 border-white/20">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-300 flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Retention
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="text-2xl font-bold text-white mb-2">
                    {analytics.current_metrics.creator_retention.toFixed(1)}
                  </div>
                  <Progress 
                    value={analytics.current_metrics.creator_retention * 10} 
                    className="h-2"
                  />
                  <p className="text-xs text-gray-400 mt-2">
                    Active creators
                  </p>
                </CardContent>
              </Card>

              {/* Content Freshness */}
              <Card className="bg-white/10 border-white/20">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-300 flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Freshness
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="text-2xl font-bold text-white mb-2">
                    {analytics.current_metrics.content_freshness.toFixed(1)}
                  </div>
                  <Progress 
                    value={analytics.current_metrics.content_freshness * 10} 
                    className="h-2"
                  />
                  <p className="text-xs text-gray-400 mt-2">
                    Recent content
                  </p>
                </CardContent>
              </Card>

              {/* User Satisfaction */}
              <Card className="bg-white/10 border-white/20">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-300 flex items-center gap-2">
                    <Star className="h-4 w-4" />
                    Satisfaction
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="text-2xl font-bold text-white mb-2">
                    {analytics.current_metrics.user_satisfaction.toFixed(1)}
                  </div>
                  <Progress 
                    value={analytics.current_metrics.user_satisfaction * 10} 
                    className="h-2"
                  />
                  <p className="text-xs text-gray-400 mt-2">
                    User ratings
                  </p>
                </CardContent>
              </Card>

              {/* Discovery Rate */}
              <Card className="bg-white/10 border-white/20">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm font-medium text-gray-300 flex items-center gap-2">
                    <Eye className="h-4 w-4" />
                    Discovery
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="text-2xl font-bold text-white mb-2">
                    {analytics.current_metrics.discovery_rate.toFixed(1)}
                  </div>
                  <Progress 
                    value={analytics.current_metrics.discovery_rate * 10} 
                    className="h-2"
                  />
                  <p className="text-xs text-gray-400 mt-2">
                    Creator clicks
                  </p>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card className="bg-white/10 border-white/20">
              <CardContent className="p-8 text-center">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-semibold text-white mb-2">No Analytics Data</h3>
                <p className="text-gray-300 mb-4">
                  Click "Calculate Metrics" to generate analytics for this bundle.
                </p>
                <Button
                  onClick={handleCalculateMetrics}
                  disabled={calculating}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  {calculating ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Calculating...
                    </>
                  ) : (
                    <>
                      <BarChart3 className="mr-2 h-4 w-4" />
                      Calculate Metrics
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Trending Topics Overview */}
      {showOverview && (
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-white">Trending Topics</h2>
          
          {trendsLoading ? (
            <Card className="bg-white/10 border-white/20">
              <CardContent className="p-8 text-center">
                <RefreshCw className="h-8 w-8 animate-spin text-purple-400 mx-auto mb-4" />
                <p className="text-gray-300">Loading trending topics...</p>
              </CardContent>
            </Card>
          ) : trends.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {trends.slice(0, 6).map((trend) => (
                <Card key={trend.id} className="bg-white/10 border-white/20">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg text-white flex items-center justify-between">
                      <span className="truncate">{trend.topic_name}</span>
                      <Badge className={`${getScoreBg(trend.trend_score)} ${getScoreColor(trend.trend_score)}`}>
                        {trend.trend_score.toFixed(1)}
                      </Badge>
                    </CardTitle>
                    <CardDescription className="text-gray-300">
                      Source: {trend.source.replace('_', ' ')}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="flex items-center gap-2 text-sm text-gray-400">
                      <TrendingUp className="h-4 w-4" />
                      <span>Trending now</span>
                    </div>
                    {trend.topic_keywords && trend.topic_keywords.length > 0 && (
                      <div className="flex flex-wrap gap-1 mt-2">
                        {trend.topic_keywords.slice(0, 3).map((keyword: string) => (
                          <Badge 
                            key={keyword}
                            className="text-xs bg-gray-600/20 text-gray-400"
                          >
                            {keyword}
                          </Badge>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <Card className="bg-white/10 border-white/20">
              <CardContent className="p-8 text-center">
                <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-semibold text-white mb-2">No Trending Topics</h3>
                <p className="text-gray-300">
                  No trending topics are currently available.
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  )
}

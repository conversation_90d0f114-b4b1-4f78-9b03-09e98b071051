import { createServiceRoleClient } from '@/lib/supabase-server'

export interface BundleMetrics {
  engagement_score: number      // User interaction with bundle (0-10)
  creator_retention: number     // How many creators stay relevant (0-10)
  content_freshness: number     // How recent the content is (0-10)
  user_satisfaction: number     // User ratings and feedback (0-10)
  discovery_rate: number        // How often users find new content (0-10)
}

export interface BundlePerformanceData {
  bundle_id: string
  metrics: BundleMetrics
  raw_data: {
    total_views: number
    total_interactions: number
    unique_users: number
    creators_added: number
    creators_removed: number
    user_ratings_count: number
    user_ratings_sum: number
  }
  measurement_period: {
    start: Date
    end: Date
  }
}

export interface UserInteraction {
  bundle_id: string
  user_id?: string
  interaction_type: 'view' | 'add' | 'remove' | 'rate' | 'share' | 'creator_click'
  interaction_data?: any
  session_id?: string
  user_agent?: string
  referrer?: string
}

export interface TrendingTopic {
  topic_name: string
  topic_keywords: string[]
  trend_score: number
  source: 'social' | 'news' | 'platform' | 'ai_analysis' | 'user_behavior'
  source_data?: any
  expires_at?: Date
}

export interface BundleLifecycleEvent {
  bundle_id: string
  event_type: 'created' | 'refreshed' | 'optimized' | 'retired' | 'reactivated' | 'quality_check'
  event_data?: any
  performance_score?: number
  trigger_reason?: string
  created_by?: 'system' | 'admin' | 'ai'
}

export class BundleAnalyticsService {
  private supabase = createServiceRoleClient()

  /**
   * Record a user interaction with a bundle
   */
  async recordInteraction(interaction: UserInteraction): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('bundle_user_interactions')
        .insert({
          bundle_id: interaction.bundle_id,
          user_id: interaction.user_id,
          interaction_type: interaction.interaction_type,
          interaction_data: interaction.interaction_data || {},
          session_id: interaction.session_id,
          user_agent: interaction.user_agent,
          referrer: interaction.referrer
        })

      if (error) {
        console.error('Error recording bundle interaction:', error)
      }
    } catch (err) {
      console.error('Failed to record bundle interaction:', err)
    }
  }

  /**
   * Calculate bundle performance metrics for a given period
   */
  async calculateBundleMetrics(
    bundleId: string, 
    periodStart: Date, 
    periodEnd: Date
  ): Promise<BundleMetrics> {
    try {
      // Get interaction data for the period
      const { data: interactions, error: interactionError } = await this.supabase
        .from('bundle_user_interactions')
        .select('*')
        .eq('bundle_id', bundleId)
        .gte('created_at', periodStart.toISOString())
        .lte('created_at', periodEnd.toISOString())

      if (interactionError) {
        throw new Error(`Failed to fetch interactions: ${interactionError.message}`)
      }

      // Get bundle data
      const { data: bundle, error: bundleError } = await this.supabase
        .from('creator_bundles')
        .select(`
          *,
          bundle_creators (
            creator_id,
            created_at,
            creators (
              id,
              created_at,
              content (
                id,
                published_at
              )
            )
          )
        `)
        .eq('id', bundleId)
        .single()

      if (bundleError) {
        throw new Error(`Failed to fetch bundle: ${bundleError.message}`)
      }

      // Calculate metrics
      const metrics = this.computeMetrics(interactions || [], bundle, periodStart, periodEnd)
      return metrics

    } catch (error) {
      console.error('Error calculating bundle metrics:', error)
      return {
        engagement_score: 0,
        creator_retention: 0,
        content_freshness: 0,
        user_satisfaction: 0,
        discovery_rate: 0
      }
    }
  }

  /**
   * Store calculated performance metrics
   */
  async storePerformanceMetrics(data: BundlePerformanceData): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('bundle_performance_metrics')
        .insert({
          bundle_id: data.bundle_id,
          engagement_score: data.metrics.engagement_score,
          creator_retention: data.metrics.creator_retention,
          content_freshness: data.metrics.content_freshness,
          user_satisfaction: data.metrics.user_satisfaction,
          discovery_rate: data.metrics.discovery_rate,
          total_views: data.raw_data.total_views,
          total_interactions: data.raw_data.total_interactions,
          unique_users: data.raw_data.unique_users,
          creators_added: data.raw_data.creators_added,
          creators_removed: data.raw_data.creators_removed,
          user_ratings_count: data.raw_data.user_ratings_count,
          user_ratings_sum: data.raw_data.user_ratings_sum,
          measurement_period_start: data.measurement_period.start.toISOString(),
          measurement_period_end: data.measurement_period.end.toISOString()
        })

      if (error) {
        throw new Error(`Failed to store metrics: ${error.message}`)
      }
    } catch (err) {
      console.error('Failed to store performance metrics:', err)
      throw err
    }
  }

  /**
   * Record a bundle lifecycle event
   */
  async recordLifecycleEvent(event: BundleLifecycleEvent): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('bundle_lifecycle_events')
        .insert({
          bundle_id: event.bundle_id,
          event_type: event.event_type,
          event_data: event.event_data || {},
          performance_score: event.performance_score,
          trigger_reason: event.trigger_reason,
          created_by: event.created_by || 'system'
        })

      if (error) {
        throw new Error(`Failed to record lifecycle event: ${error.message}`)
      }
    } catch (err) {
      console.error('Failed to record lifecycle event:', err)
      throw err
    }
  }

  /**
   * Get performance metrics for a bundle
   */
  async getBundlePerformance(bundleId: string, limit: number = 10): Promise<any[]> {
    const { data, error } = await this.supabase
      .from('bundle_performance_metrics')
      .select('*')
      .eq('bundle_id', bundleId)
      .order('measured_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching bundle performance:', error)
      return []
    }

    return data || []
  }

  /**
   * Get trending topics
   */
  async getTrendingTopics(limit: number = 20): Promise<any[]> {
    const { data, error } = await this.supabase
      .from('trending_topics')
      .select('*')
      .eq('status', 'active')
      .order('trend_score', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Error fetching trending topics:', error)
      return []
    }

    return data || []
  }

  /**
   * Compute metrics from raw data
   */
  private computeMetrics(
    interactions: any[], 
    bundle: any, 
    periodStart: Date, 
    periodEnd: Date
  ): BundleMetrics {
    // Engagement Score (0-10): Based on interactions per view
    const views = interactions.filter(i => i.interaction_type === 'view').length
    const totalInteractions = interactions.length
    const engagementScore = views > 0 ? Math.min((totalInteractions / views) * 2, 10) : 0

    // Creator Retention (0-10): Based on how many creators are still active
    const creators = bundle.bundle_creators || []
    const activeCreators = creators.filter((bc: any) => {
      const recentContent = bc.creators?.content?.filter((c: any) => 
        new Date(c.published_at) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
      ) || []
      return recentContent.length > 0
    })
    const retentionScore = creators.length > 0 ? (activeCreators.length / creators.length) * 10 : 0

    // Content Freshness (0-10): Based on how recent the content is
    const allContent = creators.flatMap((bc: any) => bc.creators?.content || [])
    const recentContent = allContent.filter((c: any) => 
      new Date(c.published_at) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
    )
    const freshnessScore = allContent.length > 0 ? (recentContent.length / allContent.length) * 10 : 0

    // User Satisfaction (0-10): Based on ratings
    const ratings = interactions.filter(i => i.interaction_type === 'rate')
    const avgRating = ratings.length > 0 
      ? ratings.reduce((sum, r) => sum + (r.interaction_data?.rating || 5), 0) / ratings.length 
      : 5
    const satisfactionScore = (avgRating / 5) * 10 // Convert 1-5 scale to 0-10

    // Discovery Rate (0-10): Based on creator clicks and additions
    const creatorClicks = interactions.filter(i => i.interaction_type === 'creator_click').length
    const additions = interactions.filter(i => i.interaction_type === 'add').length
    const discoveryScore = views > 0 ? Math.min(((creatorClicks + additions * 2) / views) * 5, 10) : 0

    return {
      engagement_score: Math.round(engagementScore * 100) / 100,
      creator_retention: Math.round(retentionScore * 100) / 100,
      content_freshness: Math.round(freshnessScore * 100) / 100,
      user_satisfaction: Math.round(satisfactionScore * 100) / 100,
      discovery_rate: Math.round(discoveryScore * 100) / 100
    }
  }
}

const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

async function testFeed() {
  try {
    console.log('🧪 Testing feed functionality...')

    // Create Supabase client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    )

    // Test 1: Check if content table exists and has data
    console.log('📊 Checking content table...')
    const { data: contentData, error: contentError } = await supabase
      .from('content')
      .select('id, title, content_type, creator_id, published_at')
      .limit(5)

    if (contentError) {
      console.error('❌ Content table error:', contentError)
      return
    }

    console.log(`✅ Found ${contentData?.length || 0} content items`)
    if (contentData && contentData.length > 0) {
      console.log('📝 Sample content:', contentData[0])
    }

    // Test 2: Check if creators table exists
    console.log('👥 Checking creators table...')
    const { data: creatorsData, error: creatorsError } = await supabase
      .from('creators')
      .select('id, name, platform, trust_score')
      .limit(3)

    if (creatorsError) {
      console.error('❌ Creators table error:', creatorsError)
      return
    }

    console.log(`✅ Found ${creatorsData?.length || 0} creators`)

    // Test 3: Check if user_creators table exists
    console.log('🔗 Checking user_creators table...')
    const { data: userCreatorsData, error: userCreatorsError } = await supabase
      .from('user_creators')
      .select('user_id, creator_id')
      .limit(3)

    if (userCreatorsError) {
      console.error('❌ User creators table error:', userCreatorsError)
      return
    }

    console.log(`✅ Found ${userCreatorsData?.length || 0} user-creator relationships`)

    // Test 4: Test a basic feed query
    console.log('🔍 Testing basic feed query...')
    
    if (userCreatorsData && userCreatorsData.length > 0) {
      const creatorIds = [...new Set(userCreatorsData.map(uc => uc.creator_id))]
      
      const { data: feedData, error: feedError } = await supabase
        .from('content')
        .select(`
          id,
          title,
          description,
          content_type,
          platform_url,
          thumbnail_url,
          published_at,
          ai_summary,
          referenced_titles,
          tags,
          creator_id,
          creators!inner (
            id,
            name,
            platform,
            handle,
            avatar_url,
            trust_score,
            verified
          )
        `)
        .in('creator_id', creatorIds)
        .order('published_at', { ascending: false })
        .limit(5)

      if (feedError) {
        console.error('❌ Feed query error:', feedError)
        return
      }

      console.log(`✅ Feed query successful! Found ${feedData?.length || 0} items`)
      if (feedData && feedData.length > 0) {
        console.log('📝 Sample feed item:', {
          title: feedData[0].title,
          content_type: feedData[0].content_type,
          creator: feedData[0].creators?.name,
          published_at: feedData[0].published_at
        })
      }
    } else {
      console.log('⚠️ No user-creator relationships found, cannot test feed query')
    }

    // Test 5: Check for engagement columns
    console.log('📈 Checking for engagement columns...')
    try {
      const { data: engagementTest, error: engagementError } = await supabase
        .from('content')
        .select('view_count, engagement_score, last_viewed_at')
        .limit(1)

      if (engagementError) {
        console.log('⚠️ Engagement columns not available:', engagementError.message)
      } else {
        console.log('✅ Engagement columns are available!')
      }
    } catch (err) {
      console.log('⚠️ Engagement columns not available:', err.message)
    }

    console.log('🎉 Feed test completed!')

  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

testFeed()

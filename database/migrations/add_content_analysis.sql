-- Add content analysis column to store AI analysis results
ALTER TABLE content ADD COLUMN IF NOT EXISTS content_analysis JSONB;

-- Add index for better performance on content analysis queries
CREATE INDEX IF NOT EXISTS idx_content_analysis ON content USING GIN (content_analysis);

-- Add updated_at column if it doesn't exist
ALTER TABLE content ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- <PERSON>reate trigger to automatically update updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Drop trigger if exists and recreate
DROP TRIGGER IF EXISTS update_content_updated_at ON content;
CREATE TRIGGER update_content_updated_at
    BEFORE UPDATE ON content
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create table for tracking content-movie references (for future use)
CREATE TABLE IF NOT EXISTS content_movie_references (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    content_id UUID REFERENCES content(id) ON DELETE CASCADE,
    movie_title VARCHAR(255) NOT NULL,
    movie_slug VARCHAR(255) NOT NULL,
    tmdb_id INTEGER,
    timestamp_start VARCHAR(10), -- "2:15"
    timestamp_end VARCHAR(10),   -- "4:30"
    sentiment VARCHAR(20) CHECK (sentiment IN ('positive', 'negative', 'mixed', 'neutral')),
    confidence DECIMAL(3,2) CHECK (confidence >= 0 AND confidence <= 1),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(content_id, movie_slug)
);

-- Create indexes for content_movie_references
CREATE INDEX IF NOT EXISTS idx_content_movie_references_content_id ON content_movie_references(content_id);
CREATE INDEX IF NOT EXISTS idx_content_movie_references_movie_slug ON content_movie_references(movie_slug);
CREATE INDEX IF NOT EXISTS idx_content_movie_references_sentiment ON content_movie_references(sentiment);

-- Create table for caching movie analysis results
CREATE TABLE IF NOT EXISTS movie_analysis_cache (
    movie_slug VARCHAR(255) PRIMARY KEY,
    movie_title VARCHAR(255) NOT NULL,
    tmdb_id INTEGER,
    creator_consensus JSONB,
    trending_topics JSONB,
    total_content_count INTEGER DEFAULT 0,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for movie_analysis_cache
CREATE INDEX IF NOT EXISTS idx_movie_analysis_cache_tmdb_id ON movie_analysis_cache(tmdb_id);
CREATE INDEX IF NOT EXISTS idx_movie_analysis_cache_last_updated ON movie_analysis_cache(last_updated);

-- Add comments for documentation
COMMENT ON COLUMN content.content_analysis IS 'AI-generated analysis of content including type, sentiment, referenced movies, etc.';
COMMENT ON TABLE content_movie_references IS 'Tracks which movies/shows are referenced in which content items';
COMMENT ON TABLE movie_analysis_cache IS 'Cached analysis results for movies/shows based on creator content';
